:root {
    --background-color: #0D1117;
    --text-color: #eaeaea;
    --accent-color: #2ca6f7;
    --button-gradient-start: #00E0FF;
    --button-gradient-end: #FF006E;
    --button-gradient: linear-gradient(45deg, var(--button-gradient-start), var(--button-gradient-end));
    --delete-gradient-start: #ff3366;
    --delete-gradient-end: #ff5555;
    --delete-gradient: linear-gradient(90deg, var(--delete-gradient-start) 0%, var(--delete-gradient-end) 100%);
    /* Update header gradient to match home page */
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    --header-gradient: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --notification-bg: rgba(13, 17, 23, 0.8);
    --notification-hover: rgba(255, 255, 255, 0.1);
}

/* Add proper spacing for main content */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
}

main.container {
    flex: 1;
    padding: 2rem;
    margin-top: 100px; /* Adjust based on your navbar height */
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    box-sizing: border-box;
}

/* Footer styling */
footer {
    margin-top: auto;
    padding: 1rem;
    text-align: center;
    background: var(--notification-bg);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.notifications-container {
    background: var(--notification-bg);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 2rem;
}

.notification {
    background-color: var(--notification-hover);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease, opacity 0.5s ease;
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
}

.notification:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    transition: all 0.3s ease;
}

/* Update read notification styling */
.notification.read {
    opacity: 0.7;
    background-color: rgba(255, 255, 255, 0.05);
}

.notification.read .notification-title {
    color: #999;
}

.notification-icon {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin-right: 15px;
    border-radius: 50%;
}

.notification-content {
    flex: 1;
    min-width: 0; /* Prevents flex item from overflowing */
}

.notification-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.notification-message {
    font-size: 0.9em;
    color: #ccc;
}

.notification-time {
    font-size: 0.8em;
    color: #999;
    margin-top: 5px;
}

.notification-actions {
    display: flex;
    gap: 10px;
}

.notification-actions button {
    padding: 5px 10px;
    font-size: 0.8em;
    background: var(--delete-gradient);
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.notification-actions .mark-read-btn {
    background: linear-gradient(90deg, var(--button-gradient-start) 0%, var(--button-gradient-end) 100%);
    color: #ffffff;
    border: none;
    padding: 5px 10px;
    font-size: 0.8em;
    border-radius: 5px;
    cursor: pointer;
    font-family: "Plus Jakarta Sans", sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.notification-actions .mark-read-btn:hover {
    background: linear-gradient(90deg, var(--button-gradient-end) 0%, var(--button-gradient-start) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(44, 166, 247, 0.3);
}

.notification-actions .mark-read-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(44, 166, 247, 0.2);
}

.notification-actions button:hover {
    background: linear-gradient(90deg, var(--delete-gradient-end), var(--delete-gradient-start));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 51, 102, 0.3);
}

.notification-actions button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 51, 102, 0.2);
}

.notifications-header {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 20px;
}

.notifications-header .mark-all-btn {
    background: linear-gradient(90deg, var(--button-gradient-start) 0%, var(--button-gradient-end) 100%);
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-family: "Plus Jakarta Sans", sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.notifications-header .mark-all-btn:hover {
    background: linear-gradient(90deg, var(--button-gradient-end) 0%, var(--button-gradient-start) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(44, 166, 247, 0.3);
}

.notifications-header .mark-all-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(44, 166, 247, 0.2);
}

.notifications-header .clear-all-btn {
    background: var(--delete-gradient);
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-family: "Plus Jakarta Sans", sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.notifications-header .clear-all-btn:hover {
    background: linear-gradient(90deg, var(--delete-gradient-end), var(--delete-gradient-start));
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 51, 102, 0.3);
}

.notifications-header .clear-all-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 51, 102, 0.2);
}

.loader {
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid var(--accent-color);
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;
    margin: 20px auto;
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Add loading spinner */
.loading-spinner {
    text-align: center;
    padding: 2rem;
    color: var(--text-color);
}

/* Empty state styling */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-color);
}

.empty-state img {
    width: 120px;
    height: 120px;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.empty-state p {
    color: #999;
}

/* Add slide out animation */
@keyframes slideOut {
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Add fade in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Add button hover effects */
.notifications-header button {
    background: var(--button-gradient);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-family: "Plus Jakarta Sans", sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.notifications-header button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(44, 166, 247, 0.3);
}

/* Add responsive design improvements */
@media (max-width: 768px) {
    main.container {
        padding: 1rem;
        margin-top: 80px; /* Slightly smaller margin for mobile */
    }

    .notification {
        flex-direction: column;
        gap: 1rem;
    }

    .notification-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Add reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .notification,
    .notifications-header button,
    .notification-actions button {
        transition: none;
        animation: none;
    }
}
