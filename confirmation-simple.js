// Simple version of confirmation.js without modules
document.addEventListener('DOMContentLoaded', function() {
    console.log('confirmation-simple.js loaded');
    
    // Load confirmation details
    loadConfirmationDetails();
    
    // Handle receipt download
    const downloadButton = document.querySelector('.download-receipt');
    if (downloadButton) {
        downloadButton.addEventListener('click', generateReceipt);
    }
    
    async function loadConfirmationDetails() {
        // Get data from session storage
        const subscriptionId = sessionStorage.getItem('subscriptionId');
        const transactionId = sessionStorage.getItem('transactionId');
        const nextBillingDate = sessionStorage.getItem('nextBillingDate');
        const selectedPlanJson = sessionStorage.getItem('selectedPlan');
        
        if (!selectedPlanJson) {
            // If no subscription data, redirect to subscription page
            window.location.href = 'subscription.html';
            return;
        }
        
        try {
            // Parse selected plan
            const selectedPlan = JSON.parse(selectedPlanJson);
            
            // Update UI with subscription details
            const planNameElement = document.getElementById('planName');
            const billingCycleElement = document.getElementById('billingCycle');
            const nextBillingElement = document.getElementById('nextBilling');
            const amountElement = document.getElementById('amount');
            const userEmailElement = document.getElementById('userEmail');
            
            if (planNameElement) planNameElement.textContent = selectedPlan.name;
            
            if (billingCycleElement) {
                billingCycleElement.textContent = selectedPlan.interval === 'month' ? 'Monthly' : 'Yearly';
            }
            
            // Format next billing date
            if (nextBillingElement) {
                const nextBillingDateObj = new Date();
                nextBillingDateObj.setDate(nextBillingDateObj.getDate() + 30);
                nextBillingElement.textContent = nextBillingDateObj.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }
            
            if (amountElement) {
                amountElement.textContent = `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;
            }
            
            if (userEmailElement) {
                userEmailElement.textContent = '<EMAIL>';
            }
        } catch (error) {
            console.error('Error loading confirmation details:', error);
        }
    }
    
    function generateReceipt() {
        console.log('Generating receipt...');
        alert('Receipt download functionality would be implemented here.');
    }
    
    // Function to show loader
    function showLoader() {
        const loader = document.getElementById('loader');
        if (loader) {
            loader.style.display = 'flex';
        }
    }
    
    // Function to hide loader
    function hideLoader() {
        const loader = document.getElementById('loader');
        if (loader) {
            loader.style.display = 'none';
        }
    }
});
