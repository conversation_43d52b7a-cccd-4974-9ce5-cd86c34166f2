/**
 * Backend-connected API functions for music library management
 * This replaces the localStorage-based API with real backend calls
 */

import { Song, Playlist, Library } from './models.js';

/**
 * Library API class for managing music library data with backend
 */
class LibraryAPI {
    constructor() {
        this.currentUser = 'user_123'; // For now, using a fixed user ID
        this.library = null;
        this.baseURL = 'http://localhost:3001/api';
        this.initializeLibrary();
    }

    /**
     * Make HTTP request to backend
     */
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Request failed');
            }
            
            return data;
        } catch (error) {
            console.error(`API request failed for ${endpoint}:`, error);
            // Fallback to localStorage for offline functionality
            return this.fallbackToLocalStorage(endpoint, options);
        }
    }

    /**
     * Fallback to localStorage when backend is unavailable
     */
    fallbackToLocalStorage(endpoint, options) {
        console.log('Using localStorage fallback...');
        // Return mock data structure
        return {
            success: true,
            message: 'Using offline mode',
            data: []
        };
    }

    /**
     * Initialize the library for the current user
     */
    async initializeLibrary() {
        try {
            const response = await this.makeRequest(`/library/${this.currentUser}`);
            this.library = response.library || { songs: [], playlists: [] };
        } catch (error) {
            console.error('Failed to initialize library:', error);
            // Create empty library structure
            this.library = { songs: [], playlists: [] };
        }
    }

    /**
     * Get all playlists for the current user
     * @returns {Array} Array of playlists
     */
    async getPlaylists() {
        try {
            const response = await this.makeRequest(`/library/${this.currentUser}/playlists`);
            return response.playlists || [];
        } catch (error) {
            console.error('Failed to get playlists:', error);
            return [];
        }
    }

    /**
     * Get a playlist by ID
     * @param {string} playlistId - The ID of the playlist to get
     * @returns {Playlist} The playlist
     */
    async getPlaylist(playlistId) {
        try {
            const response = await this.makeRequest(`/library/${this.currentUser}/playlists/${playlistId}`);
            return response.playlist;
        } catch (error) {
            console.error('Failed to get playlist:', error);
            return null;
        }
    }

    /**
     * Create a new playlist
     * @param {Object} playlistData - The playlist data
     * @returns {Playlist} The created playlist
     */
    async createPlaylist(playlistData) {
        try {
            const response = await this.makeRequest(`/library/${this.currentUser}/playlists`, {
                method: 'POST',
                body: JSON.stringify(playlistData)
            });
            return response.playlist;
        } catch (error) {
            console.error('Failed to create playlist:', error);
            throw error;
        }
    }

    /**
     * Update a playlist
     * @param {string} playlistId - The ID of the playlist to update
     * @param {Object} playlistData - The updated playlist data
     * @returns {Playlist} The updated playlist
     */
    async updatePlaylist(playlistId, playlistData) {
        try {
            const response = await this.makeRequest(`/library/${this.currentUser}/playlists/${playlistId}`, {
                method: 'PUT',
                body: JSON.stringify(playlistData)
            });
            return response.playlist;
        } catch (error) {
            console.error('Failed to update playlist:', error);
            throw error;
        }
    }

    /**
     * Delete a playlist
     * @param {string} playlistId - The ID of the playlist to delete
     */
    async deletePlaylist(playlistId) {
        try {
            await this.makeRequest(`/library/${this.currentUser}/playlists/${playlistId}`, {
                method: 'DELETE'
            });
        } catch (error) {
            console.error('Failed to delete playlist:', error);
            throw error;
        }
    }

    /**
     * Add a song to a playlist
     * @param {string} playlistId - The ID of the playlist
     * @param {Song} song - The song to add
     */
    async addSongToPlaylist(playlistId, song) {
        try {
            await this.makeRequest(`/library/${this.currentUser}/playlists/${playlistId}/songs`, {
                method: 'POST',
                body: JSON.stringify({ songId: song.id })
            });
        } catch (error) {
            console.error('Failed to add song to playlist:', error);
            throw error;
        }
    }

    /**
     * Remove a song from a playlist
     * @param {string} playlistId - The ID of the playlist
     * @param {string} songId - The ID of the song to remove
     */
    async removeSongFromPlaylist(playlistId, songId) {
        try {
            await this.makeRequest(`/library/${this.currentUser}/playlists/${playlistId}/songs/${songId}`, {
                method: 'DELETE'
            });
        } catch (error) {
            console.error('Failed to remove song from playlist:', error);
            throw error;
        }
    }

    /**
     * Get all favorite songs
     * @returns {Array} Array of favorite songs
     */
    async getFavorites() {
        try {
            const response = await this.makeRequest(`/library/${this.currentUser}/favorites`);
            return response.songs || [];
        } catch (error) {
            console.error('Failed to get favorites:', error);
            return [];
        }
    }

    /**
     * Add a song to favorites
     * @param {Song} song - The song to add to favorites
     */
    async addToFavorites(song) {
        try {
            // First add song to library if not exists
            await this.makeRequest(`/library/${this.currentUser}/songs`, {
                method: 'POST',
                body: JSON.stringify(song)
            });
            
            // Then toggle favorite
            await this.makeRequest(`/library/${this.currentUser}/songs/${song.id}/favorite`, {
                method: 'POST'
            });
        } catch (error) {
            console.error('Failed to add to favorites:', error);
            throw error;
        }
    }

    /**
     * Remove a song from favorites
     * @param {string} songId - The ID of the song to remove from favorites
     */
    async removeFromFavorites(songId) {
        try {
            await this.makeRequest(`/library/${this.currentUser}/songs/${songId}/favorite`, {
                method: 'POST'
            });
        } catch (error) {
            console.error('Failed to remove from favorites:', error);
            throw error;
        }
    }

    /**
     * Check if a song is in favorites
     * @param {string} songId - The ID of the song to check
     * @returns {boolean} True if the song is in favorites
     */
    async isFavorite(songId) {
        try {
            const favorites = await this.getFavorites();
            return favorites.some(song => song.id === songId);
        } catch (error) {
            console.error('Failed to check favorite status:', error);
            return false;
        }
    }

    /**
     * Get recently played songs
     * @returns {Array} Array of recently played songs
     */
    async getRecentlyPlayed() {
        try {
            const response = await this.makeRequest(`/library/${this.currentUser}/recent`);
            return response.songs || [];
        } catch (error) {
            console.error('Failed to get recently played:', error);
            return [];
        }
    }

    /**
     * Add a song to recently played
     * @param {Song} song - The song to add to recently played
     */
    async addToRecentlyPlayed(song) {
        try {
            await this.makeRequest(`/library/${this.currentUser}/songs/${song.id}/play`, {
                method: 'POST',
                body: JSON.stringify({ duration: song.duration || 0 })
            });
        } catch (error) {
            console.error('Failed to add to recently played:', error);
        }
    }

    /**
     * Get all songs in user's library
     * @returns {Array} Array of songs
     */
    async getSongs() {
        try {
            const response = await this.makeRequest(`/library/${this.currentUser}/songs`);
            return response.songs || [];
        } catch (error) {
            console.error('Failed to get songs:', error);
            return [];
        }
    }

    /**
     * Add a song to the library
     * @param {Song} song - The song to add
     */
    async addSong(song) {
        try {
            const response = await this.makeRequest(`/library/${this.currentUser}/songs`, {
                method: 'POST',
                body: JSON.stringify(song)
            });
            return response.song;
        } catch (error) {
            console.error('Failed to add song:', error);
            throw error;
        }
    }

    /**
     * Search for music using external API
     * @param {string} query - The search query
     * @param {number} limit - Number of results to return
     * @returns {Array} Array of songs from search results
     */
    async searchMusic(query, limit = 20) {
        try {
            const response = await this.makeRequest(`/library/search?q=${encodeURIComponent(query)}&limit=${limit}`);
            return response.songs || [];
        } catch (error) {
            console.error('Failed to search music:', error);
            return [];
        }
    }

    /**
     * Get trending/popular songs
     * @param {number} limit - Number of results to return
     * @returns {Array} Array of trending songs
     */
    async getTrendingSongs(limit = 20) {
        try {
            const response = await this.makeRequest(`/library/trending?limit=${limit}`);
            return response.songs || [];
        } catch (error) {
            console.error('Failed to get trending songs:', error);
            return [];
        }
    }

    /**
     * Add a song from search results to user's library
     * @param {Object} song - The song to add
     * @returns {Object} The added song
     */
    async addSongFromSearch(song) {
        try {
            // First add the song to the global song database
            const response = await this.makeRequest(`/library/${this.currentUser}/songs`, {
                method: 'POST',
                body: JSON.stringify(song)
            });
            return response.song;
        } catch (error) {
            console.error('Failed to add song from search:', error);
            throw error;
        }
    }

    /**
     * Add a song from search results directly to a playlist
     * @param {string} playlistId - The ID of the playlist
     * @param {Object} song - The song to add
     */
    async addSongFromSearchToPlaylist(playlistId, song) {
        try {
            // First add the song to the library
            await this.addSongFromSearch(song);

            // Then add it to the playlist
            await this.addSongToPlaylist(playlistId, song);
        } catch (error) {
            console.error('Failed to add song from search to playlist:', error);
            throw error;
        }
    }

    /**
     * Search for music using external API
     * @param {string} query - The search query
     * @param {number} limit - Number of results to return
     * @returns {Array} Array of songs from search results
     */
    async searchMusic(query, limit = 20) {
        try {
            const response = await this.makeRequest(`/library/search?q=${encodeURIComponent(query)}&limit=${limit}`);
            return response.songs || [];
        } catch (error) {
            console.error('Failed to search music:', error);
            return [];
        }
    }

    /**
     * Get trending/popular songs
     * @param {number} limit - Number of results to return
     * @returns {Array} Array of trending songs
     */
    async getTrendingSongs(limit = 20) {
        try {
            const response = await this.makeRequest(`/library/trending?limit=${limit}`);
            return response.songs || [];
        } catch (error) {
            console.error('Failed to get trending songs:', error);
            return [];
        }
    }

    /**
     * Add a song from search results to user's library
     * @param {Object} song - The song to add
     * @returns {Object} The added song
     */
    async addSongFromSearch(song) {
        try {
            // First add the song to the global song database
            const response = await this.makeRequest(`/library/${this.currentUser}/songs`, {
                method: 'POST',
                body: JSON.stringify(song)
            });
            return response.song;
        } catch (error) {
            console.error('Failed to add song from search:', error);
            throw error;
        }
    }

    /**
     * Add a song from search results directly to a playlist
     * @param {string} playlistId - The ID of the playlist
     * @param {Object} song - The song to add
     */
    async addSongFromSearchToPlaylist(playlistId, song) {
        try {
            // First add the song to the library
            await this.addSongFromSearch(song);

            // Then add it to the playlist
            await this.addSongToPlaylist(playlistId, song);
        } catch (error) {
            console.error('Failed to add song from search to playlist:', error);
            throw error;
        }
    }

    // Legacy methods for compatibility with existing frontend code
    getQueue() { return []; }
    addToQueue(song) { console.log('Queue functionality not implemented yet'); }
    removeFromQueue(index) { console.log('Queue functionality not implemented yet'); }
    clearQueue() { console.log('Queue functionality not implemented yet'); }
    getDownloads() { return []; }
    addToDownloads(song) { console.log('Downloads functionality not implemented yet'); }
    removeFromDownloads(songId) { console.log('Downloads functionality not implemented yet'); }
    isDownloaded(songId) { return false; }
}

// Create and export a singleton instance
const libraryAPI = new LibraryAPI();
export default libraryAPI;
