export const API = {
    login: async (username, password) => {
        // This is a mock implementation for demonstration purposes
        // In a real application, you would send these credentials to your server
        console.log('Login attempt for user:', username);

        // Simulate API call with a delay
        return new Promise((resolve) => {
            setTimeout(() => {
                // For demo purposes, accept any login
                resolve({
                    success: true,
                    token: 'mock-jwt-token-' + Math.random().toString(36).substring(2),
                    user: {
                        id: 1,
                        username: username,
                        name: 'Demo User'
                    }
                });
            }, 1000);
        });
    },

    // Subscription and Payment API functions
    getSubscriptionPlans: async () => {
        // In a real implementation, this would fetch from your backend
        return [
            {
                id: 'free',
                name: 'Free Account',
                price: 0,
                interval: 'month',
                features: [
                    'Enjoy Banshee with occasional ads',
                    'Basic audio quality (128kbps)',
                    'Limited skips (6 per hour)',
                    'Mobile app access'
                ]
            },
            {
                id: 'premium',
                name: 'Premium',
                price: 2.99,
                interval: 'month',
                features: [
                    'Ad-free listening experience',
                    'High-quality audio (320kbps)',
                    'Unlimited skips',
                    'Offline mode',
                    'Cross-platform sync',
                    'Exclusive content access'
                ]
            },
            {
                id: 'artist',
                name: 'Artist Account',
                price: 4.99,
                interval: 'month',
                features: [
                    'All Premium features included',
                    'Upload unlimited tracks',
                    'Advanced analytics dashboard',
                    'Promotional tools',
                    'Direct fan engagement',
                    'Custom artist profile'
                ]
            }
        ];
    },

    subscribeToPlan: async (planId) => {
        // In a real implementation, this would create a subscription in your backend
        console.log(`Creating subscription for plan: ${planId}`);

        // Simulate API call with a delay
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    subscriptionId: 'sub_' + Math.random().toString(36).substring(2),
                    planId: planId,
                    nextStep: 'payment'
                });
            }, 800);
        });
    },

    processPayment: async (paymentDetails, subscriptionId) => {
        // In a real implementation, this would process payment through Stripe
        console.log('Processing payment for subscription:', subscriptionId);
        console.log('Payment details:', paymentDetails);

        // Simulate API call with a delay
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                // Simulate successful payment 90% of the time
                if (Math.random() < 0.9) {
                    resolve({
                        success: true,
                        transactionId: 'txn_' + Math.random().toString(36).substring(2),
                        subscriptionId: subscriptionId,
                        nextBillingDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
                    });
                } else {
                    // Simulate payment failure
                    reject({
                        success: false,
                        error: 'Payment processing failed',
                        errorCode: 'payment_failed',
                        message: 'Your card was declined. Please try another payment method.'
                    });
                }
            }, 1500);
        });
    },

    getSubscriptionDetails: async (subscriptionId) => {
        // In a real implementation, this would fetch subscription details from your backend
        console.log(`Fetching details for subscription: ${subscriptionId}`);

        // Simulate API call with a delay
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    id: subscriptionId,
                    status: 'active',
                    planId: 'premium',
                    planName: 'Premium',
                    price: 2.99,
                    interval: 'month',
                    currentPeriodStart: new Date().toISOString(),
                    currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                    cancelAtPeriodEnd: false
                });
            }, 600);
        });
    },

    cancelSubscription: async (subscriptionId) => {
        // In a real implementation, this would cancel the subscription in your backend
        console.log(`Cancelling subscription: ${subscriptionId}`);

        // Simulate API call with a delay
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    message: 'Subscription will be canceled at the end of the current billing period'
                });
            }, 800);
        });
    },

    getTrendingMusic: async () => {
        // Simulate API call
        return [
            { title: 'Album 1', image: 'imgs/album-01.png' },
            { title: 'Album 2', image: 'imgs/album-02.png' }
        ];
    },
    getFeaturedArtists: async () => {
        // Simulate API call
        return [
            { name: 'Artist 1', image: 'imgs/album-03.png' },
            { name: 'Artist 2', image: 'imgs/album-04.png' }
        ];
    },
    getNewReleases: async () => {
        // Simulate API call
        return [
            { title: 'Album 3', image: 'imgs/album-05.png' },
            { title: 'Album 4', image: 'imgs/album-02.png' }
        ];
    }
};

// Utility functions
export const utils = {
    showLoader: () => {
        // Implementation would show a loading spinner
        console.log('Loading...');
    },
    hideLoader: () => {
        // Implementation would hide the loading spinner
        console.log('Loading complete');
    }
};
