import { API, utils } from './api.js';

const cardTypes = {
    visa: /^4/,
    mastercard: /^5[1-5]/,
    amex: /^3[47]/,
    discover: /^6/
};

function detectCardType(number) {
    for (const [type, regex] of Object.entries(cardTypes)) {
        if (regex.test(number)) {
            return type;
        }
    }
    return 'generic';
}

function formatCardNumber(input) {
    let value = input.value.replace(/\D/g, '');
    let formattedValue = '';

    if (value.length > 0) {
        const cardType = detectCardType(value);
        const cardTypeIcon = input.parentElement.querySelector('.card-type-icon');
        if (cardTypeIcon) {
            cardTypeIcon.className = `card-type-icon fab fa-cc-${cardType}`;
        }

        // Format based on card type
        if (cardType === 'amex') {
            formattedValue = value.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3');
        } else {
            formattedValue = value.replace(/(\d{4})/g, '$1 ').trim();
        }
    }

    input.value = formattedValue;
}

function formatExpiryDate(input) {
    let value = input.value.replace(/\D/g, '');
    if (value.length >= 2) {
        const month = parseInt(value.substring(0, 2));
        if (month > 12) value = '12' + value.slice(2);
        value = value.replace(/(\d{2})(\d{0,2})/, '$1/$2');
    }
    input.value = value;
}

function validateCVC(input) {
    let value = input.value.replace(/\D/g, '');
    input.value = value.slice(0, 4);
}

// Initialize form validation and formatting
document.addEventListener('DOMContentLoaded', () => {
    // Load selected plan details
    loadSelectedPlan();

    const cardNumber = document.getElementById('card-number');
    const expiryDate = document.getElementById('expiry-date');
    const cvc = document.getElementById('cvc');

    if (cardNumber) cardNumber.addEventListener('input', () => formatCardNumber(cardNumber));
    if (expiryDate) expiryDate.addEventListener('input', () => formatExpiryDate(expiryDate));
    if (cvc) cvc.addEventListener('input', () => validateCVC(cvc));

    // Payment method selection
    const paymentMethods = document.querySelectorAll('.payment-method');
    paymentMethods.forEach(method => {
        method.addEventListener('click', () => {
            paymentMethods.forEach(m => m.classList.remove('active'));
            method.classList.add('active');
        });
    });

    // Initialize form submission
    const paymentForm = document.getElementById('paymentForm');
    if (paymentForm) {
        paymentForm.addEventListener('submit', handlePaymentSubmission);
    }

    // Restore form progress
    restoreProgress();
});

// Load selected plan details from session storage
function loadSelectedPlan() {
    // Get selected plan from session storage
    const selectedPlanJson = sessionStorage.getItem('selectedPlan');

    if (!selectedPlanJson) {
        // If no plan is selected, redirect back to subscription page
        window.location.href = 'subscription.html';
        return;
    }

    try {
        const selectedPlan = JSON.parse(selectedPlanJson);

        // Update UI with selected plan details
        const planNameElement = document.getElementById('selectedPlanName');
        const planPriceElement = document.getElementById('selectedPlanPrice');
        const subtotalElement = document.getElementById('subtotal');
        const totalElement = document.getElementById('total');
        const buttonPriceElement = document.querySelector('.button-price');

        if (planNameElement) planNameElement.textContent = selectedPlan.name;
        if (planPriceElement) planPriceElement.textContent = `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;
        if (subtotalElement) subtotalElement.textContent = `$${selectedPlan.price.toFixed(2)}`;
        if (totalElement) totalElement.textContent = `$${selectedPlan.price.toFixed(2)}`;
        if (buttonPriceElement) buttonPriceElement.textContent = `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;

    } catch (error) {
        console.error('Error parsing selected plan:', error);
        showError('There was an error loading your selected plan. Please try again.');
    }
}

// Handle payment form submission
async function handlePaymentSubmission(event) {
    event.preventDefault();

    // Get subscription ID from session storage
    const subscriptionId = sessionStorage.getItem('subscriptionId');
    if (!subscriptionId) {
        showError('Subscription information not found. Please try again.');
        return;
    }

    // Show loading state
    const submitButton = event.target.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.querySelector('.button-text').textContent;
    submitButton.disabled = true;
    submitButton.querySelector('.button-text').textContent = 'Processing...';

    try {
        // Collect payment details from form
        const paymentDetails = {
            cardName: document.getElementById('card-name').value,
            cardNumber: document.getElementById('card-number').value.replace(/\s/g, ''),
            expiryDate: document.getElementById('expiry-date').value,
            cvc: document.getElementById('cvc').value,
            billingAddress: {
                country: document.getElementById('country').value,
                zipCode: document.getElementById('zip').value
            }
        };

        // Process payment
        const response = await API.processPayment(paymentDetails, subscriptionId);

        if (response.success) {
            // Show success message
            showToast('Payment processed successfully!');

            // Redirect to confirmation page
            setTimeout(() => {
                window.location.href = 'confirmation.html';
            }, 1500);
        } else {
            // Show error message
            showError(response.message || 'Payment processing failed. Please try again.');
        }
    } catch (error) {
        console.error('Payment error:', error);
        showError(error.message || 'An error occurred while processing your payment. Please try again.');
    } finally {
        // Reset button state
        submitButton.disabled = false;
        submitButton.querySelector('.button-text').textContent = originalButtonText;
    }
}

// Show error message
function showError(message) {
    // Create error message element
    const errorElement = document.createElement('div');
    errorElement.className = 'payment-error';
    errorElement.textContent = message;

    // Add to page
    const paymentForm = document.getElementById('paymentForm');
    const existingError = document.querySelector('.payment-error');

    if (existingError) {
        existingError.remove();
    }

    paymentForm.prepend(errorElement);

    // Scroll to error
    errorElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

// Show toast message
function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast success';
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Debounce function to limit how often a function can be called
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

// Save form progress
const saveProgress = debounce(() => {
    const formData = {
        cardName: document.getElementById('card-name')?.value || '',
        // Don't save sensitive data
        lastUpdated: new Date().toISOString()
    };
    sessionStorage.setItem('paymentProgress', JSON.stringify(formData));
}, 1000);

// Restore form progress
function restoreProgress() {
    const savedData = sessionStorage.getItem('paymentProgress');
    if (savedData) {
        try {
            const formData = JSON.parse(savedData);
            const cardNameInput = document.getElementById('card-name');
            if (cardNameInput && formData.cardName) {
                cardNameInput.value = formData.cardName;
            }
        } catch (error) {
            console.error('Error restoring form progress:', error);
            // Clear invalid data
            sessionStorage.removeItem('paymentProgress');
        }
    }
}

// Add card type indicator when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const cardNumberInput = document.getElementById('card-number');
    if (cardNumberInput) {
        cardNumberInput.insertAdjacentHTML(
            'afterend',
            '<i class="card-type-icon fab fa-cc-generic"></i>'
        );
    }
});