const express = require('express');
const router = express.Router();
const Song = require('../models/Song');
const Playlist = require('../models/Playlist');
const Library = require('../models/Library');
const memoryStorage = require('../utils/memoryStorage');
const mongoose = require('mongoose');

// Utility function to generate unique IDs
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

// Check if MongoDB is connected
function isMongoConnected() {
    return mongoose.connection.readyState === 1;
}

// Get user's library
router.get('/:userId', async (req, res) => {
    try {
        const { userId } = req.params;

        if (!isMongoConnected()) {
            // Use memory storage
            let library = memoryStorage.getLibrary(userId);
            if (!library) {
                library = memoryStorage.createLibrary(userId);
            }

            return res.json({
                success: true,
                library: library
            });
        }

        let library = await Library.findByUser(userId);
        if (!library) {
            // Create new library for user
            library = new Library({ userId });
            await library.save();
        }

        res.json({
            success: true,
            library: library
        });
    } catch (error) {
        console.error('Error fetching library:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch library'
        });
    }
});

// Get all songs in user's library with full song details
router.get('/:userId/songs', async (req, res) => {
    try {
        const { userId } = req.params;

        if (!isMongoConnected()) {
            // Use memory storage
            const library = memoryStorage.getLibrary(userId);
            if (!library) {
                return res.json({
                    success: true,
                    songs: []
                });
            }

            // Get full song details for all songs in library
            const songsWithMetadata = library.songs.map(librarySong => {
                const song = memoryStorage.getSong(librarySong.songId);
                if (song) {
                    return {
                        ...song,
                        libraryMetadata: {
                            addedAt: librarySong.addedAt,
                            playCount: librarySong.playCount,
                            lastPlayedAt: librarySong.lastPlayedAt,
                            isFavorite: librarySong.isFavorite,
                            rating: librarySong.rating
                        }
                    };
                }
                return null;
            }).filter(Boolean);

            return res.json({
                success: true,
                songs: songsWithMetadata
            });
        }

        const library = await Library.findByUser(userId);
        if (!library) {
            return res.json({
                success: true,
                songs: []
            });
        }

        // Get full song details for all songs in library
        const songIds = library.songs.map(s => s.songId);
        const songs = await Song.find({ id: { $in: songIds } });

        // Merge library metadata with song details
        const songsWithMetadata = songs.map(song => {
            const libraryEntry = library.songs.find(s => s.songId === song.id);
            return {
                ...song.toObject(),
                libraryMetadata: {
                    addedAt: libraryEntry.addedAt,
                    playCount: libraryEntry.playCount,
                    lastPlayedAt: libraryEntry.lastPlayedAt,
                    isFavorite: libraryEntry.isFavorite,
                    rating: libraryEntry.rating
                }
            };
        });

        res.json({
            success: true,
            songs: songsWithMetadata
        });
    } catch (error) {
        console.error('Error fetching library songs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch library songs'
        });
    }
});

// Add song to library
router.post('/:userId/songs', async (req, res) => {
    try {
        const { userId } = req.params;
        const songData = req.body;

        if (!isMongoConnected()) {
            // Use memory storage
            let song = memoryStorage.getSong(songData.id);
            if (!song) {
                song = memoryStorage.addSong({
                    ...songData,
                    uploadedBy: userId
                });
            }

            // Add to user's library
            memoryStorage.addSongToLibrary(userId, songData.id);

            return res.json({
                success: true,
                song: song,
                message: 'Song added to library'
            });
        }

        // Create or update song in database
        let song = await Song.findOne({ id: songData.id });
        if (!song) {
            song = new Song({
                ...songData,
                uploadedBy: userId
            });
            await song.save();
        }

        // Add to user's library
        let library = await Library.findByUser(userId);
        if (!library) {
            library = new Library({ userId });
        }

        await library.addSong(songData.id);

        res.json({
            success: true,
            song: song,
            message: 'Song added to library'
        });
    } catch (error) {
        console.error('Error adding song to library:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to add song to library'
        });
    }
});

// Remove song from library
router.delete('/:userId/songs/:songId', async (req, res) => {
    try {
        const { userId, songId } = req.params;
        
        const library = await Library.findByUser(userId);
        if (!library) {
            return res.status(404).json({
                success: false,
                message: 'Library not found'
            });
        }
        
        await library.removeSong(songId);
        
        res.json({
            success: true,
            message: 'Song removed from library'
        });
    } catch (error) {
        console.error('Error removing song from library:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to remove song from library'
        });
    }
});

// Get all playlists in user's library
router.get('/:userId/playlists', async (req, res) => {
    try {
        const { userId } = req.params;

        if (!isMongoConnected()) {
            // Use memory storage
            const library = memoryStorage.getLibrary(userId);
            if (!library) {
                return res.json({
                    success: true,
                    playlists: []
                });
            }

            // Get full playlist details
            const playlistsWithMetadata = library.playlists.map(libraryPlaylist => {
                const playlist = memoryStorage.getPlaylist(libraryPlaylist.playlistId);
                if (playlist) {
                    return {
                        ...playlist,
                        libraryMetadata: {
                            addedAt: libraryPlaylist.addedAt,
                            isOwned: libraryPlaylist.isOwned,
                            isFollowed: libraryPlaylist.isFollowed
                        }
                    };
                }
                return null;
            }).filter(Boolean);

            return res.json({
                success: true,
                playlists: playlistsWithMetadata
            });
        }

        const library = await Library.findByUser(userId);
        if (!library) {
            return res.json({
                success: true,
                playlists: []
            });
        }

        // Get full playlist details
        const playlistIds = library.playlists.map(p => p.playlistId);
        const playlists = await Playlist.find({ id: { $in: playlistIds } });

        // Merge library metadata with playlist details
        const playlistsWithMetadata = playlists.map(playlist => {
            const libraryEntry = library.playlists.find(p => p.playlistId === playlist.id);
            return {
                ...playlist.toObject(),
                libraryMetadata: {
                    addedAt: libraryEntry.addedAt,
                    isOwned: libraryEntry.isOwned,
                    isFollowed: libraryEntry.isFollowed
                }
            };
        });

        res.json({
            success: true,
            playlists: playlistsWithMetadata
        });
    } catch (error) {
        console.error('Error fetching library playlists:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch library playlists'
        });
    }
});

// Create new playlist
router.post('/:userId/playlists', async (req, res) => {
    try {
        const { userId } = req.params;
        const playlistData = req.body;
        
        // Create playlist
        const playlist = new Playlist({
            id: playlistData.id || generateId(),
            name: playlistData.name,
            description: playlistData.description || '',
            coverUrl: playlistData.coverUrl || '',
            createdBy: userId,
            songs: []
        });
        
        await playlist.save();
        
        // Add to user's library
        let library = await Library.findByUser(userId);
        if (!library) {
            library = new Library({ userId });
        }
        
        await library.addPlaylist(playlist.id, true); // true = owned
        
        res.json({
            success: true,
            playlist: playlist,
            message: 'Playlist created successfully'
        });
    } catch (error) {
        console.error('Error creating playlist:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create playlist'
        });
    }
});

// Get specific playlist with songs
router.get('/:userId/playlists/:playlistId', async (req, res) => {
    try {
        const { playlistId } = req.params;
        
        const playlist = await Playlist.findOne({ id: playlistId });
        if (!playlist) {
            return res.status(404).json({
                success: false,
                message: 'Playlist not found'
            });
        }
        
        // Get full song details for playlist
        const songIds = playlist.songs.map(s => s.songId);
        const songs = await Song.find({ id: { $in: songIds } });
        
        // Merge playlist song metadata with song details
        const songsWithMetadata = songs.map(song => {
            const playlistEntry = playlist.songs.find(s => s.songId === song.id);
            return {
                ...song.toObject(),
                playlistMetadata: {
                    addedAt: playlistEntry.addedAt,
                    addedBy: playlistEntry.addedBy
                }
            };
        });
        
        res.json({
            success: true,
            playlist: {
                ...playlist.toObject(),
                songsDetails: songsWithMetadata
            }
        });
    } catch (error) {
        console.error('Error fetching playlist:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch playlist'
        });
    }
});

// Add song to playlist
router.post('/:userId/playlists/:playlistId/songs', async (req, res) => {
    try {
        const { userId, playlistId } = req.params;
        const { songId } = req.body;

        const playlist = await Playlist.findOne({ id: playlistId });
        if (!playlist) {
            return res.status(404).json({
                success: false,
                message: 'Playlist not found'
            });
        }

        await playlist.addSong(songId, userId);

        res.json({
            success: true,
            message: 'Song added to playlist'
        });
    } catch (error) {
        console.error('Error adding song to playlist:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Failed to add song to playlist'
        });
    }
});

// Remove song from playlist
router.delete('/:userId/playlists/:playlistId/songs/:songId', async (req, res) => {
    try {
        const { playlistId, songId } = req.params;

        const playlist = await Playlist.findOne({ id: playlistId });
        if (!playlist) {
            return res.status(404).json({
                success: false,
                message: 'Playlist not found'
            });
        }

        await playlist.removeSong(songId);

        res.json({
            success: true,
            message: 'Song removed from playlist'
        });
    } catch (error) {
        console.error('Error removing song from playlist:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to remove song from playlist'
        });
    }
});

// Update playlist
router.put('/:userId/playlists/:playlistId', async (req, res) => {
    try {
        const { playlistId } = req.params;
        const updateData = req.body;

        const playlist = await Playlist.findOne({ id: playlistId });
        if (!playlist) {
            return res.status(404).json({
                success: false,
                message: 'Playlist not found'
            });
        }

        // Update allowed fields
        if (updateData.name) playlist.name = updateData.name;
        if (updateData.description !== undefined) playlist.description = updateData.description;
        if (updateData.coverUrl !== undefined) playlist.coverUrl = updateData.coverUrl;
        if (updateData.isPublic !== undefined) playlist.isPublic = updateData.isPublic;

        await playlist.save();

        res.json({
            success: true,
            playlist: playlist,
            message: 'Playlist updated successfully'
        });
    } catch (error) {
        console.error('Error updating playlist:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update playlist'
        });
    }
});

// Delete playlist
router.delete('/:userId/playlists/:playlistId', async (req, res) => {
    try {
        const { userId, playlistId } = req.params;

        // Remove from database
        await Playlist.deleteOne({ id: playlistId });

        // Remove from user's library
        const library = await Library.findByUser(userId);
        if (library) {
            await library.removePlaylist(playlistId);
        }

        res.json({
            success: true,
            message: 'Playlist deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting playlist:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete playlist'
        });
    }
});

// Toggle favorite song
router.post('/:userId/songs/:songId/favorite', async (req, res) => {
    try {
        const { userId, songId } = req.params;

        if (!isMongoConnected()) {
            // Use memory storage
            const library = memoryStorage.toggleFavorite(userId, songId);
            if (!library) {
                return res.status(404).json({
                    success: false,
                    message: 'Library or song not found'
                });
            }

            return res.json({
                success: true,
                message: 'Favorite status updated'
            });
        }

        const library = await Library.findByUser(userId);
        if (!library) {
            return res.status(404).json({
                success: false,
                message: 'Library not found'
            });
        }

        await library.toggleFavorite(songId);

        res.json({
            success: true,
            message: 'Favorite status updated'
        });
    } catch (error) {
        console.error('Error toggling favorite:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'Failed to update favorite status'
        });
    }
});

// Record song play
router.post('/:userId/songs/:songId/play', async (req, res) => {
    try {
        const { userId, songId } = req.params;
        const { duration } = req.body;

        let library = await Library.findByUser(userId);
        if (!library) {
            library = new Library({ userId });
        }

        await library.recordPlay(songId, duration || 0);

        res.json({
            success: true,
            message: 'Play recorded'
        });
    } catch (error) {
        console.error('Error recording play:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to record play'
        });
    }
});

// Get user's favorite songs
router.get('/:userId/favorites', async (req, res) => {
    try {
        const { userId } = req.params;

        if (!isMongoConnected()) {
            // Use memory storage
            const library = memoryStorage.getLibrary(userId);
            if (!library) {
                return res.json({
                    success: true,
                    songs: []
                });
            }

            const favoriteSongs = library.songs
                .filter(librarySong => librarySong.isFavorite)
                .map(librarySong => memoryStorage.getSong(librarySong.songId))
                .filter(Boolean);

            return res.json({
                success: true,
                songs: favoriteSongs
            });
        }

        const library = await Library.findByUser(userId);
        if (!library) {
            return res.json({
                success: true,
                songs: []
            });
        }

        const favorites = library.getFavorites();
        const songIds = favorites.map(f => f.songId);
        const songs = await Song.find({ id: { $in: songIds } });

        res.json({
            success: true,
            songs: songs
        });
    } catch (error) {
        console.error('Error fetching favorites:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch favorites'
        });
    }
});

// Get recently played songs
router.get('/:userId/recent', async (req, res) => {
    try {
        const { userId } = req.params;
        const limit = parseInt(req.query.limit) || 20;

        const library = await Library.findByUser(userId);
        if (!library) {
            return res.json({
                success: true,
                songs: []
            });
        }

        const recentSongIds = library.recentlyPlayed
            .slice(0, limit)
            .map(r => r.songId);

        const songs = await Song.find({ id: { $in: recentSongIds } });

        res.json({
            success: true,
            songs: songs
        });
    } catch (error) {
        console.error('Error fetching recent songs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch recent songs'
        });
    }
});

module.exports = router;
