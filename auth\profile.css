/* Profile Page Styles */

:root {
    --profile-sidebar-width: 240px;
    --profile-header-height: 300px;
    --profile-avatar-size: 150px;
    --profile-section-gap: 30px;
}

/* Profile Container */
.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 120px 20px 40px;
}

/* Profile Header */
.profile-header {
    position: relative;
    margin-bottom: 30px;
    border-radius: 12px;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(25, 32, 44, 0.8), rgba(13, 17, 23, 0.8));
    border: 1px solid var(--auth-border-color);
}

.profile-cover {
    height: var(--profile-header-height);
    background: linear-gradient(135deg, rgba(0, 224, 255, 0.1), rgba(255, 0, 110, 0.1));
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-avatar {
    position: relative;
    width: var(--profile-avatar-size);
    height: var(--profile-avatar-size);
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--auth-card-bg);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.change-avatar-btn {
    position: absolute;
    bottom: 10px;
    right: 10px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--button-gradient);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.change-avatar-btn:hover {
    transform: scale(1.1);
}

.profile-info {
    padding: 30px;
    text-align: center;
}

.profile-info h1 {
    font-size: 28px;
    margin: 0 0 5px;
    color: var(--text-color);
}

.profile-info p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0 20px;
}

.profile-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
}

.stat-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

/* Profile Content */
.profile-content {
    display: flex;
    gap: 30px;
}

.profile-sidebar {
    width: var(--profile-sidebar-width);
    flex-shrink: 0;
}

.profile-nav {
    list-style: none;
    padding: 0;
    margin: 0;
    background: var(--auth-card-bg);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--auth-border-color);
}

.profile-nav li {
    border-bottom: 1px solid var(--auth-border-color);
}

.profile-nav li:last-child {
    border-bottom: none;
}

.profile-nav a {
    display: block;
    padding: 15px 20px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.profile-nav a:hover {
    background: rgba(255, 255, 255, 0.05);
}

.profile-nav a.active {
    background: linear-gradient(90deg, rgba(0, 224, 255, 0.1), transparent);
    color: var(--neon-blue);
    border-left: 3px solid var(--neon-blue);
}

.profile-main {
    flex: 1;
}

.profile-section {
    display: none;
    background: var(--auth-card-bg);
    border-radius: 12px;
    padding: 30px;
    border: 1px solid var(--auth-border-color);
    margin-bottom: 20px;
}

.profile-section.active {
    display: block;
}

.profile-section h2 {
    font-size: 24px;
    margin: 0 0 20px;
    color: var(--text-color);
    border-bottom: 1px solid var(--auth-border-color);
    padding-bottom: 15px;
}

.profile-section h3 {
    font-size: 18px;
    margin: 0 0 15px;
    color: var(--text-color);
}

/* Profile Form */
.profile-form {
    margin-bottom: 30px;
}

.profile-form .form-group {
    margin-bottom: 20px;
}

.profile-form label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
}

.profile-form textarea {
    width: 100%;
    padding: 12px 15px;
    border-radius: var(--auth-border-radius);
    border: 1px solid var(--auth-border-color);
    background: var(--auth-input-bg);
    color: var(--text-color);
    font-size: 16px;
    resize: vertical;
    min-height: 100px;
    transition: all 0.3s ease;
}

.profile-form textarea:focus {
    border-color: var(--neon-blue);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 224, 255, 0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
}

/* Security Section */
.security-section {
    margin-top: 40px;
}

.session-list {
    margin-top: 15px;
}

.session-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 10px;
}

.session-item.current {
    background: rgba(0, 224, 255, 0.05);
    border: 1px solid rgba(0, 224, 255, 0.2);
}

.session-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.session-info i {
    font-size: 24px;
    color: rgba(255, 255, 255, 0.7);
}

.session-info h4 {
    margin: 0 0 5px;
    font-size: 16px;
    color: var(--text-color);
}

.session-info p {
    margin: 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.session-time {
    font-size: 12px !important;
    color: rgba(255, 255, 255, 0.5) !important;
    margin-top: 5px !important;
}

.session-action {
    background: none;
    border: 1px solid var(--auth-border-color);
    border-radius: 4px;
    padding: 6px 12px;
    color: var(--text-color);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.session-action:hover:not([disabled]) {
    background: rgba(255, 59, 48, 0.1);
    border-color: rgba(255, 59, 48, 0.3);
    color: var(--auth-error-color);
}

.session-action[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Subscription Section */
.subscription-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.subscription-header h3 {
    margin: 0;
}

.subscription-status {
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
}

.subscription-status.free {
    background: rgba(52, 199, 89, 0.1);
    color: #34c759;
}

.subscription-status.premium {
    background: rgba(0, 122, 255, 0.1);
    color: #007aff;
}

.subscription-status.artist {
    background: rgba(255, 149, 0, 0.1);
    color: #ff9500;
}

.subscription-features {
    margin-top: 30px;
}

.plan-comparison {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.plan-column {
    flex: 1;
    min-width: 200px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--auth-border-color);
}

.plan-column.premium {
    background: rgba(0, 122, 255, 0.05);
    border-color: rgba(0, 122, 255, 0.2);
}

.plan-column.artist {
    background: rgba(255, 149, 0, 0.05);
    border-color: rgba(255, 149, 0, 0.2);
}

.plan-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid var(--auth-border-color);
}

.plan-header h4 {
    margin: 0 0 10px;
    font-size: 18px;
    color: var(--text-color);
}

.plan-price {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
}

.plan-period {
    font-size: 14px;
    font-weight: normal;
    color: rgba(255, 255, 255, 0.7);
}

.plan-features {
    list-style: none;
    padding: 20px;
    margin: 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

.plan-features li:last-child {
    margin-bottom: 0;
}

.plan-features i.fa-check {
    color: #34c759;
}

.plan-features i.fa-times {
    color: #ff3b30;
}

/* Notifications Section */
.notification-group {
    margin-bottom: 30px;
}

.notification-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 10px;
}

.notification-option h4 {
    margin: 0 0 5px;
    font-size: 16px;
    color: var(--text-color);
}

.notification-option p {
    margin: 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.2);
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background: var(--button-gradient);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Privacy Section */
.privacy-group {
    margin-bottom: 30px;
}

.privacy-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    margin-bottom: 10px;
}

.privacy-option h4 {
    margin: 0 0 5px;
    font-size: 16px;
    color: var(--text-color);
}

.privacy-option p {
    margin: 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.privacy-select {
    padding: 8px 12px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--auth-border-color);
    color: var(--text-color);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.privacy-select:focus {
    outline: none;
    border-color: var(--neon-blue);
}

.data-actions {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid var(--auth-border-color);
}

.data-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.secondary-button, .danger-button {
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.secondary-button {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    border: 1px solid var(--auth-border-color);
}

.secondary-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.danger-button {
    background: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
    border: 1px solid rgba(255, 59, 48, 0.3);
}

.danger-button:hover {
    background: rgba(255, 59, 48, 0.2);
}

/* Delete Account Modal */
.delete-warning {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    border-radius: 8px;
    background: rgba(255, 59, 48, 0.1);
    margin-bottom: 20px;
}

.delete-warning i {
    font-size: 24px;
    color: #ff3b30;
}

.delete-warning p {
    margin: 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .profile-content {
        flex-direction: column;
    }
    
    .profile-sidebar {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .profile-nav {
        display: flex;
        flex-wrap: wrap;
    }
    
    .profile-nav li {
        flex: 1;
        min-width: 120px;
        border-bottom: none;
        border-right: 1px solid var(--auth-border-color);
    }
    
    .profile-nav li:last-child {
        border-right: none;
    }
    
    .profile-nav a {
        text-align: center;
        padding: 10px;
    }
    
    .profile-section {
        padding: 20px;
    }
    
    .subscription-info {
        padding: 15px;
    }
    
    .plan-comparison {
        flex-direction: column;
    }
    
    .plan-column {
        min-width: 100%;
    }
    
    .notification-option, .privacy-option {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .toggle-switch, .privacy-select {
        align-self: flex-start;
    }
    
    .data-buttons {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .profile-header {
        margin-bottom: 20px;
    }
    
    .profile-cover {
        height: 200px;
    }
    
    .profile-avatar {
        width: 100px;
        height: 100px;
    }
    
    .profile-info {
        padding: 20px;
    }
    
    .profile-info h1 {
        font-size: 24px;
    }
    
    .profile-stats {
        gap: 20px;
    }
    
    .stat-value {
        font-size: 20px;
    }
    
    .profile-section h2 {
        font-size: 20px;
    }
    
    .profile-section h3 {
        font-size: 16px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions button {
        width: 100%;
    }
}
