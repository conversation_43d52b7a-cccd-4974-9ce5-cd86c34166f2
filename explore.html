<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore Music - Banshee Music App</title>
    <meta name="description" content="Explore new music, artists, and genres on Banshee Music">
    <meta name="theme-color" content="#000000">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/explore.css">
    <link rel="stylesheet" href="css/mini-player.css">
    <link rel="stylesheet" href="https://unpkg.com/flickity@2/dist/flickity.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/flickity@2/dist/flickity.pkgd.min.js"></script>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <a href="subscription.html" aria-label="Go to subscription page">
                    <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
                </a>
            </div>
            <ul class="menu" aria-label="Main Navigation">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html" aria-current="page">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile" loading="lazy">
                </button>
                <div class="dropdown">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><a href="#" class="logout">Logout</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main id="main-content" role="main" class="explore-container">
        <div class="loader" id="loader"></div>

        <section class="explore-hero">
            <div class="hero-content">
                <h1>Explore Music</h1>
                <p class="hero-subtitle">Discover new artists, genres, and tracks tailored to your taste</p>
            </div>
        </section>

        <section class="section search-section">
            <div class="section-header">
                <h2>Discover Music</h2>
            </div>
            <div class="search-container">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="search"
                           class="search-bar"
                           placeholder="Search for artists, songs, or albums..."
                           aria-label="Search for music"
                           role="searchbox"
                           minlength="2">
                    <button type="button" class="search-clear" aria-label="Clear search">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <button type="button" class="btn-medium search-button">Search</button>
            </div>

            <div class="filters-container">
                <select class="filter-select" aria-label="Filter by genre">
                    <option value="">Genre</option>
                    <option value="pop">Pop</option>
                    <option value="rock">Rock</option>
                    <option value="hiphop">Hip-Hop</option>
                    <option value="rnb">R&B</option>
                    <option value="jazz">Jazz</option>
                    <option value="electronic">Electronic</option>
                    <option value="classical">Classical</option>
                    <option value="indie">Indie</option>
                    <option value="metal">Metal</option>
                    <option value="blues">Blues</option>
                    <option value="country">Country</option>
                    <option value="folk">Folk</option>
                    <option value="reggae">Reggae</option>
                    <option value="latin">Latin</option>
                    <option value="soul">Soul</option>
                    <option value="funk">Funk</option>
                    <option value="punk">Punk</option>
                    <option value="ambient">Ambient</option>
                    <option value="edm">EDM</option>
                    <option value="trap">Trap</option>
                    <option value="house">House</option>
                    <option value="techno">Techno</option>
                    <option value="alternative">Alternative</option>
                    <option value="world">World</option>
                    <option value="instrumental">Instrumental</option>
                    <option value="soundtrack">Soundtrack</option>
                    <option value="gospel">Gospel</option>
                </select>
                <select class="filter-select" aria-label="Filter by popularity">
                    <option value="">Popularity</option>
                    <option value="most-popular">Most Popular</option>
                    <option value="trending">Trending</option>
                    <option value="new-releases">New Releases</option>
                </select>
                <select class="filter-select" aria-label="Filter by release date">
                    <option value="">Release Date</option>
                    <option value="today">Today</option>
                    <option value="this-week">This Week</option>
                    <option value="last-week">Last Week</option>
                    <option value="this-month">This Month</option>
                    <option value="last-month">Last Month</option>
                    <option value="last-3-months">Last 3 Months</option>
                    <option value="last-6-months">Last 6 Months</option>
                    <option value="this-year">This Year</option>
                    <option value="last-year">Last Year</option>
                    <option value="last-2-years">Last 2 Years</option>
                    <option value="last-5-years">Last 5 Years</option>
                    <option value="2010s">2010s</option>
                    <option value="2000s">2000s</option>
                    <option value="1990s">1990s</option>
                    <option value="1980s">1980s</option>
                    <option value="1970s">1970s</option>
                    <option value="older">Older</option>
                </select>
            </div>
            <div class="results-count"></div>
        </section>

        <section class="section featured-artists">
            <div class="section-header">
                <h2>Featured Artists</h2>
            </div>
            <div class="carousel" data-flickity='{
                "wrapAround": true,
                "pageDots": true,
                "prevNextButtons": true,
                "autoPlay": 5000,
                "pauseAutoPlayOnHover": true,
                "lazyLoad": 2
            }'>
                <!-- First Grid -->
                <div class="carousel-cell">
                    <div class="skeleton-cards">
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                    </div>
                    <div class="cards">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png"
                                     alt="Artist 1"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Artist 1">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist 1</h3>
                                    <p>Pop • Rock • 2.5M Followers</p>
                                </div>
                                <button type="button" class="button" aria-label="View Artist 1 Profile">View Profile</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png"
                                     alt="Artist 2"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Artist 2">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist 2</h3>
                                    <p>Rock • Alternative • 1.8M Followers</p>
                                </div>
                                <button type="button" class="button" aria-label="View Artist 2 Profile">View Profile</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png"
                                     alt="Artist 3"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Artist 3">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist 3</h3>
                                    <p>Hip-Hop • R&B • 3.2M Followers</p>
                                </div>
                                <button type="button" class="button" aria-label="View Artist 3 Profile">View Profile</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png"
                                     alt="Artist 4"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Artist 4">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist 4</h3>
                                    <p>Jazz • Soul • 1.5M Followers</p>
                                </div>
                                <button type="button" class="button" aria-label="View Artist 4 Profile">View Profile</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Grid -->
                <div class="carousel-cell">
                    <div class="skeleton-cards">
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                    </div>
                    <div class="cards">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png"
                                     alt="Artist 5"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Artist 5">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist 5</h3>
                                    <p>Pop • Electronic • 4.1M Followers</p>
                                </div>
                                <button type="button" class="button" aria-label="View Artist 5 Profile">View Profile</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png"
                                     alt="Artist 6"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Artist 6">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist 6</h3>
                                    <p>Indie • Folk • 1.2M Followers</p>
                                </div>
                                <button type="button" class="button" aria-label="View Artist 6 Profile">View Profile</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png"
                                     alt="Artist 7"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Artist 7">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist 7</h3>
                                    <p>Rock • Metal • 2.7M Followers</p>
                                </div>
                                <button type="button" class="button" aria-label="View Artist 7 Profile">View Profile</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-02.png"
                                     alt="Artist 8"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Artist 8">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Artist 8</h3>
                                    <p>Electronic • EDM • 3.9M Followers</p>
                                </div>
                                <button type="button" class="button" aria-label="View Artist 8 Profile">View Profile</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recommended Music Section -->
        <section class="section recommended-music">
            <div class="section-header">
                <h2>Recommended Music</h2>
            </div>
            <div class="carousel" data-flickity='{
                "wrapAround": true,
                "pageDots": true,
                "prevNextButtons": true,
                "autoPlay": 5000,
                "pauseAutoPlayOnHover": true,
                "lazyLoad": 2
            }'>
                <!-- First Grid -->
                <div class="carousel-cell">
                    <div class="skeleton-cards">
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                    </div>
                    <div class="cards">
                        <div class="card">
                            <div class="img-container">
                                <img class="music-note"
                                     src="imgs/album-02.png"
                                     alt="Album 1"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Album 1">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Album 1</h3>
                                    <p>Artist Name • 12 tracks • 3:45</p>
                                </div>
                                <button type="button" class="button" aria-label="Play Album 1">Play</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img class="music-note"
                                     src="imgs/album-02.png"
                                     alt="Album 2"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Album 2">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Album 2</h3>
                                    <p>Artist Name • 10 tracks • 3:15</p>
                                </div>
                                <button type="button" class="button" aria-label="Play Album 2">Play</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img class="music-note"
                                     src="imgs/album-02.png"
                                     alt="Album 3"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Album 3">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Album 3</h3>
                                    <p>Artist Name • 8 tracks • 2:45</p>
                                </div>
                                <button type="button" class="button" aria-label="Play Album 3">Play</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img class="music-note"
                                     src="imgs/album-02.png"
                                     alt="Album 4"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Album 4">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Album 4</h3>
                                    <p>Artist Name • 14 tracks • 4:20</p>
                                </div>
                                <button type="button" class="button" aria-label="Play Album 4">Play</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Grid -->
                <div class="carousel-cell">
                    <div class="skeleton-cards">
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                    </div>
                    <div class="cards">
                        <div class="card">
                            <div class="img-container">
                                <img class="music-note"
                                     src="imgs/album-02.png"
                                     alt="Album 5"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Album 5">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Album 5</h3>
                                    <p>Artist Name • 9 tracks • 3:30</p>
                                </div>
                                <button type="button" class="button" aria-label="Play Album 5">Play</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img class="music-note"
                                     src="imgs/album-02.png"
                                     alt="Album 6"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Album 6">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Album 6</h3>
                                    <p>Artist Name • 11 tracks • 3:55</p>
                                </div>
                                <button type="button" class="button" aria-label="Play Album 6">Play</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img class="music-note"
                                     src="imgs/album-02.png"
                                     alt="Album 7"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Album 7">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Album 7</h3>
                                    <p>Artist Name • 7 tracks • 2:50</p>
                                </div>
                                <button type="button" class="button" aria-label="Play Album 7">Play</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img class="music-note"
                                     src="imgs/album-02.png"
                                     alt="Album 8"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Album 8">
                                        <i class="fas fa-play"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Album 8</h3>
                                    <p>Artist Name • 15 tracks • 4:10</p>
                                </div>
                                <button type="button" class="button" aria-label="Play Album 8">Play</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Popular Genres Section -->
        <section class="section genres">
            <div class="section-header">
                <h2>Popular Genres</h2>
            </div>
            <div class="carousel" data-flickity='{
                "wrapAround": true,
                "pageDots": true,
                "prevNextButtons": true,
                "autoPlay": 5000,
                "pauseAutoPlayOnHover": true,
                "lazyLoad": 2
            }'>
                <!-- First Grid -->
                <div class="carousel-cell">
                    <div class="skeleton-cards">
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                    </div>
                    <div class="cards">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png"
                                     alt="Genre 1"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Genre 1 Mix">
                                        <i class="fas fa-random"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Genre 1</h3>
                                    <p>1000+ tracks • 500+ artists</p>
                                </div>
                                <button type="button" class="button" aria-label="View Genre 1">View All</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png"
                                     alt="Genre 2"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Genre 2 Mix">
                                        <i class="fas fa-random"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Genre 2</h3>
                                    <p>850+ tracks • 320+ artists</p>
                                </div>
                                <button type="button" class="button" aria-label="View Genre 2">View All</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png"
                                     alt="Genre 3"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Genre 3 Mix">
                                        <i class="fas fa-random"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Genre 3</h3>
                                    <p>750+ tracks • 280+ artists</p>
                                </div>
                                <button type="button" class="button" aria-label="View Genre 3">View All</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png"
                                     alt="Genre 4"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Genre 4 Mix">
                                        <i class="fas fa-random"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Genre 4</h3>
                                    <p>920+ tracks • 450+ artists</p>
                                </div>
                                <button type="button" class="button" aria-label="View Genre 4">View All</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Grid -->
                <div class="carousel-cell">
                    <div class="skeleton-cards">
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                        <div class="skeleton-card"></div>
                    </div>
                    <div class="cards">
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png"
                                     alt="Genre 5"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Genre 5 Mix">
                                        <i class="fas fa-random"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Genre 5</h3>
                                    <p>680+ tracks • 210+ artists</p>
                                </div>
                                <button type="button" class="button" aria-label="View Genre 5">View All</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png"
                                     alt="Genre 6"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Genre 6 Mix">
                                        <i class="fas fa-random"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Genre 6</h3>
                                    <p>790+ tracks • 340+ artists</p>
                                </div>
                                <button type="button" class="button" aria-label="View Genre 6">View All</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png"
                                     alt="Genre 7"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Genre 7 Mix">
                                        <i class="fas fa-random"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Genre 7</h3>
                                    <p>550+ tracks • 180+ artists</p>
                                </div>
                                <button type="button" class="button" aria-label="View Genre 7">View All</button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="img-container">
                                <img src="imgs/album-03.png"
                                     alt="Genre 8"
                                     loading="lazy"
                                     width="200"
                                     height="200"
                                     decoding="async">
                                <div class="play-overlay">
                                    <button type="button" class="play-button" aria-label="Play Genre 8 Mix">
                                        <i class="fas fa-random"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-content">
                                <div class="text-content">
                                    <h3>Genre 8</h3>
                                    <p>620+ tracks • 250+ artists</p>
                                </div>
                                <button type="button" class="button" aria-label="View Genre 8">View All</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="pagination">
            <button type="button" class="nav-button" aria-label="Previous Page">Previous</button>
            <button type="button" class="nav-button active" aria-label="Page 1">1</button>
            <button type="button" class="nav-button" aria-label="Page 2">2</button>
            <button type="button" class="nav-button" aria-label="Page 3">3</button>
            <button type="button" class="nav-button" aria-label="Next Page">Next</button>
        </div>
    </main>

    <!-- Footer -->
    <footer class="site-footer">
        <div class="footer-content">
            <div class="footer-info">
                <p class="copyright">&copy; 2024 Banshee Music App. All rights reserved.</p>
                <nav class="footer-nav">
                    <a href="privacy.html">Privacy Policy</a>
                    <a href="terms.html">Terms of Service</a>
                    <a href="contact.html">Contact Us</a>
                </nav>
            </div>
        </div>
    </footer>

    <button type="button" onclick="window.scrollTo(0, 0)" id="backToTop" class="back-to-top" aria-label="Back to top">↑</button>

    <script src="js/navbar.js"></script>
    <script src="js/explore.js" type="module"></script>
</body>
</html>
