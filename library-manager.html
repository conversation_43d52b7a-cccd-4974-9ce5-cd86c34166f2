<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music Library - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="library-manager.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <a href="index.html" aria-label="Go to home page">
                    <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
                </a>
            </div>
            <ul class="menu" aria-label="Main Navigation">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html" aria-current="page">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile" loading="lazy">
                </button>
                <div class="dropdown">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><a href="#" class="logout">Logout</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <div class="library-container">
        <aside class="library-sidebar">
            <div class="sidebar-section">
                <h2>Library</h2>
                <ul class="sidebar-nav">
                    <li><a href="#" id="playlists-tab" class="active">Playlists</a></li>
                    <li><a href="#" id="favorites-tab">Favorites</a></li>
                    <li><a href="#" id="recently-played-tab">Recently Played</a></li>
                    <li><a href="#" id="downloads-tab">Downloads</a></li>
                </ul>
            </div>

            <div class="sidebar-section">
                <div class="section-header">
                    <h2>Your Playlists</h2>
                    <button type="button" id="create-playlist-btn" class="create-btn">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <div id="playlists-container" class="playlists-list">
                    <!-- Playlists will be rendered here -->
                </div>
            </div>
        </aside>

        <main class="library-main">
            <div id="main-content" class="main-content">
                <!-- Main content will be rendered here -->
                <div class="section-header">
                    <h1>Your Playlists</h1>
                </div>

                <div class="playlists-grid">
                    <!-- Playlists will be rendered here -->
                </div>

                <div class="section-header">
                    <h2>Favorites</h2>
                    <a href="#" id="view-all-favorites" class="view-all">View All</a>
                </div>

                <div id="favorites-preview" class="favorites-preview">
                    <!-- Favorites preview will be rendered here -->
                </div>

                <div class="section-header">
                    <h2>Recently Played</h2>
                    <a href="#" id="view-all-recently-played" class="view-all">View All</a>
                </div>

                <div id="recently-played-preview" class="recently-played-preview">
                    <!-- Recently played preview will be rendered here -->
                </div>
            </div>

            <div id="favorites-content" class="content-section hidden">
                <div class="section-header">
                    <h1>Your Favorites</h1>
                </div>

                <div id="favorites-container">
                    <!-- Favorites will be rendered here -->
                </div>
            </div>

            <div id="recently-played-content" class="content-section hidden">
                <div class="section-header">
                    <h1>Recently Played</h1>
                </div>

                <div id="recently-played-container">
                    <!-- Recently played will be rendered here -->
                </div>
            </div>

            <div id="downloads-content" class="content-section hidden">
                <div class="section-header">
                    <h1>Downloads</h1>
                </div>

                <div id="downloads-container">
                    <!-- Downloads will be rendered here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Create Playlist Modal -->
    <div id="create-playlist-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Create New Playlist</h2>
                <button type="button" class="close-modal" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="new-playlist-form">
                    <div class="form-group">
                        <label for="new-playlist-name">Playlist Name</label>
                        <input type="text" id="new-playlist-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="new-playlist-description">Description (optional)</label>
                        <textarea id="new-playlist-description" name="description"></textarea>
                    </div>
                    <div class="form-group checkbox">
                        <input type="checkbox" id="new-playlist-public" name="public" checked>
                        <label for="new-playlist-public">Make playlist public</label>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="cancel-btn">Cancel</button>
                        <button type="submit" class="create-btn">Create</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Playlist Modal -->
    <div id="edit-playlist-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Edit Playlist</h2>
                <button type="button" class="close-modal" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="edit-playlist-form">
                    <div class="form-group">
                        <label for="edit-playlist-name">Playlist Name</label>
                        <input type="text" id="edit-playlist-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-playlist-description">Description (optional)</label>
                        <textarea id="edit-playlist-description" name="description"></textarea>
                    </div>
                    <div class="form-group checkbox">
                        <input type="checkbox" id="edit-playlist-public" name="public">
                        <label for="edit-playlist-public">Make playlist public</label>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="cancel-btn">Cancel</button>
                        <button type="submit" class="save-btn">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Mini Player -->
    <div class="mini-player">
        <div class="now-playing">
            <img src="imgs/album-01.png" alt="Now Playing" class="now-playing-cover">
            <div class="now-playing-info">
                <h3 class="now-playing-title">Blinding Lights</h3>
                <p class="now-playing-artist">The Weeknd</p>
            </div>
        </div>
        <div class="player-controls">
            <button type="button" class="control-btn previous" aria-label="Previous song">
                <i class="fas fa-step-backward"></i>
            </button>
            <button type="button" class="control-btn play-pause" aria-label="Play or pause">
                <i class="fas fa-play"></i>
            </button>
            <button type="button" class="control-btn next" aria-label="Next song">
                <i class="fas fa-step-forward"></i>
            </button>
        </div>
        <div class="player-progress">
            <span class="current-time">0:00</span>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <span class="total-time">3:20</span>
        </div>
        <div class="player-options">
            <button type="button" class="option-btn volume" aria-label="Volume">
                <i class="fas fa-volume-up"></i>
            </button>
            <button type="button" class="option-btn repeat" aria-label="Repeat">
                <i class="fas fa-redo"></i>
            </button>
            <button type="button" class="option-btn shuffle" aria-label="Shuffle">
                <i class="fas fa-random"></i>
            </button>
            <button type="button" class="option-btn queue" aria-label="Queue">
                <i class="fas fa-list"></i>
            </button>
        </div>
    </div>

    <footer>
        <p>&copy; 2023 Banshee Music App. All rights reserved.</p>
    </footer>

    <script type="module" src="js/library/main.js"></script>
    <script type="module">
        document.addEventListener('DOMContentLoaded', () => {
            // Set up tab navigation
            const tabs = {
                'playlists-tab': document.getElementById('main-content'),
                'favorites-tab': document.getElementById('favorites-content'),
                'recently-played-tab': document.getElementById('recently-played-content'),
                'downloads-tab': document.getElementById('downloads-content')
            };

            // Add click event listeners to tabs
            Object.keys(tabs).forEach(tabId => {
                const tabElement = document.getElementById(tabId);
                if (tabElement && tabs[tabId]) {
                    tabElement.addEventListener('click', (e) => {
                        e.preventDefault();

                        // Hide all content sections
                        Object.values(tabs).forEach(content => {
                            if (content) content.classList.add('hidden');
                        });

                        // Show the selected content section
                        tabs[tabId].classList.remove('hidden');

                        // Update active tab
                        document.querySelectorAll('.sidebar-nav a').forEach(tab => {
                            tab.classList.remove('active');
                        });
                        tabElement.classList.add('active');
                    });
                }
            });

            // Set up modal functionality
            const modals = document.querySelectorAll('.modal');
            const closeButtons = document.querySelectorAll('.close-modal, .cancel-btn');

            closeButtons.forEach(button => {
                button.addEventListener('click', () => {
                    modals.forEach(modal => {
                        modal.style.display = 'none';
                    });
                });
            });

            // Close modal when clicking outside the modal content
            window.addEventListener('click', (e) => {
                modals.forEach(modal => {
                    if (e.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            });

            // Set up view all links
            const viewAllFavorites = document.getElementById('view-all-favorites');
            if (viewAllFavorites) {
                viewAllFavorites.addEventListener('click', (e) => {
                    e.preventDefault();
                    document.getElementById('favorites-tab').click();
                });
            }

            const viewAllRecentlyPlayed = document.getElementById('view-all-recently-played');
            if (viewAllRecentlyPlayed) {
                viewAllRecentlyPlayed.addEventListener('click', (e) => {
                    e.preventDefault();
                    document.getElementById('recently-played-tab').click();
                });
            }
        });
    </script>
</body>
</html>
