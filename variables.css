/* CSS Variables */
:root {
    /* Colors */
    --background-color: #0D1117;
    --text-color: #ffffff;
    --electric-violet: #6F00FF;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    --error-color: #ff5a5a;
    --hover-color: rgba(255, 255, 255, 0.1);
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    --accent-color: var(--cosmic-pink);

    /* Gradients */
    --button-gradient: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --header-gradient: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));

    /* Shadows */
    --button-hover-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --button-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    --card-shadow: 0 8px 32px rgba(56, 12, 97, 0.15);
    --dropdown-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

    /* Components */
    --dropdown-bg: rgba(255, 255, 255, 0.1);
    --dropdown-border: 1px solid rgba(255, 255, 255, 0.1);

    /* Spacing */
    --section-gap: 2rem;
    --container-padding: 20px;

    /* Border Radius */
    --border-radius-lg: 25px;
    --border-radius-md: 16px;
    --border-radius-sm: 12px;
    --explore-card-radius: 12px;

    /* Transitions */
    --transition-speed: 0.3s;
    --twinkle-duration: 3s;
}

