/* Enhanced Base styles to match Home/Explore pages */
:root {
    /* Colors - Updated to match site theme */
    --background-color: #0D1117;
    --text-color: #ffffff;
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    --cyber-lime-rgb: 167, 255, 74;
    --accent-color: var(--cosmic-pink);
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;

    /* Standardize variable names to match other pages */
    --text-primary: var(--text-color);
    --text-secondary: rgba(255, 255, 255, 0.7);
    --background: var(--background-color);
    --border: rgba(255, 255, 255, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --button-shadow: 0 4px 12px rgba(var(--electric-violet-rgb), 0.3);
    --transition-speed: 0.3s ease;
    --transition-fast: 0.2s ease;
    --header-gradient: linear-gradient(to right, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    --hover-color: rgba(255, 255, 255, 0.05);
    --error-color: #ff4d4d;
    --button-hover-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);

    /* Button Gradient - Updated to match style.css */
    --button-gradient: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --card-gradient: linear-gradient(135deg, rgba(var(--electric-violet-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.1));
}

body {
    background: var(--background);
    color: var(--text-primary);
    font-family: 'Plus Jakarta Sans', sans-serif;
    line-height: 1.5;
    margin: 0;
    padding-top: 114px; /* Add space for fixed navbar with larger logo */
    overflow-x: hidden;
}

/* Navbar Styles */
header {
    background: linear-gradient(
        to right,
        var(--header-gradient-start) 0%,
        var(--header-gradient-end) 100%
    );
    padding: 12px 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo img {
    width: 90px;
    height: 90px;
    transition: transform var(--transition-speed);
}

.logo img:hover {
    transform: scale(1.05);
}

.menu {
    list-style: none;
    display: flex;
    gap: 3rem;
    margin: 0;
    padding: 0;
}

.menu li a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.125rem;
    position: relative;
    padding: 0.5rem 0;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0);
}

.menu li a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--button-gradient);
    transition: width 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0), opacity 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0), box-shadow 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0);
    opacity: 0;
}

.menu li a:hover::after,
.menu li a[aria-current="page"]::after {
    width: 100%;
    opacity: 1;
    box-shadow: 0 0 20px var(--neon-blue);
}

.menu li a:hover {
    color: rgba(255, 255, 255, 1);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.user-profile {
    position: relative;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(var(--electric-violet-rgb), 0.3);
    transition: transform var(--transition-speed), border-color var(--transition-speed), box-shadow var(--transition-speed), filter var(--transition-speed);
}

.profile-button:hover .profile-icon {
    transform: scale(1.05);
    border-color: rgba(0, 224, 255, 0.3);
    box-shadow:
        0 0 15px rgba(0, 224, 255, 0.2),
        0 0 30px rgba(0, 224, 255, 0.1);
    filter: brightness(1.1);
}

.dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: rgba(20, 23, 31, 0.85);
    border-radius: 12px;
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px) scale(0.98);
    transition: all 0.25s cubic-bezier(0.3, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 224, 255, 0.08);
    -webkit-backdrop-filter: blur(12px);
    backdrop-filter: blur(12px);
    transform-origin: top right;
    z-index: 1001;
    overflow: hidden;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--button-gradient);
    opacity: 0.8;
}

.dropdown.show,
.user-profile:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 8px 0;
}

.dropdown li {
    margin: 0;
    padding: 0;
}

.dropdown a {
    color: var(--text-primary);
    text-decoration: none;
    padding: 10px 16px;
    display: block;
    font-size: 14px;
    transition: all 0.2s ease;
    position: relative;
    margin: 2px 5px;
    border-radius: 8px;
    font-weight: 500;
}

.dropdown a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: var(--button-gradient);
    opacity: 0.1;
    transition: width 0.2s ease;
    border-radius: 8px;
    z-index: -1;
}

.dropdown a:hover {
    color: white;
    transform: translateX(3px);
}

.dropdown a:hover::before {
    width: 100%;
}

.dropdown .logout {
    color: var(--cosmic-pink);
    margin-top: 5px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: 12px;
}

.dropdown .logout:hover {
    color: white;
    background-color: rgba(var(--cosmic-pink-rgb), 0.15);
}

/* Core Layout - Updated to match Home/Explore */
.content-wrapper {
    padding: 32px;
    height: calc(100vh - 160px);
    overflow-y: auto;
    background: var(--background-color);
}

/* Playlist Header - Enhanced with gradient background */
.playlist-header {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 32px;
    padding: 40px;
    background: rgba(255, 255, 255, 0.04);
    border-radius: 16px;
    margin-bottom: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
    overflow: hidden;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed), border-color var(--transition-speed);
}

.playlist-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.12);
}

/* Add gradient overlay to match Profile page */
.playlist-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: 0;
    pointer-events: none;
}

.playlist-cover {
    position: relative;
    width: 280px;
    height: 280px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(0, 224, 255, 0.4); /* Changed to neon-blue */
    z-index: 1;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.playlist-cover:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.4);
}

.playlist-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.edit-cover {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: opacity var(--transition-speed);
    cursor: pointer;
    border: none;
    color: white;
}

.edit-cover i {
    font-size: 24px;
    color: var(--neon-blue);
}

.edit-cover span {
    font-size: 14px;
    font-weight: 600;
}

.edit-cover:hover {
    opacity: 1;
}

.playlist-info {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    gap: 16px;
    background: rgba(255, 255, 255, 0.03); /* Match profile page style */
    padding: 24px;
    border-radius: 12px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    z-index: 1; /* Ensure it's above the gradient overlay */
    transition: background var(--transition-speed), border-color var(--transition-speed);
}

.playlist-header:hover .playlist-info {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.1);
}

.playlist-type {
    font-size: 14px;
    font-weight: 600;
    color: var(--neon-blue);
    letter-spacing: 1px;
}

#playlist-name {
    font-size: 48px;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0;
    line-height: 1.2;
    transition: transform var(--transition-fast);
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.playlist-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-secondary);
    font-size: 14px;
}

.creator-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    object-fit: cover;
}

.dot {
    font-size: 8px;
    vertical-align: middle;
}

.playlist-actions {
    display: flex;
    gap: 16px;
    margin-top: 24px;
}

/* Button Styles - Updated to match Home/Explore */
.play-all, .shuffle-play, .more-options {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: 25px; /* Updated to match Explore page */
    font-weight: 600;
    font-size: 15px;
    transition: all var(--transition-speed);
    border: none;
    cursor: pointer;
    text-transform: uppercase; /* Added to match Explore page */
    letter-spacing: 1px; /* Added to match Explore page */
}

.play-all {
    background: var(--button-gradient);
    color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.play-all:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
}

.play-all:active, .shuffle-play:active, .more-options:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

.shuffle-play {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 120px; /* Added to match Explore page */
}

.shuffle-play:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.1);
}

.share-playlist {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 12px 24px;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed);
}

.share-playlist:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.1);
}

.more-options {
    background: transparent;
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.more-options:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* Songs Section - Updated to match Library page */
.songs-section {
    margin-top: 24px;
    background: rgba(20, 30, 45, 0.3);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.section-header h2 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

/* Songs Table - Enhanced with better styling */
.songs-table {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 12px;
    overflow: hidden;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: var(--shadow-md);
}

.songs-header {
    display: grid;
    grid-template-columns: 60px 3fr 2fr 2fr 80px;
    padding: 16px 24px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.header-cell {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.song-item {
    display: grid;
    grid-template-columns: 60px 3fr 2fr 2fr 80px;
    padding: 12px 24px;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.03);
    transition: background var(--transition-fast);
    position: relative;
    cursor: pointer;
    gap: 16px;
}

.song-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.song-item.playing {
    background: rgba(0, 224, 255, 0.1); /* Changed to neon-blue */
    border-left: 3px solid var(--neon-blue);
    animation: playingPulse 2s infinite alternate;
}

@keyframes playingPulse {
    0% {
        background: rgba(0, 224, 255, 0.1);
    }
    100% {
        background: rgba(0, 224, 255, 0.2);
    }
}

/* Song Details Expansion */
.song-item.expanded {
    margin-bottom: 120px; /* Space for expanded details */
}

.song-details {
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 0 0 8px 8px;
    padding: 0;
    display: grid;
    grid-template-columns: 120px 1fr;
    gap: 20px;
    height: 0;
    overflow: hidden;
    opacity: 0;
    transition: height var(--transition-speed), opacity var(--transition-speed), padding var(--transition-speed);
    z-index: 5;
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-top: none;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.song-item.expanded .song-details {
    height: 110px;
    opacity: 1;
    padding: 16px 24px;
}

.song-waveform {
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.song-waveform::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        rgba(var(--neon-blue-rgb), 0.2) 0%,
        rgba(var(--cosmic-pink-rgb), 0.2) 100%);
    -webkit-mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 30"><path d="M0 15 Q 10 5, 20 15 T 40 15 T 60 15 T 80 15 T 100 15" fill="none" stroke="white" stroke-width="1"/></svg>');
    -webkit-mask-size: 20% 100%;
    mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 30"><path d="M0 15 Q 10 5, 20 15 T 40 15 T 60 15 T 80 15 T 100 15" fill="none" stroke="white" stroke-width="1"/></svg>');
    mask-size: 20% 100%;
}

.song-metadata {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.metadata-row {
    display: flex;
    gap: 16px;
}

.metadata-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.metadata-label {
    font-size: 11px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metadata-value {
    font-size: 14px;
    color: var(--text-primary);
}

.preview-button {
    position: absolute;
    bottom: 16px;
    right: 24px;
    background: var(--button-gradient);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all var(--transition-speed);
}

.preview-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--neon-blue-rgb), 0.3);
}

.song-number {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.song-number input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: var(--electric-violet);
    opacity: 0.7;
    transition: opacity var(--transition-fast);
}

.song-number input[type="checkbox"]:hover,
.song-number input[type="checkbox"]:checked {
    opacity: 1;
}

.song-title-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
}

.song-cover {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
}

.song-title {
    font-weight: 600;
    color: var(--text-primary);
}

.song-artist, .song-album {
    color: var(--text-secondary);
    font-size: 14px;
}

.song-duration {
    color: var(--text-secondary);
    font-size: 14px;
    text-align: right;
}

.song-actions {
    opacity: 0;
    transition: opacity var(--transition-fast);
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.song-item:hover .song-actions {
    opacity: 1;
}

.song-action-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.song-action-btn:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.1);
}

/* Selection Toolbar */
.selection-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(var(--electric-violet-rgb), 0.15);
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(var(--electric-violet-rgb), 0.2);
}

.selection-toolbar[hidden] {
    display: none;
}

.selection-info {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--text-primary);
    font-weight: 500;
}

.selection-info input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
    accent-color: var(--electric-violet);
}

.selection-actions {
    display: flex;
    gap: 12px;
}

.selection-action {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: var(--text-primary);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all var(--transition-fast);
}

.selection-action:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.selection-action[data-action="play"] {
    background: var(--button-gradient);
    color: white;
}

.selection-action[data-action="play"]:hover {
    box-shadow: 0 4px 12px rgba(var(--neon-blue-rgb), 0.3);
}

.selection-action[data-action="remove"]:hover {
    background: rgba(var(--cosmic-pink-rgb), 0.2);
}

/* Sort Select - Updated to match other dropdowns */
.sort-select {
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border);
    border-radius: 20px;
    padding: 0 16px;
    color: var(--text-primary);
    font-family: inherit;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.sort-select:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(111, 0, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(111, 0, 255, 0.2);
}

/* Toast Notifications - Updated to match site style */
.toast {
    position: fixed;
    bottom: 24px;
    right: 24px;
    padding: 12px 20px;
    background: rgba(30, 41, 59, 0.9);
    color: var(--text-primary);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    transform: translateY(100px);
    opacity: 0;
    transition: transform var(--transition-speed), opacity var(--transition-speed);
    z-index: 1000;
    font-weight: 500;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast-success {
    border-left: 4px solid var(--cyber-lime);
}

.toast-info {
    border-left: 4px solid var(--neon-blue);
}

.toast-error {
    border-left: 4px solid var(--cosmic-pink);
}

/* Glow effects for interactive elements */
.playlist-name:focus {
    outline: none;
    text-shadow: 0 0 8px rgba(111, 0, 255, 0.5);
}

.song-item.playing .song-title {
    color: var(--neon-blue);
    text-shadow: 0 0 8px rgba(0, 224, 255, 0.3);
}

/* Media Queries */
@media (max-width: 1024px) {
    .playlist-header {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .playlist-cover {
        width: 220px;
        height: 220px;
        margin: 0 auto;
    }

    .playlist-info {
        text-align: center;
    }

    .playlist-meta {
        justify-content: center;
    }

    .playlist-description {
        max-width: 100%;
        margin: 0 auto;
    }

    .playlist-actions {
        justify-content: center;
    }

    .footer-content {
        padding: 0 1.5rem;
    }
}

@media (max-width: 768px) {
    .songs-header, .song-item {
        grid-template-columns: 40px 3fr 2fr 80px;
    }

    .song-album, .header-cell:nth-child(4) {
        display: none;
    }

    .content-wrapper {
        padding: 16px;
    }

    .songs-section {
        padding: 16px;
    }

    .playlist-header {
        padding: 24px;
    }

    .footer-nav {
        gap: 1.5rem;
    }

    body {
        padding-top: 94px; /* Adjust for smaller navbar on tablets */
    }

    header {
        padding: 10px 15px;
    }

    .logo img {
        width: 70px;
        height: 70px;
    }

    .menu {
        gap: 2rem;
    }

    .menu li a {
        font-size: 1rem;
    }

    .back-to-top-container {
        padding: 0 1.5rem;
    }

    .back-to-top {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 45px;
        height: 45px;
    }

    /* Adjust now playing bar for mobile */
    .now-playing-bar {
        height: 80px;
    }

    .now-playing-left img {
        width: 48px;
        height: 48px;
    }

    .track-name {
        font-size: 12px;
    }

    .artist-name {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .songs-header, .song-item {
        grid-template-columns: 30px 2fr 80px;
    }

    .song-artist, .header-cell:nth-child(3) {
        display: none;
    }

    .playlist-header {
        padding: 16px;
    }

    #playlist-name {
        font-size: 32px;
    }

    .playlist-actions {
        flex-wrap: wrap;
        gap: 12px;
    }

    .play-all, .shuffle-play {
        width: 100%;
        justify-content: center;
    }

    .more-options {
        position: absolute;
        top: 16px;
        right: 16px;
    }

    .footer-nav {
        flex-direction: column;
        gap: 1rem;
    }

    body {
        padding-top: 74px; /* Adjust for smaller navbar on mobile */
    }

    header {
        padding: 8px 12px;
    }

    .logo img {
        width: 50px;
        height: 50px;
    }

    .menu {
        gap: 1rem;
    }

    .menu li a {
        font-size: 0.875rem;
        padding: 0.4rem 0.6rem;
    }

    .profile-icon {
        width: 35px;
        height: 35px;
    }

    .back-to-top-container {
        padding: 0 1rem;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 40px;
        height: 40px;
    }

    .back-to-top i {
        font-size: 1.1rem;
    }

    /* Further adjust player for small screens */
    .now-playing-right {
        margin-right: 10px;
    }

    .now-playing-left {
        margin-left: 10px;
    }

    .control-buttons {
        gap: 8px;
    }

    .control-buttons .play-pause {
        width: 36px;
        height: 36px;
    }

    /* Hide some controls on very small screens */
    .lyrics, .queue {
        display: none;
    }
}

/* Skeleton Loaders */
.skeleton {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
}

.skeleton-song {
    height: 60px;
    margin-bottom: 4px;
    display: grid;
    grid-template-columns: 60px 3fr 2fr 2fr 80px;
    padding: 12px 24px;
    align-items: center;
    gap: 16px;
}

.skeleton-number {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.skeleton-title {
    height: 16px;
    width: 80%;
}

.skeleton-artist, .skeleton-album {
    height: 14px;
    width: 60%;
}

.skeleton-duration {
    height: 14px;
    width: 40px;
    margin-left: auto;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Empty State */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px dashed rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

.empty-state-icon {
    font-size: 3rem;
    color: rgba(255, 255, 255, 0.2);
    margin-bottom: 1rem;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.empty-state-text {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    max-width: 400px;
}

.empty-state-button {
    background: var(--button-gradient);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed);
    box-shadow: var(--button-shadow);
}

.empty-state-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(var(--neon-blue-rgb), 0.3);
}

/* Content Loading Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.song-item {
    animation: fadeInUp 0.3s ease forwards;
    opacity: 0;
}

/* Staggered animation for song items */
.song-item:nth-child(1) { animation-delay: 0.05s; }
.song-item:nth-child(2) { animation-delay: 0.1s; }
.song-item:nth-child(3) { animation-delay: 0.15s; }
.song-item:nth-child(4) { animation-delay: 0.2s; }
.song-item:nth-child(5) { animation-delay: 0.25s; }
.song-item:nth-child(6) { animation-delay: 0.3s; }
.song-item:nth-child(7) { animation-delay: 0.35s; }
.song-item:nth-child(8) { animation-delay: 0.4s; }
.song-item:nth-child(9) { animation-delay: 0.45s; }
.song-item:nth-child(10) { animation-delay: 0.5s; }

/* Add subtle animations */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.play-all:active {
    animation: pulse var(--transition-speed);
}

.playlist-description {
    color: var(--text-secondary);
    margin-top: 8px;
    font-size: 14px;
    max-width: 80%;
    line-height: 1.5;
}

.playlist-description:focus {
    outline: none;
    border-bottom: 1px solid var(--electric-violet);
}

/* This section is a duplicate and has been removed */

/* Options Menu */
.options-menu {
    background: rgba(30, 41, 59, 0.95);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    z-index: 100;
    min-width: 180px;
}

.options-menu ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.options-menu li {
    padding: 12px 16px;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background var(--transition-fast);
}

.options-menu li:hover {
    background: rgba(111, 0, 255, 0.15);
}

.options-menu li:first-child {
    border-radius: 8px 8px 0 0;
}

.options-menu li:last-child {
    border-radius: 0 0 8px 8px;
}

/* Player Controls Refinements */
.player-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 40%;
}

.control-buttons {
    display: flex;
    align-items: center;
    gap: 16px;
}

.control-buttons button {
    background: transparent;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    padding: 8px;
    border-radius: 50%;
}

.control-buttons .play-pause {
    width: 40px;
    height: 40px;
    background: var(--button-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: var(--button-shadow);
    margin-left: 2px; /* Center the play button icon */
}

.control-buttons .play-pause i {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    /* Slight adjustment to visually center the play icon */
    margin-left: 10px;
}

.control-buttons button:hover {
    color: var(--neon-blue);
    transform: scale(1.1);
}

.control-buttons .play-pause:hover {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(0, 224, 255, 0.4);
}

.progress-bar {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 8px;
}

.progress-wrapper {
    flex: 1;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    position: relative;
}

.progress {
    height: 100%;
    background: var(--button-gradient);
    border-radius: 2px;
    width: 30%;
    position: relative;
}

.progress::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.progress-wrapper:hover .progress::after {
    opacity: 1;
}

.current-time, .total-time {
    font-size: 12px;
    color: var(--text-secondary);
    min-width: 35px;
}

/* Now Playing Right Refinements */
.now-playing-right {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-right: 20px;
}

.now-playing-right button {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 8px;
    border-radius: 50%;
}

.now-playing-right button:hover {
    color: var(--neon-blue);
    background: rgba(255, 255, 255, 0.05);
}

.volume {
    position: relative;
    display: flex;
    align-items: center;
}

.volume-slider {
    width: 0;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    transition: width var(--transition-speed);
    margin-left: 8px;
    opacity: 0;
}

.volume:hover .volume-slider {
    width: 80px;
    opacity: 1;
}

.volume-bar {
    height: 100%;
    width: 70%;
    background: var(--button-gradient);
    border-radius: 2px;
}

/* Now Playing Left Refinements */
.now-playing-left {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 20px;
}

.now-playing-left img {
    width: 56px;
    height: 56px;
    border-radius: 8px;
    object-fit: cover;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.track-info {
    display: flex;
    flex-direction: column;
}

.track-name {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
}

.artist-name {
    font-size: 12px;
    color: var(--text-secondary);
}

.like-track {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 8px;
}

.like-track:hover {
    color: var(--cosmic-pink);
}

/* Now Playing Bar */
.now-playing-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 90px;
    background: rgba(13, 17, 23, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
}

/* Footer Styles */
.site-footer {
    position: relative;
    width: 100%;
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.05),
        rgba(255, 255, 255, 0.02)
    );
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-top: 4rem;
    margin-bottom: 90px; /* Add space for the now-playing-bar */
    z-index: 10;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.footer-logo img {
    height: 40px;
    width: auto;
    opacity: 0.8;
    transition: opacity var(--transition-speed);
}

.footer-logo img:hover {
    opacity: 1;
}

.footer-info {
    text-align: center;
}

.copyright {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0 0 1rem;
}

.footer-nav {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.footer-nav a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color var(--transition-speed);
}

.footer-nav a:hover {
    color: var(--accent-color);
}

/* Back to Top Button */
.back-to-top-container {
    position: relative;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    height: 0;
}

.back-to-top {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
    background: var(--button-gradient);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed);
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(var(--cosmic-pink-rgb), 0.3), 0 4px 15px rgba(var(--neon-blue-rgb), 0.3);
    overflow: hidden;
}

.back-to-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(var(--neon-blue-rgb), 0.6), rgba(var(--cosmic-pink-rgb), 0.6));
    opacity: 0;
    transition: opacity var(--transition-speed);
    z-index: -1;
}



.back-to-top:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 25px rgba(var(--cosmic-pink-rgb), 0.4), 0 8px 25px rgba(var(--neon-blue-rgb), 0.4);
}

.back-to-top:hover::before {
    opacity: 1;
}

.back-to-top:active {
    transform: translateY(-2px) scale(0.95);
    transition: transform 0.1s ease;
}

.back-to-top i {
    font-size: 1.3rem;
    transition: transform var(--transition-speed);
}

.back-to-top:hover i {
    transform: translateY(-2px);
    animation: bounce 1s infinite alternate;
}

@keyframes bounce {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-4px);
    }
}

/* Analytics Section */
.analytics-section {
    margin-bottom: 32px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.refresh-analytics-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    transition: all var(--transition-speed);
}

.refresh-analytics-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.refresh-analytics-btn i {
    font-size: 12px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.analytics-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    transition: all var(--transition-speed);
}

.analytics-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.07);
    border-color: rgba(255, 255, 255, 0.12);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.analytics-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: var(--button-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.analytics-content {
    flex: 1;
}

.analytics-content h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0 0 4px 0;
}

.analytics-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.analytics-trend {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.analytics-trend.positive {
    color: #4ade80; /* Green */
}

.analytics-trend.negative {
    color: #f87171; /* Red */
}

.top-songs-chart {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.08);
}

.top-songs-chart h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 16px 0;
    color: var(--text-primary);
}

.chart-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.chart-bar {
    height: 36px;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.3) 0%, rgba(var(--cosmic-pink-rgb), 0.3) 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    position: relative;
    overflow: hidden;
    max-width: 100%;
    transition: all var(--transition-speed);
}

.chart-bar-1 { width: 90%; }
.chart-bar-2 { width: 75%; }
.chart-bar-3 { width: 60%; }
.chart-bar-4 { width: 45%; }
.chart-bar-5 { width: 30%; }

.chart-bar:hover {
    transform: translateX(5px);
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.4) 0%, rgba(var(--cosmic-pink-rgb), 0.4) 100%);
}

.chart-label {
    font-weight: 500;
    color: var(--text-primary);
    z-index: 1;
}

.chart-value {
    font-size: 14px;
    color: var(--text-secondary);
    z-index: 1;
}

/* Songs Section */
.songs-section {
    margin-top: 32px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

/* Share Modal Styles */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

.share-options {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.share-link-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.share-link-container input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--text-primary);
    padding: 12px;
    font-size: 14px;
}

.share-link-container input:focus {
    outline: none;
}

.copy-link-btn {
    background: var(--button-gradient);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.copy-link-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--neon-blue-rgb), 0.3);
}

.share-platforms {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.share-platform-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: all var(--transition-speed);
    color: white;
}

.share-platform-btn i {
    font-size: 24px;
}

.share-platform-btn span {
    font-size: 12px;
    font-weight: 500;
}

.share-platform-btn.facebook {
    background: #1877F2;
}

.share-platform-btn.twitter {
    background: #1DA1F2;
}

.share-platform-btn.instagram {
    background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
}

.share-platform-btn.whatsapp {
    background: #25D366;
}

.share-platform-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.embed-code-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.embed-code-container h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.embed-code-container textarea {
    width: 100%;
    height: 80px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 12px;
    font-family: monospace;
    font-size: 12px;
    resize: none;
}

.embed-code-container textarea:focus {
    outline: none;
    border-color: rgba(var(--neon-blue-rgb), 0.5);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-speed), visibility var(--transition-speed);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: rgba(20, 30, 45, 0.95);
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    transform: translateY(30px);
    transition: transform var(--transition-speed);
}

.modal.active .modal-content {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
}

.close-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 20px;
    cursor: pointer;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.share-options {
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.share-link-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.share-link-container input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--text-primary);
    padding: 12px;
    font-size: 14px;
}

.share-link-container input:focus {
    outline: none;
}

.copy-link-btn {
    background: var(--button-gradient);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.copy-link-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--neon-blue-rgb), 0.3);
}

.share-platforms {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.share-platform-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: all var(--transition-speed);
    color: white;
}

.share-platform-btn i {
    font-size: 24px;
}

.share-platform-btn span {
    font-size: 12px;
    font-weight: 500;
}

.share-platform-btn.facebook {
    background: #1877F2;
}

.share-platform-btn.twitter {
    background: #1DA1F2;
}

.share-platform-btn.instagram {
    background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
}

.share-platform-btn.whatsapp {
    background: #25D366;
}

.share-platform-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.embed-code-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.embed-code-container h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.embed-code-container textarea {
    width: 100%;
    height: 80px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-primary);
    padding: 12px;
    font-family: monospace;
    font-size: 12px;
    resize: none;
}

.embed-code-container textarea:focus {
    outline: none;
    border-color: rgba(var(--neon-blue-rgb), 0.5);
}
