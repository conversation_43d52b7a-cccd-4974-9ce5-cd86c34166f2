/**
 * Explore Page JavaScript
 * Organized and cleaned up for better maintainability
 */

// Import the library API and mini player
import libraryAPI from './library/api-backend.js';
import { Song } from './library/models.js';
import miniPlayer from './mini-player.js';

// Global variables
let currentSearchResults = [];
let currentPlaylists = [];

document.addEventListener('DOMContentLoaded', () => {
    console.log('Explore page loaded');
    console.log('libraryAPI:', libraryAPI);
    console.log('miniPlayer:', miniPlayer);

    // Initialize components
    setupLazyLoading();
    initializeEventListeners();
    initializeCarousels();
    loadExploreContent();

    // Add animation class to navbar for current page
    const currentPageLink = document.querySelector('.menu a[aria-current="page"]');
    if (currentPageLink) {
        currentPageLink.parentElement.classList.add('active');
    }
});

/**
 * Setup enhanced lazy loading for images
 */
function setupLazyLoading() {
    // First, handle all carousel images with higher priority
    const carouselImages = document.querySelectorAll('.carousel img, .img-container img');
    
    if ('loading' in HTMLImageElement.prototype) {
        // Browser supports native lazy loading
        carouselImages.forEach(img => {
            // Make sure all carousel images have loading="lazy" attribute
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
            
            // Add blur-up effect
            img.style.filter = 'blur(5px)';
            img.style.transition = 'filter 0.5s ease-out';
            
            img.onload = function() {
                img.style.filter = 'blur(0)';
            };
        });
        
        // Handle all other images
        const otherImages = document.querySelectorAll('img:not(.carousel img):not(.img-container img)');
        otherImages.forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
        });
    } else {
        // Fallback for browsers that don't support lazy loading
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/lazysizes/5.3.2/lazysizes.min.js';
        document.body.appendChild(script);

        // Process all images
        const allImages = document.querySelectorAll('img[loading="lazy"]');
        allImages.forEach(img => {
            img.classList.add('lazyload');
            
            // Store original source
            const originalSrc = img.src;
            img.setAttribute('data-src', originalSrc);
            
            // Set placeholder
            img.src = 'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==';
            
            // Add blur-up effect for carousel images
            if (img.closest('.carousel') || img.closest('.img-container')) {
                img.style.filter = 'blur(5px)';
                img.style.transition = 'filter 0.5s ease-out';
                
                img.addEventListener('lazyloaded', function() {
                    img.style.filter = 'blur(0)';
                });
            }
        });
    }
    
    // Add intersection observer for better performance
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src && !img.src.includes(img.dataset.src)) {
                        img.src = img.dataset.src;
                    }
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px 0px', // Start loading images when they're 50px from viewport
            threshold: 0.01 // Trigger when at least 1% of the image is visible
        });

        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filter = button.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            // Filter items
            filterItems(filter);
        });
    });

    // Search functionality
    const searchBar = document.querySelector('.search-bar');
    const searchClear = document.querySelector('.search-clear');
    const searchButton = document.querySelector('.search-button');

    if (searchBar) {
        searchBar.addEventListener('input', () => {
            const searchTerm = searchBar.value.trim();
            if (searchTerm.length > 2) {
                console.log('Searching for:', searchTerm);
                searchItems(searchTerm);
            } else if (searchTerm.length === 0) {
                resetSearch();
            }
        });

        searchBar.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const searchTerm = searchBar.value.trim();
                console.log('Enter pressed, searching for:', searchTerm);
                searchItems(searchTerm);
            }
        });
    }

    if (searchClear) {
        searchClear.addEventListener('click', () => {
            searchBar.value = '';
            resetSearch();
        });
    }

    if (searchButton) {
        searchButton.addEventListener('click', () => {
            const searchTerm = searchBar.value.trim();
            console.log('Search button clicked, searching for:', searchTerm);
            searchItems(searchTerm);
        });
    }

    // Play buttons
    const playButtons = document.querySelectorAll('.play-button');
    playButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const itemName = button.getAttribute('aria-label')?.replace('Play ', '') || 'Unknown';
            console.log(`Playing: ${itemName}`);
            // Here you would trigger the actual playback
        });
    });

    // Follow buttons
    const followButtons = document.querySelectorAll('.follow-btn');
    followButtons.forEach(button => {
        button.addEventListener('click', function() {
            const isFollowing = this.classList.contains('following');
            const artistName = this.closest('.artist-card').querySelector('.artist-name').textContent;
            
            if (isFollowing) {
                this.textContent = 'Follow';
                this.classList.remove('following');
                console.log(`Unfollowed ${artistName}`);
            } else {
                this.textContent = 'Following';
                this.classList.add('following');
                console.log(`Followed ${artistName}`);
            }
        });
    });
}

/**
 * Initialize Flickity carousels
 */
function initializeCarousels() {
    const carousels = document.querySelectorAll('.carousel');
    
    carousels.forEach(carousel => {
        // Flickity is initialized via data-flickity attribute in HTML
        // This is just to ensure all carousels are properly set up
        if (carousel.classList.contains('flickity-enabled')) {
            return;
        }
        
        // Add any additional carousel setup if needed
    });
}

/**
 * Filter items based on category
 * @param {string} filter - The filter category
 */
function filterItems(filter) {
    const items = document.querySelectorAll('.item');
    
    items.forEach(item => {
        if (filter === 'all' || item.getAttribute('data-type') === filter) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

/**
 * Search items based on search term using real API
 * @param {string} searchTerm - The search term
 */
async function searchItems(searchTerm) {
    console.log('searchItems called with:', searchTerm);

    if (!searchTerm || searchTerm.length < 2) {
        console.log('Search term too short or empty');
        return;
    }

    console.log('Starting search...');
    showLoader();

    try {
        console.log('Calling libraryAPI.searchMusic...');
        // Search using the iTunes API through our backend
        const searchResults = await libraryAPI.searchMusic(searchTerm, 20);
        console.log('Search results received:', searchResults);
        currentSearchResults = searchResults;

        // Clear existing content and show search results
        console.log('Displaying search results...');
        await displaySearchResults(searchResults, searchTerm);

        // Update search results count
        const resultsCount = document.querySelector('.results-count');
        if (resultsCount) {
            resultsCount.textContent = `${searchResults.length} results found for "${searchTerm}"`;
        }

        console.log('Search completed successfully');

    } catch (error) {
        console.error('Search failed:', error);
        console.error('Error stack:', error.stack);
        const resultsCount = document.querySelector('.results-count');
        if (resultsCount) {
            resultsCount.textContent = 'Search failed. Please try again.';
        }

        // Show error to user
        alert(`Search failed: ${error.message}`);
    } finally {
        hideLoader();
    }
}

/**
 * Display search results in the page
 * @param {Array} results - Array of search results
 * @param {string} searchTerm - The search term
 */
async function displaySearchResults(results, searchTerm) {
    // Get or create search results section
    let searchResultsSection = document.querySelector('.search-results-section');

    if (!searchResultsSection) {
        searchResultsSection = document.createElement('section');
        searchResultsSection.className = 'section search-results-section';

        // Insert after the search section
        const searchSection = document.querySelector('.search-section');
        searchSection.parentNode.insertBefore(searchResultsSection, searchSection.nextSibling);
    }

    // Hide other sections during search
    const otherSections = document.querySelectorAll('.featured-artists, .recommended-music');
    otherSections.forEach(section => {
        section.style.display = 'none';
    });

    // Create search results content
    searchResultsSection.innerHTML = `
        <div class="section-header">
            <h2>Search Results</h2>
            <button type="button" class="clear-search-btn">Clear Search</button>
        </div>
        <div class="search-results-grid" id="search-results-grid">
            ${results.length === 0 ? '<p class="no-results">No results found. Try a different search term.</p>' : ''}
        </div>
    `;

    // Add clear search functionality
    const clearSearchBtn = searchResultsSection.querySelector('.clear-search-btn');
    clearSearchBtn.addEventListener('click', resetSearch);

    if (results.length > 0) {
        const grid = searchResultsSection.querySelector('.search-results-grid');

        // Load user's playlists for the "Add to Playlist" functionality
        currentPlaylists = await libraryAPI.getPlaylists();

        results.forEach(song => {
            const songCard = createSongCard(song);
            grid.appendChild(songCard);
        });
    }
}

/**
 * Reset search results
 */
function resetSearch() {
    // Remove search results section
    const searchResultsSection = document.querySelector('.search-results-section');
    if (searchResultsSection) {
        searchResultsSection.remove();
    }

    // Show other sections again
    const otherSections = document.querySelectorAll('.featured-artists, .recommended-music');
    otherSections.forEach(section => {
        section.style.display = 'block';
    });

    // Reset results count
    const resultsCount = document.querySelector('.results-count');
    if (resultsCount) {
        resultsCount.textContent = '';
    }

    // Clear current search results
    currentSearchResults = [];
}

/**
 * Show loading spinner
 */
function showLoader() {
    const loader = document.getElementById('loader');
    if (loader) {
        loader.style.display = 'block';
    }
}

/**
 * Hide loading spinner
 */
function hideLoader() {
    const loader = document.getElementById('loader');
    if (loader) {
        loader.style.display = 'none';
    }
}

/**
 * Main function to load explore content
 */
async function loadExploreContent() {
    showLoader();

    try {
        // Simulate API calls with setTimeout
        setTimeout(() => {
            hideLoader();
        }, 500);
    } catch (error) {
        console.error('Error loading explore content:', error);
        hideLoader();
    }
}
