/* CSS Variables */
:root {
    /* Colors */
    --background-color: #0D1117;
    --text-color: #ffffff;
    --electric-violet: #6F00FF;
    --neon-blue: #00E0FF;
    --cosmic-pink: #FF006E;
    --cyber-lime: #A7FF4A;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    --accent-color: var(--cosmic-pink);
    
    /* Gradients */
    --button-gradient: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --header-gradient: linear-gradient(to right, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    --button-shine: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    
    /* Shadows */
    --button-hover-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --button-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    --card-shadow: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Spacing */
    --section-gap: 2rem;
    --container-padding: 20px;
    
    /* Border Radius */
    --border-radius-lg: 25px;
    --border-radius-md: 16px;
    --border-radius-sm: 12px;
    
    /* Transitions */
    --transition-speed: 0.3s;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Plus Jakarta Sans', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
    padding-top: 120px; /* Account for fixed header */
}

/* Navbar Styles */
header {
    background: var(--header-gradient);
    padding: 12px 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: var(--button-shadow);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo img {
    width: 90px;
    height: 90px;
    transition: transform var(--transition-speed);
}

.logo img:hover {
    transform: scale(1.05);
}

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-speed);
}

.menu a:hover,
.menu a[aria-current="page"] {
    color: var(--accent-color);
}

/* User Profile */
.user-profile {
    position: relative;
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all var(--transition-speed) ease;
    filter: brightness(0.95);
}

.profile-icon:hover {
    transform: scale(1.05);
    border-color: rgba(0, 224, 255, 0.3);
    box-shadow: 
        0 0 15px rgba(0, 224, 255, 0.2),
        0 0 30px rgba(0, 224, 255, 0.1);
    filter: brightness(1.1);
}

/* Dropdown Menu */
.dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: linear-gradient(
        135deg,
        var(--header-gradient-start),
        var(--header-gradient-end)
    );
    border-radius: 12px;
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.2),
        0 0 15px rgba(0, 224, 255, 0.1);
    backdrop-filter: blur(10px);
    transform-origin: top right;
}

.user-profile:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 8px;
}

.dropdown ul li {
    margin: 2px 0;
}

.dropdown ul li a {
    color: var(--text-color);
    text-decoration: none;
    padding: 10px 16px;
    display: block;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
}

.dropdown ul li a:hover {
    color: var(--neon-blue);
    text-shadow: 
        0 0 8px rgba(0, 224, 255, 0.3),
        0 0 16px rgba(0, 224, 255, 0.2);
    transform: translateX(2px);
}

.dropdown ul li a:hover::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(
        90deg,
        transparent,
        var(--neon-blue),
        transparent
    );
    animation: shimmer 2s linear infinite;
}

/* Button Styles */
.button,
.play-button,
.section .view-all,
.playlist-controls button,
.hero-content .cta-button {
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    box-shadow: var(--button-shadow);
}

/* Common hover effects */
.button:hover,
.play-button:hover,
.section .view-all:hover,
.playlist-controls button:hover,
.hero-content .cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 8px 25px rgba(0, 224, 255, 0.3),
        0 8px 25px rgba(255, 0, 110, 0.3);
}

/* Common active effects */
.button:active,
.play-button:active,
.section .view-all:active,
.playlist-controls button:active,
.hero-content .cta-button:active {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Shine effect */
.button::before,
.play-button::before,
.section .view-all::before,
.playlist-controls button::before,
.hero-content .cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--button-shine);
    transition: 0.5s;
}

/* Shine animation on hover */
.button:hover::before,
.play-button:hover::before,
.section .view-all:hover::before,
.playlist-controls button:hover::before,
.hero-content .cta-button:hover::before {
    left: 100%;
}

/* Button size variations */
.play-button {
    padding: 12px 25px;
    font-size: 1rem;
}

.section .view-all {
    padding: 10px 20px;
    font-size: 0.9rem;
}

.playlist-controls button {
    padding: 10px 20px;
    font-size: 0.9rem;
    margin: 0 5px;
}

/* Star Animation */
.stars {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
    opacity: 0.6;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background-color: white;
    border-radius: 50%;
    animation: twinkle 4s infinite;
    opacity: 0;
}

/* Hero Section */
.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    width: 100%;
    max-width: 800px;
    padding: 3rem;
    margin: 120px auto 0;
    background: rgba(13, 17, 23, 0.8);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.hero-content p {
    font-size: clamp(1.1rem, 2vw, 1.5rem);
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-content .cta-button {
    display: inline-block;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    color: white;
    background: var(--button-gradient);
    border-radius: 30px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: var(--button-hover-shadow);
}

.hero-content .cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3),
                0 8px 25px rgba(255, 0, 110, 0.3);
}

.hero-content .cta-button:active {
    transform: translateY(-1px);
}

/* Section Styles */
.section {
    margin: 60px 0;
    position: relative;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.section .view-all {
    font-size: 0.9rem;
    padding: 8px 20px;
}

.section .items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.section .item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    position: relative;
}

.section .item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.section .item img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform var(--transition-speed);
}

.section .item:hover img {
    transform: scale(1.05);
}

.section .item-info {
    padding: 15px;
}

.section .item-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 5px;
}

.section .item-subtitle {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--button-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 24px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 9999;
}

.back-to-top:hover {
    transform: translateY(-3px);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

/* Loader */
.loader {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(13, 17, 23, 0.8);
    z-index: 9999;
    justify-content: center;
    align-items: center;
}

.loader::after {
    content: "";
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--neon-blue);
    animation: spin 1s ease-in-out infinite;
}

/* Utility Classes */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Animations */
@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes twinkle {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(0.5);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes navShimmer {
    0% {
        background-position: 200% 0;
        box-shadow: 0 0 10px var(--neon-blue),
                   0 0 20px rgba(0, 224, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 15px var(--neon-blue),
                   0 0 25px rgba(0, 224, 255, 0.4);
    }
    100% {
        background-position: -200% 0;
        box-shadow: 0 0 10px var(--neon-blue),
                   0 0 20px rgba(0, 224, 255, 0.3);
    }
}

/* Media Queries - All consolidated at the bottom */
@media screen and (max-width: 1200px) {
    .container {
        max-width: 1140px;
        margin-left: auto;
        margin-right: auto;
    }
}

@media screen and (max-width: 768px) {
    .hero-content {
        margin: 100px auto 0;
        padding: 2rem;
        width: 90%;
    }
    
    .section .items {
        grid-template-columns: 1fr;
    }
    
    .section .item {
        min-width: 250px;
    }
    
    .container {
        width: 95%;
        padding: 10px;
    }
    
    /* Mobile-specific improvements */
    .menu a, 
    button,
    .item button,
    .back-to-top {
        min-height: 44px;  /* iOS minimum touch target */
        min-width: 44px;
        padding: 12px 20px;
    }

    /* Improve mobile navigation */
    .menu {
        display: none;
    }
    
    .menu.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--background-color);
        padding: 1rem;
    }

    .menu a {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 16px;
    }

    /* Optimize item layout for touch */
    .item {
        padding: 16px;
        margin: 12px;
        border-radius: 12px;  /* Slightly larger for better touch feel */
    }

    /* Ensure sufficient spacing between interactive elements */
    .user-profile {
        padding: 8px;
    }

    .dropdown {
        padding: 12px 0;
    }

    .dropdown a {
        padding: 12px 16px;
        display: block;
    }
    
    .quick-access {
        margin: -8px auto 0;
        padding: 1.5rem;
    }

    .quick-links {
        gap: 1.5rem;
    }
    
    .quick-link {
        min-width: 100px;
        max-width: 120px;
        padding: 1.25rem;
    }
    
    .quick-link img {
        width: 40px;
        height: 40px;
    }
}

@media screen and (max-width: 480px) {
    .hero-content {
        margin: 80px auto 0;
        padding: 1.5rem;
        width: 95%; /* Slightly wider on mobile */
    }
    
    .section .item {
        min-width: 220px;
    }
    
    .section h2 {
        font-size: 32px;
    }
    
    .quick-access {
        margin: -5px auto 0;
        padding: 1rem;
    }

    .quick-links {
        gap: 1rem;
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .quick-link {
        min-width: 90px;
        max-width: 100px;
        padding: 1rem;
        flex: 0 1 calc(33.333% - 1rem);
    }
    
    .quick-link img {
        width: 35px;
        height: 35px;
        margin-bottom: 0.75rem;
    }
    
    .quick-link span {
        font-size: 0.8rem;
    }
}

@media (prefers-reduced-motion: reduce) {
    .menu li a[aria-current="page"]::after {
        animation: none;
        background-position: 0 0;
    }
    
    .button::before,
    .play-button::before,
    .section .view-all::before,
    .playlist-controls button::before,
    .hero-content .cta-button::before {
        transition: none;
    }
    
    .star {
        animation: none;
    }
    
    .loader::after {
        animation: none;
    }
}
