/**
 * In-memory storage for testing when MongoDB is not available
 */

class MemoryStorage {
    constructor() {
        this.songs = new Map();
        this.playlists = new Map();
        this.libraries = new Map();
        this.initializeSampleData();
    }

    initializeSampleData() {
        // Sample songs
        const sampleSongs = [
            {
                id: 'song_1',
                title: 'Blinding Lights',
                artist: 'The Weeknd',
                album: 'After Hours',
                duration: 200,
                coverUrl: 'imgs/album-01.png',
                genre: ['Pop', 'R&B'],
                releaseDate: '2020-03-20',
                popularity: 95,
                uploadedBy: 'user_123'
            },
            {
                id: 'song_2',
                title: 'Levitating',
                artist: '<PERSON><PERSON><PERSON>',
                album: 'Future Nostalgia',
                duration: 203,
                coverUrl: 'imgs/album-02.png',
                genre: ['Pop', 'Dance'],
                releaseDate: '2020-10-01',
                popularity: 92,
                uploadedBy: 'user_123'
            },
            {
                id: 'song_3',
                title: 'Save Your Tears',
                artist: 'The Weeknd',
                album: 'After Hours',
                duration: 215,
                coverUrl: 'imgs/album-01.png',
                genre: ['Pop', 'R&B'],
                releaseDate: '2020-03-20',
                popularity: 90,
                uploadedBy: 'user_123'
            },
            {
                id: 'song_4',
                title: 'Good 4 U',
                artist: '<PERSON>',
                album: 'SOUR',
                duration: 178,
                coverUrl: 'imgs/album-03.png',
                genre: ['Pop', 'Rock'],
                releaseDate: '2021-05-14',
                popularity: 88,
                uploadedBy: 'user_123'
            },
            {
                id: 'song_5',
                title: 'Construyendo en el Espacio',
                artist: 'Luna Cantina',
                album: 'Cosmic Dreams',
                duration: 225,
                coverUrl: 'imgs/album-02.png',
                audioUrl: './mp3/Construyendo en el Espacio - Luna Cantina.mp3',
                genre: ['Alternative', 'Indie'],
                releaseDate: '2023-01-15',
                popularity: 75,
                uploadedBy: 'user_123'
            }
        ];

        // Sample playlists
        const samplePlaylists = [
            {
                id: 'playlist_1',
                name: 'My Favorites',
                description: 'A collection of my all-time favorite songs',
                coverUrl: 'imgs/album-01.png',
                createdBy: 'user_123',
                songs: [
                    { songId: 'song_1', addedBy: 'user_123', addedAt: new Date() },
                    { songId: 'song_2', addedBy: 'user_123', addedAt: new Date() },
                    { songId: 'song_5', addedBy: 'user_123', addedAt: new Date() }
                ],
                isPublic: true,
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                id: 'playlist_2',
                name: 'Chill Vibes',
                description: 'Perfect for relaxing and unwinding',
                coverUrl: 'imgs/album-02.png',
                createdBy: 'user_123',
                songs: [
                    { songId: 'song_3', addedBy: 'user_123', addedAt: new Date() },
                    { songId: 'song_5', addedBy: 'user_123', addedAt: new Date() }
                ],
                isPublic: true,
                createdAt: new Date(),
                updatedAt: new Date()
            }
        ];

        // Sample library
        const sampleLibrary = {
            userId: 'user_123',
            songs: sampleSongs.map(song => ({
                songId: song.id,
                addedAt: new Date(),
                playCount: Math.floor(Math.random() * 50),
                isFavorite: ['song_1', 'song_2', 'song_5'].includes(song.id),
                lastPlayedAt: new Date()
            })),
            playlists: samplePlaylists.map(playlist => ({
                playlistId: playlist.id,
                addedAt: new Date(),
                isOwned: true,
                isFollowed: false
            })),
            recentlyPlayed: [
                { songId: 'song_5', playedAt: new Date(Date.now() - 1000 * 60 * 30) },
                { songId: 'song_1', playedAt: new Date(Date.now() - 1000 * 60 * 60) },
                { songId: 'song_2', playedAt: new Date(Date.now() - 1000 * 60 * 120) }
            ],
            createdAt: new Date(),
            updatedAt: new Date()
        };

        // Store in memory
        sampleSongs.forEach(song => this.songs.set(song.id, song));
        samplePlaylists.forEach(playlist => this.playlists.set(playlist.id, playlist));
        this.libraries.set('user_123', sampleLibrary);

        console.log('Memory storage initialized with sample data');
    }

    // Song operations
    getSong(id) {
        return this.songs.get(id);
    }

    getAllSongs() {
        return Array.from(this.songs.values());
    }

    addSong(song) {
        this.songs.set(song.id, { ...song, createdAt: new Date(), updatedAt: new Date() });
        return this.songs.get(song.id);
    }

    updateSong(id, updates) {
        const song = this.songs.get(id);
        if (song) {
            const updatedSong = { ...song, ...updates, updatedAt: new Date() };
            this.songs.set(id, updatedSong);
            return updatedSong;
        }
        return null;
    }

    deleteSong(id) {
        return this.songs.delete(id);
    }

    // Playlist operations
    getPlaylist(id) {
        return this.playlists.get(id);
    }

    getAllPlaylists() {
        return Array.from(this.playlists.values());
    }

    addPlaylist(playlist) {
        const newPlaylist = { 
            ...playlist, 
            songs: playlist.songs || [],
            createdAt: new Date(), 
            updatedAt: new Date() 
        };
        this.playlists.set(playlist.id, newPlaylist);
        return newPlaylist;
    }

    updatePlaylist(id, updates) {
        const playlist = this.playlists.get(id);
        if (playlist) {
            const updatedPlaylist = { ...playlist, ...updates, updatedAt: new Date() };
            this.playlists.set(id, updatedPlaylist);
            return updatedPlaylist;
        }
        return null;
    }

    deletePlaylist(id) {
        return this.playlists.delete(id);
    }

    addSongToPlaylist(playlistId, songId, userId) {
        const playlist = this.playlists.get(playlistId);
        if (playlist) {
            // Check if song already exists
            const existingSong = playlist.songs.find(s => s.songId === songId);
            if (!existingSong) {
                playlist.songs.push({
                    songId: songId,
                    addedBy: userId,
                    addedAt: new Date()
                });
                playlist.updatedAt = new Date();
                this.playlists.set(playlistId, playlist);
            }
            return playlist;
        }
        return null;
    }

    removeSongFromPlaylist(playlistId, songId) {
        const playlist = this.playlists.get(playlistId);
        if (playlist) {
            playlist.songs = playlist.songs.filter(s => s.songId !== songId);
            playlist.updatedAt = new Date();
            this.playlists.set(playlistId, playlist);
            return playlist;
        }
        return null;
    }

    // Library operations
    getLibrary(userId) {
        return this.libraries.get(userId);
    }

    createLibrary(userId) {
        const library = {
            userId: userId,
            songs: [],
            playlists: [],
            recentlyPlayed: [],
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.libraries.set(userId, library);
        return library;
    }

    addSongToLibrary(userId, songId) {
        let library = this.libraries.get(userId);
        if (!library) {
            library = this.createLibrary(userId);
        }

        // Check if song already exists
        const existingSong = library.songs.find(s => s.songId === songId);
        if (!existingSong) {
            library.songs.push({
                songId: songId,
                addedAt: new Date(),
                playCount: 0,
                isFavorite: false
            });
            library.updatedAt = new Date();
            this.libraries.set(userId, library);
        }
        return library;
    }

    toggleFavorite(userId, songId) {
        const library = this.libraries.get(userId);
        if (library) {
            const song = library.songs.find(s => s.songId === songId);
            if (song) {
                song.isFavorite = !song.isFavorite;
                library.updatedAt = new Date();
                this.libraries.set(userId, library);
                return library;
            }
        }
        return null;
    }

    recordPlay(userId, songId, duration = 0) {
        const library = this.libraries.get(userId);
        if (library) {
            // Update play count
            const song = library.songs.find(s => s.songId === songId);
            if (song) {
                song.playCount += 1;
                song.lastPlayedAt = new Date();
            }

            // Add to recently played
            library.recentlyPlayed.unshift({
                songId: songId,
                playedAt: new Date()
            });

            // Keep only last 50
            if (library.recentlyPlayed.length > 50) {
                library.recentlyPlayed = library.recentlyPlayed.slice(0, 50);
            }

            library.updatedAt = new Date();
            this.libraries.set(userId, library);
            return library;
        }
        return null;
    }
}

// Create singleton instance
const memoryStorage = new MemoryStorage();

module.exports = memoryStorage;
