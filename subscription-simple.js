// Simple version of subscription.js without modules
document.addEventListener('DOMContentLoaded', function() {
    console.log('subscription-simple.js loaded');

    // Find all subscribe buttons (now using .button class with data-plan attribute)
    const subscribeBtns = document.querySelectorAll('.button[data-plan]');
    console.log('Found subscribe buttons:', subscribeBtns.length);

    // Add click event listeners
    subscribeBtns.forEach(btn => {
        btn.addEventListener('click', handleSubscribe);
    });

    // Function to handle subscription
    async function handleSubscribe(event) {
        console.log('Subscribe button clicked');
        const plan = event.target.dataset.plan;
        console.log('Selected plan:', plan);

        // Add loading class to button
        const button = event.target;
        button.classList.add('loading');
        console.log('Added loading class to button');

        try {
            // Show loader
            showLoader();

            // Simulate API call
            console.log('Simulating API call...');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Generate a mock subscription ID
            const subscriptionId = 'sub_' + Math.random().toString(36).substring(2);
            console.log('Generated subscriptionId:', subscriptionId);

            // Create a mock plan object
            const selectedPlan = {
                id: plan,
                name: plan === 'free' ? 'Free Account' : (plan === 'premium' ? 'Premium' : 'Artist Account'),
                price: plan === 'free' ? 0 : (plan === 'premium' ? 2.99 : 4.99),
                interval: 'month',
                features: [
                    plan === 'free' ? 'Basic audio quality' : 'High-quality audio',
                    plan === 'free' ? 'Limited skips' : 'Unlimited skips',
                    plan !== 'free' ? 'Offline mode' : null,
                    plan === 'artist' ? 'Upload unlimited tracks' : null
                ].filter(Boolean)
            };

            // Save selected plan to session storage
            console.log('Saving plan to session storage:', selectedPlan);
            sessionStorage.setItem('selectedPlan', JSON.stringify(selectedPlan));
            sessionStorage.setItem('subscriptionId', subscriptionId);

            // Hide loader
            hideLoader();

            // Show success message
            showToast(`Successfully selected ${selectedPlan.name}!`);

            // Redirect based on the plan type
            console.log('Will redirect to:', plan === 'free' ? 'confirmation.html' : 'payment.html');
            setTimeout(() => {
                if (plan === 'free') {
                    // For free plan, go directly to confirmation
                    window.location.href = 'confirmation.html';
                } else {
                    // For paid plans, go to payment page
                    window.location.href = 'payment.html';
                }
            }, 1500);
        } catch (error) {
            console.error('Subscription error:', error);
            showToast('An error occurred during subscription. Please try again.', 'error');
        } finally {
            console.log('Removing loading class from button');
            button.classList.remove('loading');
            hideLoader();
        }
    }

    // Function to show toast message
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    // Function to show loader
    function showLoader() {
        const loader = document.getElementById('api-loader');
        if (loader) {
            loader.style.display = 'flex';
        }
    }

    // Function to hide loader
    function hideLoader() {
        const loader = document.getElementById('api-loader');
        if (loader) {
            loader.style.display = 'none';
        }
    }
});
