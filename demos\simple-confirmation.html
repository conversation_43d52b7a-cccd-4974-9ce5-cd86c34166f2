<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Confirmed - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Plus Jakarta Sans', sans-serif;
            background-color: #1c1c1e;
            color: #eaeaea;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .confirmation-container {
            background: linear-gradient(315deg, rgba(13, 17, 23, 0.95), rgba(25, 32, 44, 0.95));
            border-radius: 16px;
            padding: 3rem;
            width: 90%;
            max-width: 800px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }
        
        .success-animation {
            margin-bottom: 2rem;
        }
        
        .success-animation i {
            font-size: 5rem;
            color: #4CAF50;
            animation: scaleIn 0.5s ease-out;
        }
        
        @keyframes scaleIn {
            0% { transform: scale(0); opacity: 0; }
            70% { transform: scale(1.2); opacity: 1; }
            100% { transform: scale(1); opacity: 1; }
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .confirmation-details {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .subscription-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: left;
        }
        
        .subscription-info h2 {
            margin-top: 0;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 0.5rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .label {
            font-size: 0.9rem;
            color: #aaa;
            margin-bottom: 0.5rem;
        }
        
        .value {
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .features-preview {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: left;
        }
        
        .features-preview h2 {
            margin-top: 0;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 0.5rem;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .feature-list li {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .feature-list li:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }
        
        .feature-list li i {
            margin-right: 1rem;
            color: #00E0FF;
            font-size: 1.2rem;
        }
        
        .receipt-info {
            margin-top: 1.5rem;
            color: #aaa;
            font-size: 0.9rem;
        }
        
        .receipt-info p {
            margin-bottom: 1rem;
        }
        
        .receipt-info span {
            color: #eaeaea;
            font-weight: 600;
        }
        
        .download-receipt {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: #eaeaea;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .download-receipt i {
            margin-right: 0.5rem;
        }
        
        .download-receipt:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .primary-button, .secondary-button {
            display: flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .primary-button {
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            color: white;
            border: none;
        }
        
        .secondary-button {
            background: rgba(255, 255, 255, 0.1);
            color: #eaeaea;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .primary-button:hover, .secondary-button:hover {
            transform: translateY(-2px);
        }
        
        .primary-button:hover {
            box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
        }
        
        .secondary-button:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .primary-button i, .secondary-button i {
            margin-right: 0.5rem;
        }
        
        .toast {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 1rem 2rem;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        
        @media (max-width: 768px) {
            .confirmation-container {
                padding: 2rem;
                width: 95%;
            }
            
            .info-grid, .feature-list {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
                gap: 1rem;
            }
            
            .primary-button, .secondary-button {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="confirmation-container">
        <div class="success-animation">
            <i class="fas fa-check-circle"></i>
        </div>
        
        <h1>Welcome to Banshee Premium!</h1>
        
        <div class="confirmation-details">
            <div class="subscription-info">
                <h2>Subscription Details</h2>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">Plan</span>
                        <span class="value" id="planName">Premium</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Billing Cycle</span>
                        <span class="value" id="billingCycle">Monthly</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Next Billing Date</span>
                        <span class="value" id="nextBilling">March 14, 2024</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Amount</span>
                        <span class="value" id="amount">$2.99/month</span>
                    </div>
                </div>
            </div>

            <div class="features-preview">
                <h2>What's Next?</h2>
                <ul class="feature-list" id="featuresList">
                    <li>
                        <i class="fas fa-music"></i>
                        <span>Enjoy ad-free music streaming</span>
                    </li>
                    <li>
                        <i class="fas fa-download"></i>
                        <span>Download unlimited songs for offline listening</span>
                    </li>
                    <li>
                        <i class="fas fa-headphones"></i>
                        <span>Experience HD audio quality</span>
                    </li>
                    <li>
                        <i class="fas fa-mobile-alt"></i>
                        <span>Listen on any device</span>
                    </li>
                </ul>
            </div>

            <div class="receipt-info">
                <p>A confirmation email has been sent to <span id="userEmail"><EMAIL></span></p>
                <button class="download-receipt">
                    <i class="fas fa-download"></i>
                    Download Receipt
                </button>
            </div>
        </div>

        <div class="action-buttons">
            <a href="index.html" class="primary-button">
                <i class="fas fa-play"></i>
                Start Listening
            </a>
            <a href="simple-subscription.html" class="secondary-button">
                <i class="fas fa-cog"></i>
                Manage Subscription
            </a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Confirmation page loaded');
            
            // Load subscription details
            loadConfirmationDetails();
            
            // Initialize receipt download
            document.querySelector('.download-receipt').addEventListener('click', generateReceipt);
        });
        
        function loadConfirmationDetails() {
            // Get data from session storage
            const subscriptionId = sessionStorage.getItem('subscriptionId');
            const transactionId = sessionStorage.getItem('transactionId');
            const nextBillingDate = sessionStorage.getItem('nextBillingDate');
            const selectedPlanJson = sessionStorage.getItem('selectedPlan');
            
            if (!subscriptionId || !selectedPlanJson) {
                // If no subscription data, redirect to subscription page
                window.location.href = 'simple-subscription.html';
                return;
            }
            
            try {
                // Parse selected plan
                const selectedPlan = JSON.parse(selectedPlanJson);
                
                // Update UI with subscription details
                document.getElementById('planName').textContent = selectedPlan.name;
                document.getElementById('billingCycle').textContent = 
                    selectedPlan.interval === 'month' ? 'Monthly' : 'Yearly';
                
                // Format next billing date
                const nextBillingDateObj = new Date(nextBillingDate || new Date().setDate(new Date().getDate() + 30));
                document.getElementById('nextBilling').textContent = 
                    nextBillingDateObj.toLocaleDateString('en-US', { 
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric' 
                    });
                
                // Update amount
                document.getElementById('amount').textContent = 
                    `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;
                
                // Update page title based on plan
                const pageTitle = document.querySelector('h1');
                if (pageTitle) {
                    pageTitle.textContent = `Welcome to Banshee ${selectedPlan.name}!`;
                }
                
                // Update features based on plan
                updateFeaturesList(selectedPlan.id);
                
            } catch (error) {
                console.error('Error loading confirmation details:', error);
                showError('There was an error loading your subscription details.');
            }
        }
        
        function updateFeaturesList(planId) {
            // This would be more dynamic in a real application
            const featuresList = document.getElementById('featuresList');
            
            if (!featuresList) return;
            
            // Clear existing features
            featuresList.innerHTML = '';
            
            // Add features based on plan
            const features = [];
            
            if (planId === 'free') {
                features.push(
                    { icon: 'music', text: 'Basic music streaming with ads' },
                    { icon: 'headphones', text: 'Standard audio quality' },
                    { icon: 'mobile-alt', text: 'Mobile app access' }
                );
            } else if (planId === 'premium') {
                features.push(
                    { icon: 'music', text: 'Enjoy ad-free music streaming' },
                    { icon: 'download', text: 'Download unlimited songs for offline listening' },
                    { icon: 'headphones', text: 'Experience HD audio quality' },
                    { icon: 'mobile-alt', text: 'Listen on any device' }
                );
            } else if (planId === 'artist') {
                features.push(
                    { icon: 'music', text: 'All Premium features included' },
                    { icon: 'upload', text: 'Upload unlimited tracks' },
                    { icon: 'chart-line', text: 'Access advanced analytics dashboard' },
                    { icon: 'bullhorn', text: 'Use promotional tools' }
                );
            }
            
            // Add features to list
            features.forEach(feature => {
                const li = document.createElement('li');
                li.innerHTML = `
                    <i class="fas fa-${feature.icon}"></i>
                    <span>${feature.text}</span>
                `;
                featuresList.appendChild(li);
            });
        }
        
        function generateReceipt() {
            // In a real application, this would generate a PDF receipt
            // For this demo, we'll just show a message
            showToast('Receipt downloaded successfully!');
        }
        
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
        
        function showError(message) {
            showToast(message);
            console.error(message);
        }
    </script>
</body>
</html>
