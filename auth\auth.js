/**
 * Authentication functionality for Banshee Music App
 */

// DOM Elements
const loginForm = document.getElementById('login-form');
const registerForm = document.getElementById('register-form');
const resetForm = document.getElementById('reset-form');
const newPasswordForm = document.getElementById('new-password-form');

const loginError = document.getElementById('login-error');
const registerError = document.getElementById('register-error');
const resetError = document.getElementById('reset-error');
const resetSuccess = document.getElementById('reset-success');
const newPasswordError = document.getElementById('new-password-error');

const resetSentSection = document.getElementById('reset-sent');
const passwordResetSuccessSection = document.getElementById('password-reset-success');

const resendLink = document.getElementById('resend-link');

// Toggle password visibility
const togglePasswordButtons = document.querySelectorAll('.toggle-password');
togglePasswordButtons.forEach(button => {
    button.addEventListener('click', function() {
        const input = this.parentElement.querySelector('input');
        const icon = this.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
});

// Password strength meter
const passwordInputs = document.querySelectorAll('input[type="password"][id="password"]');
passwordInputs.forEach(input => {
    input.addEventListener('input', function() {
        const password = this.value;
        const strengthMeter = this.parentElement.parentElement.querySelector('.password-strength');
        
        if (!strengthMeter) return;
        
        const segments = strengthMeter.querySelectorAll('.strength-segment');
        const strengthText = strengthMeter.querySelector('.strength-text');
        
        // Reset segments
        segments.forEach(segment => {
            segment.className = 'strength-segment';
        });
        
        // Calculate password strength
        let strength = 0;
        
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength++;
        if (password.match(/\d/)) strength++;
        if (password.match(/[^a-zA-Z\d]/)) strength++;
        
        // Update segments based on strength
        for (let i = 0; i < strength; i++) {
            if (segments[i]) {
                if (strength === 1) segments[i].classList.add('weak');
                else if (strength === 2) segments[i].classList.add('medium');
                else if (strength === 3) segments[i].classList.add('strong');
                else if (strength === 4) segments[i].classList.add('very-strong');
            }
        }
        
        // Update strength text
        if (strengthText) {
            if (strength === 0) strengthText.textContent = 'Password strength';
            else if (strength === 1) strengthText.textContent = 'Weak';
            else if (strength === 2) strengthText.textContent = 'Medium';
            else if (strength === 3) strengthText.textContent = 'Strong';
            else if (strength === 4) strengthText.textContent = 'Very Strong';
        }
    });
});

// Login form submission
if (loginForm) {
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const remember = document.getElementById('remember')?.checked || false;
        
        // Clear previous errors
        if (loginError) loginError.textContent = '';
        
        // Validate form
        if (!email || !password) {
            showError(loginError, 'Please fill in all fields');
            return;
        }
        
        // Show loading state
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Logging in...';
        submitButton.disabled = true;
        
        // Simulate API call
        setTimeout(() => {
            // Check if user exists in localStorage
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const user = users.find(u => u.email === email);
            
            if (!user || user.password !== password) {
                showError(loginError, 'Invalid email or password');
                submitButton.textContent = originalText;
                submitButton.disabled = false;
                return;
            }
            
            // Login successful
            const userData = {
                id: user.id,
                name: user.name,
                email: user.email,
                isLoggedIn: true,
                loginTime: new Date().toISOString()
            };
            
            // Store user data in localStorage
            localStorage.setItem('currentUser', JSON.stringify(userData));
            
            // If remember me is checked, store in localStorage, otherwise in sessionStorage
            if (remember) {
                localStorage.setItem('authToken', generateToken());
            } else {
                sessionStorage.setItem('authToken', generateToken());
            }
            
            // Redirect to home page
            window.location.href = '../index.html';
        }, 1500);
    });
}

// Register form submission
if (registerForm) {
    registerForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const name = document.getElementById('name').value;
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        const termsAgreed = document.getElementById('terms')?.checked || false;
        
        // Clear previous errors
        if (registerError) registerError.textContent = '';
        
        // Validate form
        if (!name || !email || !password || !confirmPassword) {
            showError(registerError, 'Please fill in all fields');
            return;
        }
        
        if (password !== confirmPassword) {
            showError(registerError, 'Passwords do not match');
            return;
        }
        
        if (!termsAgreed) {
            showError(registerError, 'You must agree to the Terms of Service and Privacy Policy');
            return;
        }
        
        // Validate password strength
        let strength = 0;
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength++;
        if (password.match(/\d/)) strength++;
        if (password.match(/[^a-zA-Z\d]/)) strength++;
        
        if (strength < 3) {
            showError(registerError, 'Password is too weak. It should be at least 8 characters long and include uppercase, lowercase, numbers, and special characters.');
            return;
        }
        
        // Show loading state
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Creating Account...';
        submitButton.disabled = true;
        
        // Simulate API call
        setTimeout(() => {
            // Check if user already exists
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const existingUser = users.find(u => u.email === email);
            
            if (existingUser) {
                showError(registerError, 'An account with this email already exists');
                submitButton.textContent = originalText;
                submitButton.disabled = false;
                return;
            }
            
            // Create new user
            const newUser = {
                id: generateUserId(),
                name,
                email,
                password,
                createdAt: new Date().toISOString()
            };
            
            // Add user to localStorage
            users.push(newUser);
            localStorage.setItem('users', JSON.stringify(users));
            
            // Login the user
            const userData = {
                id: newUser.id,
                name: newUser.name,
                email: newUser.email,
                isLoggedIn: true,
                loginTime: new Date().toISOString()
            };
            
            localStorage.setItem('currentUser', JSON.stringify(userData));
            sessionStorage.setItem('authToken', generateToken());
            
            // Redirect to home page
            window.location.href = '../index.html';
        }, 1500);
    });
}

// Reset password form submission
if (resetForm) {
    resetForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        
        // Clear previous messages
        if (resetError) resetError.textContent = '';
        if (resetSuccess) resetSuccess.textContent = '';
        
        // Validate form
        if (!email) {
            showError(resetError, 'Please enter your email address');
            return;
        }
        
        // Show loading state
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Sending...';
        submitButton.disabled = true;
        
        // Simulate API call
        setTimeout(() => {
            // Check if user exists
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const user = users.find(u => u.email === email);
            
            if (!user) {
                showError(resetError, 'No account found with this email address');
                submitButton.textContent = originalText;
                submitButton.disabled = false;
                return;
            }
            
            // Generate reset token
            const resetToken = generateToken();
            
            // Store reset token in localStorage
            const resetTokens = JSON.parse(localStorage.getItem('resetTokens') || '{}');
            resetTokens[email] = {
                token: resetToken,
                expiresAt: new Date(Date.now() + 3600000).toISOString() // 1 hour expiry
            };
            localStorage.setItem('resetTokens', JSON.stringify(resetTokens));
            
            // In a real app, this would send an email with a reset link
            console.log(`Reset link: ${window.location.origin}/auth/new-password.html?token=${resetToken}&email=${encodeURIComponent(email)}`);
            
            // Show success message
            if (resetSentSection) {
                resetForm.parentElement.classList.add('hidden');
                resetSentSection.classList.remove('hidden');
            } else {
                showSuccess(resetSuccess, 'Password reset link sent to your email');
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }
        }, 1500);
    });
}

// Resend reset link
if (resendLink) {
    resendLink.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Get email from form
        const email = document.getElementById('email')?.value;
        
        if (!email) {
            alert('Please enter your email address first');
            return;
        }
        
        // Show loading state
        this.textContent = 'Sending...';
        
        // Simulate API call
        setTimeout(() => {
            // Generate new reset token
            const resetToken = generateToken();
            
            // Store reset token in localStorage
            const resetTokens = JSON.parse(localStorage.getItem('resetTokens') || '{}');
            resetTokens[email] = {
                token: resetToken,
                expiresAt: new Date(Date.now() + 3600000).toISOString() // 1 hour expiry
            };
            localStorage.setItem('resetTokens', JSON.stringify(resetTokens));
            
            // In a real app, this would send an email with a reset link
            console.log(`Reset link: ${window.location.origin}/auth/new-password.html?token=${resetToken}&email=${encodeURIComponent(email)}`);
            
            // Show success message
            alert('Password reset link has been resent to your email');
            this.textContent = 'resend the link';
        }, 1500);
    });
}

// New password form submission
if (newPasswordForm) {
    // Get token and email from URL
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const email = urlParams.get('email');
    
    // Set token in hidden field
    const tokenInput = document.getElementById('reset-token');
    if (tokenInput) tokenInput.value = token;
    
    // Validate token
    if (!token || !email) {
        showError(newPasswordError, 'Invalid or expired password reset link');
        newPasswordForm.style.display = 'none';
        return;
    }
    
    // Check if token is valid
    const resetTokens = JSON.parse(localStorage.getItem('resetTokens') || '{}');
    const resetData = resetTokens[email];
    
    if (!resetData || resetData.token !== token || new Date(resetData.expiresAt) < new Date()) {
        showError(newPasswordError, 'Invalid or expired password reset link');
        newPasswordForm.style.display = 'none';
        return;
    }
    
    // Form submission
    newPasswordForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm-password').value;
        
        // Clear previous errors
        if (newPasswordError) newPasswordError.textContent = '';
        
        // Validate form
        if (!password || !confirmPassword) {
            showError(newPasswordError, 'Please fill in all fields');
            return;
        }
        
        if (password !== confirmPassword) {
            showError(newPasswordError, 'Passwords do not match');
            return;
        }
        
        // Validate password strength
        let strength = 0;
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength++;
        if (password.match(/\d/)) strength++;
        if (password.match(/[^a-zA-Z\d]/)) strength++;
        
        if (strength < 3) {
            showError(newPasswordError, 'Password is too weak. It should be at least 8 characters long and include uppercase, lowercase, numbers, and special characters.');
            return;
        }
        
        // Show loading state
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Setting Password...';
        submitButton.disabled = true;
        
        // Simulate API call
        setTimeout(() => {
            // Update user password
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            const userIndex = users.findIndex(u => u.email === email);
            
            if (userIndex === -1) {
                showError(newPasswordError, 'User not found');
                submitButton.textContent = originalText;
                submitButton.disabled = false;
                return;
            }
            
            users[userIndex].password = password;
            localStorage.setItem('users', JSON.stringify(users));
            
            // Remove reset token
            const resetTokens = JSON.parse(localStorage.getItem('resetTokens') || '{}');
            delete resetTokens[email];
            localStorage.setItem('resetTokens', JSON.stringify(resetTokens));
            
            // Show success message
            if (passwordResetSuccessSection) {
                newPasswordForm.parentElement.classList.add('hidden');
                passwordResetSuccessSection.classList.remove('hidden');
            } else {
                alert('Password has been reset successfully. You can now login with your new password.');
                window.location.href = 'login.html';
            }
        }, 1500);
    });
}

// Social login buttons
const socialButtons = document.querySelectorAll('.social-button');
socialButtons.forEach(button => {
    button.addEventListener('click', function() {
        const provider = this.classList.contains('google') ? 'Google' : 
                         this.classList.contains('facebook') ? 'Facebook' : 
                         this.classList.contains('apple') ? 'Apple' : '';
        
        alert(`${provider} login is not implemented in this demo.`);
    });
});

// Helper functions
function showError(element, message) {
    if (!element) return;
    
    element.textContent = message;
    element.style.display = 'block';
    element.classList.add('visible');
    
    // Scroll to error
    element.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

function showSuccess(element, message) {
    if (!element) return;
    
    element.textContent = message;
    element.style.display = 'block';
    element.classList.add('visible');
}

function generateToken() {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
}

function generateUserId() {
    return 'user_' + Date.now().toString(36) + 
           Math.random().toString(36).substring(2, 9);
}

// Check if user is logged in
function isLoggedIn() {
    const currentUser = localStorage.getItem('currentUser');
    const authToken = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
    
    return !!(currentUser && authToken);
}

// Redirect if already logged in
if (isLoggedIn() && 
    (window.location.pathname.includes('login.html') || 
     window.location.pathname.includes('register.html'))) {
    window.location.href = '../index.html';
}

// Initialize demo users if none exist
function initializeDemoUsers() {
    const users = JSON.parse(localStorage.getItem('users') || '[]');
    
    if (users.length === 0) {
        const demoUsers = [
            {
                id: 'user_demo1',
                name: 'Demo User',
                email: '<EMAIL>',
                password: 'Password123!',
                createdAt: new Date().toISOString()
            }
        ];
        
        localStorage.setItem('users', JSON.stringify(demoUsers));
        console.log('Demo users initialized');
    }
}

// Initialize demo users
initializeDemoUsers();
