/**
 * UI components for music library management
 */

import libraryAPI from './api.js';
import { Playlist, Song } from './models.js';

/**
 * Library UI class for rendering music library components
 */
class LibraryUI {
    constructor() {
        this.currentPlaylist = null;
    }

    /**
     * Initialize the library UI
     */
    initialize() {
        this.renderPlaylists();
        this.renderFavorites();
        this.renderRecentlyPlayed();
        this.setupEventListeners();
    }

    /**
     * Set up event listeners for library UI components
     */
    setupEventListeners() {
        // Create playlist button
        const createPlaylistBtn = document.getElementById('create-playlist-btn');
        if (createPlaylistBtn) {
            createPlaylistBtn.addEventListener('click', () => this.showCreatePlaylistModal());
        }

        // New playlist form
        const newPlaylistForm = document.getElementById('new-playlist-form');
        if (newPlaylistForm) {
            newPlaylistForm.addEventListener('submit', (e) => this.handleCreatePlaylist(e));
        }
    }

    /**
     * Render the playlists in the sidebar
     */
    renderPlaylists() {
        const playlistsContainer = document.getElementById('playlists-container');
        if (!playlistsContainer) return;

        const playlists = libraryAPI.getPlaylists();
        
        // Clear the container
        playlistsContainer.innerHTML = '';
        
        // Add each playlist to the container
        playlists.forEach(playlist => {
            const playlistItem = document.createElement('div');
            playlistItem.className = 'playlist-item';
            playlistItem.dataset.id = playlist.id;
            playlistItem.innerHTML = `
                <div class="playlist-item-cover">
                    <img src="${playlist.coverUrl}" alt="${playlist.name}" loading="lazy">
                </div>
                <div class="playlist-item-info">
                    <h3>${playlist.name}</h3>
                    <p>${playlist.songs.length} songs</p>
                </div>
            `;
            
            // Add click event to show the playlist
            playlistItem.addEventListener('click', () => this.showPlaylist(playlist.id));
            
            playlistsContainer.appendChild(playlistItem);
        });
    }

    /**
     * Render the favorites section
     */
    renderFavorites() {
        const favoritesContainer = document.getElementById('favorites-container');
        if (!favoritesContainer) return;

        const favorites = libraryAPI.getFavorites();
        
        // Clear the container
        favoritesContainer.innerHTML = '';
        
        if (favorites.length === 0) {
            favoritesContainer.innerHTML = '<p class="empty-message">No favorite songs yet.</p>';
            return;
        }
        
        // Create a table for the songs
        const table = document.createElement('table');
        table.className = 'songs-table';
        
        // Add table header
        table.innerHTML = `
            <thead>
                <tr>
                    <th>#</th>
                    <th>Title</th>
                    <th>Artist</th>
                    <th>Album</th>
                    <th>Duration</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody></tbody>
        `;
        
        const tbody = table.querySelector('tbody');
        
        // Add each song to the table
        favorites.forEach((song, index) => {
            const row = document.createElement('tr');
            row.dataset.id = song.id;
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>
                    <div class="song-info">
                        <img src="${song.coverUrl}" alt="${song.title}" loading="lazy">
                        <span>${song.title}</span>
                    </div>
                </td>
                <td>${song.artist}</td>
                <td>${song.album}</td>
                <td>${song.formattedDuration()}</td>
                <td>
                    <button class="play-btn" aria-label="Play ${song.title}">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="add-to-queue-btn" aria-label="Add ${song.title} to queue">
                        <i class="fas fa-list"></i>
                    </button>
                    <button class="remove-favorite-btn" aria-label="Remove ${song.title} from favorites">
                        <i class="fas fa-heart-broken"></i>
                    </button>
                </td>
            `;
            
            // Add event listeners for the buttons
            const playBtn = row.querySelector('.play-btn');
            playBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.playSong(song);
            });
            
            const addToQueueBtn = row.querySelector('.add-to-queue-btn');
            addToQueueBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                libraryAPI.addToQueue(song);
                this.showToast(`Added "${song.title}" to queue`);
            });
            
            const removeFavoriteBtn = row.querySelector('.remove-favorite-btn');
            removeFavoriteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                libraryAPI.removeFromFavorites(song.id);
                this.renderFavorites();
                this.showToast(`Removed "${song.title}" from favorites`);
            });
            
            tbody.appendChild(row);
        });
        
        favoritesContainer.appendChild(table);
    }

    /**
     * Render the recently played section
     */
    renderRecentlyPlayed() {
        const recentlyPlayedContainer = document.getElementById('recently-played-container');
        if (!recentlyPlayedContainer) return;

        const recentlyPlayed = libraryAPI.getRecentlyPlayed();
        
        // Clear the container
        recentlyPlayedContainer.innerHTML = '';
        
        if (recentlyPlayed.length === 0) {
            recentlyPlayedContainer.innerHTML = '<p class="empty-message">No recently played songs.</p>';
            return;
        }
        
        // Create a grid for the songs
        const grid = document.createElement('div');
        grid.className = 'songs-grid';
        
        // Add each song to the grid
        recentlyPlayed.forEach(song => {
            const songCard = document.createElement('div');
            songCard.className = 'song-card';
            songCard.dataset.id = song.id;
            songCard.innerHTML = `
                <div class="song-card-cover">
                    <img src="${song.coverUrl}" alt="${song.title}" loading="lazy">
                    <div class="song-card-overlay">
                        <button class="play-btn" aria-label="Play ${song.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                </div>
                <div class="song-card-info">
                    <h3>${song.title}</h3>
                    <p>${song.artist}</p>
                </div>
            `;
            
            // Add event listeners for the buttons
            const playBtn = songCard.querySelector('.play-btn');
            playBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.playSong(song);
            });
            
            grid.appendChild(songCard);
        });
        
        recentlyPlayedContainer.appendChild(grid);
    }

    /**
     * Show a playlist
     * @param {string} playlistId - The ID of the playlist to show
     */
    showPlaylist(playlistId) {
        const playlist = libraryAPI.getPlaylist(playlistId);
        if (!playlist) return;
        
        this.currentPlaylist = playlist;
        
        // Update the main content area
        const mainContent = document.getElementById('main-content');
        if (!mainContent) return;
        
        mainContent.innerHTML = `
            <div class="playlist-header">
                <div class="playlist-cover">
                    <img src="${playlist.coverUrl}" alt="${playlist.name}" loading="lazy">
                </div>
                <div class="playlist-info">
                    <h1>${playlist.name}</h1>
                    <p>${playlist.description}</p>
                    <div class="playlist-meta">
                        <span>${playlist.songs.length} songs</span>
                        <span>•</span>
                        <span>${playlist.formattedTotalDuration()}</span>
                    </div>
                    <div class="playlist-actions">
                        <button class="play-all-btn" aria-label="Play all songs in ${playlist.name}">
                            <i class="fas fa-play"></i> Play All
                        </button>
                        <button class="edit-playlist-btn" aria-label="Edit ${playlist.name}">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="delete-playlist-btn" aria-label="Delete ${playlist.name}">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
            <div class="playlist-songs-container"></div>
        `;
        
        // Add event listeners for the buttons
        const playAllBtn = mainContent.querySelector('.play-all-btn');
        playAllBtn.addEventListener('click', () => this.playPlaylist(playlist));
        
        const editPlaylistBtn = mainContent.querySelector('.edit-playlist-btn');
        editPlaylistBtn.addEventListener('click', () => this.showEditPlaylistModal(playlist));
        
        const deletePlaylistBtn = mainContent.querySelector('.delete-playlist-btn');
        deletePlaylistBtn.addEventListener('click', () => this.confirmDeletePlaylist(playlist));
        
        // Render the songs in the playlist
        this.renderPlaylistSongs(playlist);
    }

    /**
     * Render the songs in a playlist
     * @param {Playlist} playlist - The playlist to render
     */
    renderPlaylistSongs(playlist) {
        const songsContainer = document.querySelector('.playlist-songs-container');
        if (!songsContainer) return;
        
        if (playlist.songs.length === 0) {
            songsContainer.innerHTML = '<p class="empty-message">This playlist is empty. Add some songs!</p>';
            return;
        }
        
        // Create a table for the songs
        const table = document.createElement('table');
        table.className = 'songs-table';
        
        // Add table header
        table.innerHTML = `
            <thead>
                <tr>
                    <th>#</th>
                    <th>Title</th>
                    <th>Artist</th>
                    <th>Album</th>
                    <th>Duration</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody></tbody>
        `;
        
        const tbody = table.querySelector('tbody');
        
        // Add each song to the table
        playlist.songs.forEach((song, index) => {
            const row = document.createElement('tr');
            row.dataset.id = song.id;
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>
                    <div class="song-info">
                        <img src="${song.coverUrl}" alt="${song.title}" loading="lazy">
                        <span>${song.title}</span>
                    </div>
                </td>
                <td>${song.artist}</td>
                <td>${song.album}</td>
                <td>${song.formattedDuration()}</td>
                <td>
                    <button class="play-btn" aria-label="Play ${song.title}">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="add-to-queue-btn" aria-label="Add ${song.title} to queue">
                        <i class="fas fa-list"></i>
                    </button>
                    <button class="toggle-favorite-btn" aria-label="${libraryAPI.isFavorite(song.id) ? 'Remove from favorites' : 'Add to favorites'}">
                        <i class="fas ${libraryAPI.isFavorite(song.id) ? 'fa-heart' : 'fa-heart-o'}"></i>
                    </button>
                    <button class="remove-from-playlist-btn" aria-label="Remove ${song.title} from playlist">
                        <i class="fas fa-times"></i>
                    </button>
                </td>
            `;
            
            // Add event listeners for the buttons
            const playBtn = row.querySelector('.play-btn');
            playBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.playSong(song);
            });
            
            const addToQueueBtn = row.querySelector('.add-to-queue-btn');
            addToQueueBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                libraryAPI.addToQueue(song);
                this.showToast(`Added "${song.title}" to queue`);
            });
            
            const toggleFavoriteBtn = row.querySelector('.toggle-favorite-btn');
            toggleFavoriteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                if (libraryAPI.isFavorite(song.id)) {
                    libraryAPI.removeFromFavorites(song.id);
                    toggleFavoriteBtn.querySelector('i').className = 'fas fa-heart-o';
                    this.showToast(`Removed "${song.title}" from favorites`);
                } else {
                    libraryAPI.addToFavorites(song);
                    toggleFavoriteBtn.querySelector('i').className = 'fas fa-heart';
                    this.showToast(`Added "${song.title}" to favorites`);
                }
            });
            
            const removeFromPlaylistBtn = row.querySelector('.remove-from-playlist-btn');
            removeFromPlaylistBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                libraryAPI.removeSongFromPlaylist(playlist.id, song.id);
                this.renderPlaylistSongs(playlist);
                this.showToast(`Removed "${song.title}" from playlist`);
            });
            
            tbody.appendChild(row);
        });
        
        songsContainer.innerHTML = '';
        songsContainer.appendChild(table);
    }

    /**
     * Show the create playlist modal
     */
    showCreatePlaylistModal() {
        const modal = document.getElementById('create-playlist-modal');
        if (!modal) return;
        
        // Reset the form
        const form = document.getElementById('new-playlist-form');
        if (form) form.reset();
        
        // Show the modal
        modal.style.display = 'flex';
    }

    /**
     * Handle the create playlist form submission
     * @param {Event} e - The form submission event
     */
    handleCreatePlaylist(e) {
        e.preventDefault();
        
        const nameInput = document.getElementById('new-playlist-name');
        const descriptionInput = document.getElementById('new-playlist-description');
        const isPublicInput = document.getElementById('new-playlist-public');
        
        if (!nameInput) return;
        
        const name = nameInput.value.trim();
        if (!name) {
            this.showToast('Please enter a playlist name', 'error');
            return;
        }
        
        const description = descriptionInput ? descriptionInput.value.trim() : '';
        const isPublic = isPublicInput ? isPublicInput.checked : true;
        
        // Create the playlist
        const playlist = libraryAPI.createPlaylist({
            name,
            description,
            isPublic,
            coverUrl: 'imgs/album-01.png' // Default cover
        });
        
        // Close the modal
        const modal = document.getElementById('create-playlist-modal');
        if (modal) modal.style.display = 'none';
        
        // Render the playlists
        this.renderPlaylists();
        
        // Show the new playlist
        this.showPlaylist(playlist.id);
        
        // Show a success message
        this.showToast(`Created playlist "${name}"`);
    }

    /**
     * Show the edit playlist modal
     * @param {Playlist} playlist - The playlist to edit
     */
    showEditPlaylistModal(playlist) {
        const modal = document.getElementById('edit-playlist-modal');
        if (!modal) return;
        
        // Set the form values
        const form = document.getElementById('edit-playlist-form');
        if (!form) return;
        
        const nameInput = form.querySelector('#edit-playlist-name');
        const descriptionInput = form.querySelector('#edit-playlist-description');
        const isPublicInput = form.querySelector('#edit-playlist-public');
        
        if (nameInput) nameInput.value = playlist.name;
        if (descriptionInput) descriptionInput.value = playlist.description;
        if (isPublicInput) isPublicInput.checked = playlist.isPublic;
        
        // Set the playlist ID
        form.dataset.id = playlist.id;
        
        // Show the modal
        modal.style.display = 'flex';
        
        // Add event listener for the form submission
        form.addEventListener('submit', (e) => this.handleEditPlaylist(e));
    }

    /**
     * Handle the edit playlist form submission
     * @param {Event} e - The form submission event
     */
    handleEditPlaylist(e) {
        e.preventDefault();
        
        const form = e.target;
        const playlistId = form.dataset.id;
        
        const nameInput = form.querySelector('#edit-playlist-name');
        const descriptionInput = form.querySelector('#edit-playlist-description');
        const isPublicInput = form.querySelector('#edit-playlist-public');
        
        if (!nameInput || !playlistId) return;
        
        const name = nameInput.value.trim();
        if (!name) {
            this.showToast('Please enter a playlist name', 'error');
            return;
        }
        
        const description = descriptionInput ? descriptionInput.value.trim() : '';
        const isPublic = isPublicInput ? isPublicInput.checked : true;
        
        // Update the playlist
        const playlist = libraryAPI.updatePlaylist(playlistId, {
            name,
            description,
            isPublic
        });
        
        // Close the modal
        const modal = document.getElementById('edit-playlist-modal');
        if (modal) modal.style.display = 'none';
        
        // Render the playlists
        this.renderPlaylists();
        
        // Show the updated playlist
        this.showPlaylist(playlist.id);
        
        // Show a success message
        this.showToast(`Updated playlist "${name}"`);
    }

    /**
     * Confirm deletion of a playlist
     * @param {Playlist} playlist - The playlist to delete
     */
    confirmDeletePlaylist(playlist) {
        if (confirm(`Are you sure you want to delete the playlist "${playlist.name}"?`)) {
            libraryAPI.deletePlaylist(playlist.id);
            this.renderPlaylists();
            
            // Show the favorites section
            const favoritesTab = document.getElementById('favorites-tab');
            if (favoritesTab) favoritesTab.click();
            
            // Show a success message
            this.showToast(`Deleted playlist "${playlist.name}"`);
        }
    }

    /**
     * Play a song
     * @param {Song} song - The song to play
     */
    playSong(song) {
        // In a real app, this would play the song
        console.log(`Playing song: ${song.title}`);
        
        // Add to recently played
        libraryAPI.addToRecentlyPlayed(song);
        
        // Show a toast
        this.showToast(`Playing "${song.title}"`);
    }

    /**
     * Play a playlist
     * @param {Playlist} playlist - The playlist to play
     */
    playPlaylist(playlist) {
        // In a real app, this would play the playlist
        console.log(`Playing playlist: ${playlist.name}`);
        
        // Add the first song to recently played
        if (playlist.songs.length > 0) {
            libraryAPI.addToRecentlyPlayed(playlist.songs[0]);
        }
        
        // Show a toast
        this.showToast(`Playing "${playlist.name}"`);
    }

    /**
     * Show a toast message
     * @param {string} message - The message to show
     * @param {string} type - The type of toast (success or error)
     */
    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // Remove the toast after 3 seconds
        setTimeout(() => {
            toast.classList.add('fade-out');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }
}

// Create and export a singleton instance
const libraryUI = new LibraryUI();
export default libraryUI;
