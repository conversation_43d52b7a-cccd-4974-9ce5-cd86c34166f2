/* Subscription Page Styles */

/* This file contains only subscription-specific styles.
   Common elements like navbar, footer, and buttons are in their respective CSS files */

/* Welcome header styling */
.welcome-header {
    color: var(--heading-gradient-primary);
    font-size: 2.5em;
    margin-top: 2rem;
    margin-bottom: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
    text-align: center;
}

/* Main container styles */
.subscription-section {
    padding: 2rem 0;
}

/* Subscription plans container */
.subscription-plans {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

/* Plan card styles - using card.css as base */
.plan-card {
    width: 320px;
    margin: 0 auto;
}

/* Featured plan styling */
.card.plan-card.featured {
    background: linear-gradient(
        135deg,
        rgba(var(--electric-violet-rgb), 0.15) 0%,
        rgba(var(--neon-blue-rgb), 0.15) 100%
    );
    border: 1px solid rgba(var(--neon-blue-rgb), 0.2);
    box-shadow:
        0 8px 32px rgba(var(--neon-blue-rgb), 0.2),
        0 0 20px rgba(var(--neon-blue-rgb), 0.1);
    position: relative;
    z-index: 2;
}

.card.plan-card.featured:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 16px 48px rgba(var(--neon-blue-rgb), 0.25),
        0 0 30px rgba(var(--neon-blue-rgb), 0.15);
}

/* Popular tag for featured plan */
.popular-tag {
    position: absolute;
    top: 12px;
    right: 0;
    background: var(--button-gradient);
    color: white;
    padding: 6px 15px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 3;
    text-align: center;
    letter-spacing: 0.5px; /* Improve readability */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Add subtle text shadow for better contrast */
    border-radius: 4px 0 0 4px; /* Rounded corners on left side */
}

/* Plan header */
.plan-header {
    padding: 2rem 1.5rem 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden; /* Ensure content inside doesn't overflow */
}

.plan-header h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--text-color);
}

/* Price styling */
.price {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-bottom: 1rem;
}

.price .currency {
    font-size: 1.2rem;
    opacity: 0.8;
    align-self: flex-start;
    margin-top: 0.5rem;
}

.price .amount {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--neon-blue);
    line-height: 1;
}

.price .period {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: normal;
    align-self: flex-end;
    margin-bottom: 0.5rem;
}

/* Plan content */
.plan-content {
    padding: 1.5rem;
    overflow: hidden; /* Ensure content inside doesn't overflow */
}

/* Features list */
.features-list {
    margin: 0 0 1.5rem;
    padding: 0;
    list-style: none;
    text-align: left;
}

.features-list li {
    margin: 0.8rem 0;
    padding-left: 2rem;
    position: relative;
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
}

.features-list li i {
    position: absolute;
    left: 0;
    color: var(--neon-blue);
    font-size: 0.9rem;
}

.card.plan-card.featured .features-list li i {
    color: var(--cosmic-pink);
}

/* Button styling */
.plan-content .button {
    width: 100%;
    margin-top: 0.5rem;
    padding: 0.8rem 1.5rem;
}

/* Loading state for buttons */
.button.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress steps styling */
.subscription-progress {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin: 2rem 0;
    position: relative;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    z-index: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(13, 17, 23, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(0, 224, 255, 0.1);
    transition: all 0.3s ease;
}

.progress-step.completed .step-number {
    background: var(--neon-blue);
    border-color: var(--neon-blue);
}

.progress-step.active .step-number {
    border-color: var(--neon-blue);
    color: var(--neon-blue);
}

.step-text {
    font-size: 0.9em;
    color: rgba(0, 224, 255, 0.7);
}

.progress-step.active .step-text {
    color: var(--neon-blue);
}

/* Responsive styles */
@media (max-width: 768px) {
    .welcome-header {
        font-size: 2.2rem;
        margin-bottom: 1.5rem;
    }

    .subscription-plans {
        gap: 2rem;
    }

    .plan-card {
        width: 300px;
    }

    .subscription-progress {
        gap: 1.5rem;
    }

    .features-list li {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .welcome-header {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    .plan-card {
        width: 100%;
        max-width: 320px;
    }

    .subscription-progress {
        gap: 1rem;
    }

    .features-list li {
        font-size: 0.8rem;
        margin: 0.7rem 0;
    }
}
