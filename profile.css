/* Profile Page Styles */

/* Main Content */
main {
    padding-top: 80px;
    min-height: 100vh;
    background: var(--background-color);
    color: var(--text-color);
}

/* ===== PROFILE HERO SECTION ===== */
.profile-hero {
    position: relative;
    width: 100%;
    min-height: 340px;
    padding: 4rem 0;
    margin-top: 2rem;
    background: var(--background-color);
    overflow: hidden;
}

.profile-hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.15) 0%,
        rgba(var(--cosmic-pink-rgb), 0.15) 50%,
        rgba(var(--electric-violet-rgb), 0.15) 100%
    );
    z-index: 1;
}

.profile-container {
    position: relative;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    z-index: 2;
    display: flex;
    justify-content: center;
}

/* Profile Header */
.profile-header {
    display: flex;
    gap: 2.5rem;
    align-items: flex-start;
    padding: 2.5rem;
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    width: 100%;
    max-width: 800px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.profile-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Add backdrop gradient */
.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: -1;
    opacity: 0.5;
}

/* Profile Image Section */
.profile-image-container {
    position: relative;
    width: 140px;
    height: 140px;
    border-radius: 50%;
}

.profile-image-container img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.profile-image-container:hover img {
    transform: scale(1.02);
}

/* Edit Image Button */
.edit-image-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--button-gradient);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--button-shadow);
    transition: all 0.3s ease;
    z-index: 10;
}

.edit-image-btn i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 1.2rem;
    line-height: 1;
}

.edit-image-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--button-hover-shadow);
}

/* Profile Info */
.profile-info {
    flex: 1;
    padding-top: 0.5rem;
}

.profile-name {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    background: linear-gradient(
        45deg,
        var(--neon-blue),
        var(--cosmic-pink) 50%,
        var(--electric-violet)
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    letter-spacing: -0.5px;
}

.profile-bio {
    font-size: 1.05rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.75rem;
    line-height: 1.8;
    max-width: 90%;
    position: relative;
    overflow-y: auto;
    max-height: 120px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for the profile bio */
.profile-bio::-webkit-scrollbar {
    width: 6px;
}

.profile-bio::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb {
    background: var(--neon-blue);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(var(--neon-blue), var(--cosmic-pink));
}

/* Profile Stats */
.profile-stats {
    display: flex;
    gap: 2.5rem;
    margin-top: 1rem;
    position: relative;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    min-width: 110px;
}

.stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
}

.stat::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
}

.stat:hover::before {
    transform: scaleX(1);
}

.stat-value {
    font-size: 1.6rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.stat-icon {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 0.5rem;
    transition: transform 0.3s ease;
}

.stat:hover .stat-icon {
    transform: scale(1.2);
    color: white;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Edit Profile Button */
.edit-profile-btn {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: var(--button-gradient);
    border: none;
    color: white;
    padding: 0.4rem 0.9rem;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.edit-profile-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--button-hover-shadow);
}

/* ===== PROFILE CONTENT SECTION ===== */
.profile-content {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.content-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.content-section:hover {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

/* ===== PLAYLISTS SECTION ===== */
/* Playlists Grid */
.playlists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.03);
}

/* Playlist Card */
.playlist-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.playlist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.playlist-card .img-container {
    position: relative;
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
}

.playlist-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.playlist-card:hover img {
    transform: scale(1.05);
}

.playlist-card .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.playlist-card:hover .play-overlay {
    opacity: 1;
}

.playlist-info {
    padding: 1rem;
    text-align: center;
}

.playlist-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-color);
}

.playlist-tracks {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

/* Create Playlist Card */
.create-playlist-card {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.03);
    border: 2px dashed rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.create-playlist-card:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(var(--neon-blue-rgb), 0.3);
}

.create-playlist-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.create-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--button-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.create-icon i {
    font-size: 1.5rem;
    color: white;
}

.create-playlist-card:hover .create-icon {
    transform: scale(1.1);
    box-shadow: var(--button-hover-shadow);
}

.create-playlist-content h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
}

/* Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.03);
}

/* Card Styles */
.card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.card .img-container {
    position: relative;
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
}

.card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover img {
    transform: scale(1.05);
}

.card .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover .play-overlay {
    opacity: 1;
}

.card-content {
    padding: 1rem;
    text-align: center;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-color);
}

.card-subtitle {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

/* Artist Card */
.artist-card .img-container img {
    border-radius: 50%;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: rgba(30, 35, 45, 0.95);
    border-radius: 16px;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    transform: translateY(20px);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.modal.active .modal-content {
    transform: translateY(0);
}

.modal h2 {
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-color);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--neon-blue);
    box-shadow: 0 0 0 2px rgba(var(--neon-blue-rgb), 0.3);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .profile-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 2rem 1.5rem;
    }

    .profile-info {
        width: 100%;
    }

    .profile-bio {
        max-width: 100%;
    }

    .profile-stats {
        justify-content: center;
    }

    .edit-profile-btn {
        top: 1rem;
        right: 1rem;
    }

    .playlists-grid,
    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .content-section {
        padding: 1rem;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .profile-container {
        padding: 0 1rem;
    }

    .profile-header {
        padding: 1.5rem 1rem;
    }

    .profile-image-container {
        width: 100px;
        height: 100px;
    }

    .profile-name {
        font-size: 1.8rem;
    }

    .profile-bio {
        font-size: 0.9rem;
        max-height: 100px;
    }

    .profile-stats {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .stat {
        min-width: 90px;
        padding: 0.75rem;
    }

    .stat-value {
        font-size: 1.3rem;
    }

    .stat-icon {
        font-size: 1.2rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .playlists-grid,
    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
        gap: 1rem;
    }

    .section-header h2 {
        font-size: 1.5rem;
    }
}
