# Banshee Music App

A modern music streaming application with a focus on artist discovery and user experience.

## Project Structure

```
banshee-music-app/
├── index.html              # Main entry point
├── css/                    # CSS stylesheets
│   ├── style.css           # Global styles
│   ├── variables.css       # CSS variables
│   └── [page]-styles.css   # Page-specific styles
├── js/                     # JavaScript files
│   ├── api.js              # API integration
│   ├── main.js             # Main application logic
│   └── [page].js           # Page-specific scripts
├── assets/                 # Static assets
│   └── imgs/               # Images
├── pages/                  # Page templates
│   ├── artist.html         # Artist profile page
│   ├── playlist.html       # Playlist page
│   └── ...                 # Other pages
└── components/             # Reusable components
    ├── navbar.html         # Navigation bar
    └── player.html         # Music player
```

## Development Guidelines

1. **File Organization**:
   - Keep CSS files in the `/css` directory
   - Keep JavaScript files in the `/js` directory
   - Keep images in the `/assets/imgs` directory
   - Keep page templates in the `/pages` directory
   - Keep reusable components in the `/components` directory

2. **Naming Conventions**:
   - Use kebab-case for file names (e.g., `artist-profile.css`)
   - Use camelCase for JavaScript variables and functions
   - Use PascalCase for JavaScript classes

3. **Code Style**:
   - Use 2-space indentation
   - Add comments for complex logic
   - Group related CSS properties together
   - Place media queries at the end of CSS files

## Getting Started

1. Clone the repository
2. Open `index.html` in your browser
3. Start developing!

## Features

- Music streaming
- Artist profiles
- Playlist creation and sharing
- User profiles
- Subscription management
- Payment processing
