<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-logo">
            <img src="../imgs/logo-B.png" alt="Banshee Music Logo">
        </div>
        
        <div class="auth-card">
            <h1>Reset Password</h1>
            <p class="auth-subtitle">Enter your email to receive a password reset link</p>
            
            <div id="reset-error" class="auth-error"></div>
            <div id="reset-success" class="auth-success"></div>
            
            <form id="reset-form" class="auth-form">
                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-with-icon">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>
                
                <button type="submit" class="auth-button">Send Reset Link</button>
            </form>
            
            <p class="auth-redirect">
                Remember your password? <a href="login.html">Back to Login</a>
            </p>
        </div>
        
        <!-- This section is shown after the reset link is sent -->
        <div class="auth-card hidden" id="reset-sent">
            <div class="auth-icon-success">
                <i class="fas fa-envelope"></i>
            </div>
            
            <h1>Check Your Email</h1>
            <p class="auth-subtitle">We've sent a password reset link to your email address. Please check your inbox and follow the instructions.</p>
            
            <p class="auth-note">
                If you don't see the email, check your spam folder or <a href="#" id="resend-link">resend the link</a>.
            </p>
            
            <a href="login.html" class="auth-button">Back to Login</a>
        </div>
    </div>
    
    <script src="auth.js"></script>
</body>
</html>
