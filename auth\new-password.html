<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set New Password - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-logo">
            <img src="../imgs/logo-B.png" alt="Banshee Music Logo">
        </div>
        
        <div class="auth-card">
            <h1>Set New Password</h1>
            <p class="auth-subtitle">Create a new password for your account</p>
            
            <div id="new-password-error" class="auth-error"></div>
            
            <form id="new-password-form" class="auth-form">
                <input type="hidden" id="reset-token" name="reset-token">
                
                <div class="form-group">
                    <label for="password">New Password</label>
                    <div class="input-with-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="password-strength">
                        <div class="strength-meter">
                            <div class="strength-segment"></div>
                            <div class="strength-segment"></div>
                            <div class="strength-segment"></div>
                            <div class="strength-segment"></div>
                        </div>
                        <span class="strength-text">Password strength</span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirm-password">Confirm New Password</label>
                    <div class="input-with-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="confirm-password" name="confirm-password" required>
                    </div>
                </div>
                
                <button type="submit" class="auth-button">Set New Password</button>
            </form>
        </div>
        
        <!-- This section is shown after the password is successfully reset -->
        <div class="auth-card hidden" id="password-reset-success">
            <div class="auth-icon-success">
                <i class="fas fa-check-circle"></i>
            </div>
            
            <h1>Password Reset Successful</h1>
            <p class="auth-subtitle">Your password has been successfully reset. You can now login with your new password.</p>
            
            <a href="login.html" class="auth-button">Login Now</a>
        </div>
    </div>
    
    <script src="auth.js"></script>
</body>
</html>
