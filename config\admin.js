// Initial admin configuration
const adminConfig = {
    defaultAdmin: {
        username: "admin",
        email: "<EMAIL>",
        password: "Admin@Banshee2024", // This will be hashed in production
        role: "admin"
    },
    testAccounts: {
        regularUser: {
            username: "testuser",
            email: "<EMAIL>",
            password: "Test@User2024",
            role: "user"
        },
        artistUser: {
            username: "testartist",
            email: "<EMAIL>",
            password: "Test@Artist2024",
            role: "artist"
        },
        premiumUser: {
            username: "testpremium",
            email: "<EMAIL>",
            password: "Test@Premium2024",
            role: "premium"
        }
    }
};

module.exports = adminConfig;
