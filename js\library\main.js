/**
 * Main entry point for the music library management system
 */

import libraryAPI from './api.js';
import libraryUI from './ui.js';

/**
 * Initialize the music library management system
 */
function initializeLibrary() {
    console.log('Initializing music library management system...');
    
    // Initialize the library UI
    libraryUI.initialize();
    
    // Set up event listeners for the mini player
    setupMiniPlayer();
    
    console.log('Music library management system initialized');
}

/**
 * Set up event listeners for the mini player
 */
function setupMiniPlayer() {
    const playPauseBtn = document.querySelector('.play-pause');
    const previousBtn = document.querySelector('.previous');
    const nextBtn = document.querySelector('.next');
    const progressBar = document.querySelector('.progress-bar');
    const volumeBtn = document.querySelector('.volume');
    const repeatBtn = document.querySelector('.repeat');
    const shuffleBtn = document.querySelector('.shuffle');
    const queueBtn = document.querySelector('.queue');
    
    // Play/Pause button
    if (playPauseBtn) {
        playPauseBtn.addEventListener('click', () => {
            const icon = playPauseBtn.querySelector('i');
            if (icon.classList.contains('fa-play')) {
                icon.classList.remove('fa-play');
                icon.classList.add('fa-pause');
                // In a real app, this would play the current song
                console.log('Playing music');
            } else {
                icon.classList.remove('fa-pause');
                icon.classList.add('fa-play');
                // In a real app, this would pause the current song
                console.log('Pausing music');
            }
        });
    }
    
    // Previous button
    if (previousBtn) {
        previousBtn.addEventListener('click', () => {
            // In a real app, this would play the previous song
            console.log('Playing previous song');
        });
    }
    
    // Next button
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            // In a real app, this would play the next song
            console.log('Playing next song');
        });
    }
    
    // Progress bar
    if (progressBar) {
        progressBar.addEventListener('click', (e) => {
            const rect = progressBar.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const width = rect.width;
            const percentage = x / width;
            
            // Update progress fill
            const progressFill = progressBar.querySelector('.progress-fill');
            if (progressFill) {
                progressFill.style.width = `${percentage * 100}%`;
            }
            
            // In a real app, this would seek to the corresponding position in the song
            console.log(`Seeking to ${Math.round(percentage * 100)}%`);
        });
    }
    
    // Volume button
    if (volumeBtn) {
        volumeBtn.addEventListener('click', () => {
            const icon = volumeBtn.querySelector('i');
            if (icon.classList.contains('fa-volume-up')) {
                icon.classList.remove('fa-volume-up');
                icon.classList.add('fa-volume-mute');
                // In a real app, this would mute the audio
                console.log('Muting audio');
            } else {
                icon.classList.remove('fa-volume-mute');
                icon.classList.add('fa-volume-up');
                // In a real app, this would unmute the audio
                console.log('Unmuting audio');
            }
        });
    }
    
    // Repeat button
    if (repeatBtn) {
        repeatBtn.addEventListener('click', () => {
            repeatBtn.classList.toggle('active');
            // In a real app, this would toggle repeat mode
            console.log('Toggling repeat mode');
        });
    }
    
    // Shuffle button
    if (shuffleBtn) {
        shuffleBtn.addEventListener('click', () => {
            shuffleBtn.classList.toggle('active');
            // In a real app, this would toggle shuffle mode
            console.log('Toggling shuffle mode');
        });
    }
    
    // Queue button
    if (queueBtn) {
        queueBtn.addEventListener('click', () => {
            // In a real app, this would show the queue
            console.log('Showing queue');
            
            // For now, let's just show a toast
            const toast = document.createElement('div');
            toast.className = 'toast success';
            toast.textContent = 'Queue: Coming soon!';
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('fade-out');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        });
    }
}

// Initialize the library when the DOM is loaded
document.addEventListener('DOMContentLoaded', initializeLibrary);

// Export the initialization function
export { initializeLibrary };
