/* Animations for the music app */

/* Shimmer animation for gradients */
@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Star glow animation */
@keyframes starGlow {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255,255,255,0);
    }
    25% {
        box-shadow: 0 0 25px 3px rgba(0, 224, 255, 0.3); /* Blue glow */
    }
    50% {
        box-shadow: 0 0 30px 4px rgba(255,255,255,0.4); /* White glow */
    }
    75% {
        box-shadow: 0 0 25px 3px rgba(255, 0, 110, 0.3); /* Pink glow */
    }
}

/* Shooting star animation */
@keyframes shooting-star {
    0% {
        transform: translate(-100px, -100px) rotate(45deg) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    20%, 80% {
        transform: translate(calc(100vw + 100px), calc(100vh + 100px)) rotate(45deg) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(calc(100vw + 150px), calc(100vh + 150px)) rotate(45deg) scale(0.5);
        opacity: 0;
    }
}

/* Twinkle animation for stars */
@keyframes twinkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0.8);
        filter: blur(1px);
    }
    25% {
        opacity: 0.7;
        transform: scale(1.1);
        filter: blur(0.5px);
    }
    50% {
        opacity: 1;
        transform: scale(1.3);
        filter: blur(0px);
    }
    75% {
        opacity: 0.7;
        transform: scale(1.1);
        filter: blur(0.5px);
    }
}

/* Pulse animation for quick links */
@keyframes quickLinkPulse {
    0% {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2),
                   0 0 20px rgba(var(--neon-blue-rgb), 0.2);
    }
    50% {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3),
                   0 0 30px rgba(var(--neon-blue-rgb), 0.3),
                   0 0 20px rgba(var(--cosmic-pink-rgb), 0.2);
    }
    100% {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2),
                   0 0 20px rgba(var(--neon-blue-rgb), 0.2);
    }
}

/* Button shine animation */
@keyframes buttonShine {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Quick Link Pulse animation */
@keyframes quickLinkPulse {
    0% {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2),
                   0 0 20px rgba(var(--neon-blue-rgb), 0.2);
    }
    50% {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3),
                   0 0 30px rgba(var(--neon-blue-rgb), 0.3),
                   0 0 20px rgba(var(--cosmic-pink-rgb), 0.2);
    }
    100% {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2),
                   0 0 20px rgba(var(--neon-blue-rgb), 0.2);
    }
}

/* Fade in animation */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Slide up animation */
@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Navigation shimmer animation */
@keyframes navShimmer {
    0% {
        background-position: 200% 0;
        box-shadow: 0 0 10px var(--neon-blue),
                   0 0 20px rgba(0, 224, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 15px var(--neon-blue),
                   0 0 25px rgba(0, 224, 255, 0.4);
    }
    100% {
        background-position: -200% 0;
        box-shadow: 0 0 10px var(--neon-blue),
                   0 0 20px rgba(0, 224, 255, 0.3);
    }
}

/* Reduce animations for users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .menu li a[aria-current="page"]::after {
        animation: none;
        background-position: 0 0;
    }
}
