/* <PERSON><PERSON> Styles for the music app */

/* Base Button Styles */
.button,
.play-button,
.view-all,
.cta-button,
.control-button,
.playlist-controls button,
.hd-button,
button[class*="btn-"] {
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: 15px 30px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    box-shadow: var(--button-shadow);
}

/* But<PERSON> Sizes */
.btn-large,
.cta-button {
    padding: 1rem 2.5rem;
    font-size: 1.2rem;
    border-radius: 30px;
}

.btn-medium,
.button,
.play-button {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
    border-radius: 25px;
}

.btn-small,
.view-all,
.control-button,
.playlist-controls button {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    border-radius: 20px;
}

/* Button Hover Effects */
.button:hover,
.play-button:hover,
.view-all:hover,
.cta-button:hover,
.control-button:hover,
.playlist-controls button:hover,
.hd-button:hover,
button[class*="btn-"]:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

/* Button Active Effects */
.button:active,
.play-button:active,
.view-all:active,
.cta-button:active,
.control-button:active,
.playlist-controls button:active,
.hd-button:active,
button[class*="btn-"]:active {
    transform: translateY(-1px);
    box-shadow: var(--button-shadow);
}

/* Shine effect */
.button::before,
.play-button::before,
.view-all::before,
.cta-button::before,
.control-button::before,
.playlist-controls button::before,
.hd-button::before,
button[class*="btn-"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: 0.5s;
}

/* Shine animation on hover */
.button:hover::before,
.play-button:hover::before,
.view-all:hover::before,
.cta-button:hover::before,
.control-button:hover::before,
.playlist-controls button:hover::before,
.hd-button:hover::before,
button[class*="btn-"]:hover::before {
    left: 100%;
    animation: buttonShine 1s linear;
}

/* Icon Spacing */
button i,
.button i,
.view-all i,
.cta-button i {
    margin-right: 8px;
}

/* Special Button Styles */
.play-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.play-button i {
    margin-right: 0;
    margin-left: 3px; /* Slight offset for play icon */
}

/* Outline Button Variant */
.button-outline,
.btn-outline {
    background: transparent;
    border: 2px solid var(--neon-blue);
    color: var(--neon-blue);
}

.button-outline:hover,
.btn-outline:hover {
    background: rgba(0, 224, 255, 0.1);
    color: white;
}

/* Ghost Button Variant */
.button-ghost,
.btn-ghost {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.button-ghost:hover,
.btn-ghost:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Disabled Button State */
.button:disabled,
.play-button:disabled,
.view-all:disabled,
.cta-button:disabled,
.control-button:disabled,
.playlist-controls button:disabled,
.hd-button:disabled,
button[class*="btn-"]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.button:disabled::before,
.play-button:disabled::before,
.view-all:disabled::before,
.cta-button:disabled::before,
.control-button:disabled::before,
.playlist-controls button:disabled::before,
.hd-button:disabled::before,
button[class*="btn-"]:disabled::before {
    display: none;
}

/* Media Queries */
@media (max-width: 768px) {
    .btn-large,
    .cta-button {
        padding: 0.8rem 2rem;
        font-size: 1.1rem;
    }
    
    .btn-medium,
    .button,
    .play-button {
        padding: 0.7rem 1.3rem;
        font-size: 0.95rem;
    }
    
    .btn-small,
    .view-all,
    .control-button,
    .playlist-controls button {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .btn-large,
    .cta-button {
        padding: 0.7rem 1.8rem;
        font-size: 1rem;
    }
    
    .btn-medium,
    .button,
    .play-button {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .btn-small,
    .view-all,
    .control-button,
    .playlist-controls button {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}
