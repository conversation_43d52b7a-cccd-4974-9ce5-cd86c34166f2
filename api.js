// Base URL for API requests
const API_BASE_URL = 'https://api.bansheemusic.com/v1'; // This would be your actual API endpoint

// Utility functions
export const utils = {
    showLoader: () => {
        const loader = document.getElementById('loader');
        if (loader) loader.style.display = 'flex';
    },

    hideLoader: () => {
        const loader = document.getElementById('loader');
        if (loader) loader.style.display = 'none';
    },

    showToast: (message, isError = false) => {
        const toast = document.createElement('div');
        toast.className = `toast ${isError ? 'error' : 'success'}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    },

    getAuthToken: () => {
        return localStorage.getItem('token') || sessionStorage.getItem('token');
    },

    setAuthToken: (token, rememberMe = false) => {
        if (rememberMe) {
            localStorage.setItem('token', token);
        } else {
            sessionStorage.setItem('token', token);
        }
    },

    clearAuthToken: () => {
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
    },

    isAuthenticated: () => {
        return !!utils.getAuthToken();
    }
};

// API client with authentication
export const API = {
    // Authentication
    login: async (username, password, captchaResponse) => {
        utils.showLoader();
        try {
            // In a real implementation, this would be an actual API call
            // For now, we'll simulate a network request
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Validate credentials (mock implementation)
            if (username === 'admin' && password === 'password') {
                const token = 'mock-jwt-token-' + Math.random().toString(36).substring(2);
                utils.setAuthToken(token, true);
                return {
                    success: true,
                    token,
                    user: {
                        id: 1,
                        username,
                        name: 'Admin User',
                        email: '<EMAIL>',
                        role: 'admin'
                    }
                };
            } else if (username === 'user' && password === 'password') {
                const token = 'mock-jwt-token-' + Math.random().toString(36).substring(2);
                utils.setAuthToken(token, true);
                return {
                    success: true,
                    token,
                    user: {
                        id: 2,
                        username,
                        name: 'Regular User',
                        email: '<EMAIL>',
                        role: 'user'
                    }
                };
            }

            return {
                success: false,
                message: 'Invalid username or password'
            };
        } catch (error) {
            console.error('Login error:', error);
            return {
                success: false,
                message: 'An error occurred during login'
            };
        } finally {
            utils.hideLoader();
        }
    },

    signup: async (userData) => {
        utils.showLoader();
        try {
            // In a real implementation, this would be an actual API call
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Simulate user creation
            const token = 'mock-jwt-token-' + Math.random().toString(36).substring(2);
            utils.setAuthToken(token, true);

            return {
                success: true,
                token,
                user: {
                    id: Math.floor(Math.random() * 1000) + 3,
                    username: userData.username,
                    name: userData.name,
                    email: userData.email,
                    role: 'user'
                }
            };
        } catch (error) {
            console.error('Signup error:', error);
            return {
                success: false,
                message: 'An error occurred during signup'
            };
        } finally {
            utils.hideLoader();
        }
    },

    logout: () => {
        utils.clearAuthToken();
        window.location.href = 'login.html';
    },

    validateCaptcha: async (captchaResponse) => {
        // In a real implementation, this would verify the CAPTCHA with a server
        // For now, we'll just simulate validation
        await new Promise(resolve => setTimeout(resolve, 500));

        // Always return true for the mock implementation
        return { valid: true };
    },

    // Subscription Management
    getSubscriptionPlans: async () => {
        console.log('API.getSubscriptionPlans called');
        utils.showLoader();
        try {
            console.log('Simulating API call to get subscription plans...');
            // In a real implementation, this would fetch from your backend
            await new Promise(resolve => setTimeout(resolve, 800));

            const plans = [
                {
                    id: 'free',
                    name: 'Free Account',
                    price: 0,
                    interval: 'month',
                    features: [
                        'Enjoy Banshee with occasional ads',
                        'Basic audio quality (128kbps)',
                        'Limited skips (6 per hour)',
                        'Mobile app access'
                    ]
                },
                {
                    id: 'premium',
                    name: 'Premium',
                    price: 2.99,
                    interval: 'month',
                    features: [
                        'Ad-free listening experience',
                        'High-quality audio (320kbps)',
                        'Unlimited skips',
                        'Offline mode',
                        'Cross-platform sync',
                        'Exclusive content access'
                    ]
                },
                {
                    id: 'artist',
                    name: 'Artist Account',
                    price: 4.99,
                    interval: 'month',
                    features: [
                        'All Premium features included',
                        'Upload unlimited tracks',
                        'Advanced analytics dashboard',
                        'Promotional tools',
                        'Direct fan engagement',
                        'Custom artist profile'
                    ]
                }
            ];
            console.log('Returning subscription plans:', plans);
            return plans;
        } finally {
            console.log('Hiding loader...');
            utils.hideLoader();
        }
    },

    subscribeToPlan: async (planId) => {
        console.log('API.subscribeToPlan called with planId:', planId);
        utils.showLoader();
        try {
            console.log('Simulating API call...');
            // In a real implementation, this would create a subscription in your backend
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Generate a mock subscription ID
            const subscriptionId = 'sub_' + Math.random().toString(36).substring(2);
            console.log('Generated subscriptionId:', subscriptionId);

            // Store subscription info in session storage for the payment flow
            console.log('Getting subscription plans...');
            const plans = await API.getSubscriptionPlans();
            console.log('Available plans:', plans);

            const selectedPlan = plans.find(plan => plan.id === planId);
            console.log('Selected plan:', selectedPlan);

            if (!selectedPlan) {
                console.error('Invalid plan selected:', planId);
                throw new Error('Invalid plan selected');
            }

            // Save selected plan to session storage
            console.log('Saving plan to session storage...');
            sessionStorage.setItem('selectedPlan', JSON.stringify(selectedPlan));
            sessionStorage.setItem('subscriptionId', subscriptionId);

            const response = {
                success: true,
                subscriptionId,
                planId,
                nextStep: planId === 'free' ? 'confirmation' : 'payment'
            };
            console.log('Returning response:', response);
            return response;
        } catch (error) {
            console.error('Subscription error:', error);
            return {
                success: false,
                message: error.message || 'Failed to create subscription'
            };
        } finally {
            console.log('Hiding loader...');
            utils.hideLoader();
        }
    },

    processPayment: async (paymentDetails, subscriptionId) => {
        utils.showLoader();
        try {
            // In a real implementation, this would process payment through Stripe or another provider
            await new Promise(resolve => setTimeout(resolve, 1500));

            // Validate payment details (basic validation)
            if (!paymentDetails.cardNumber || !paymentDetails.expiryDate || !paymentDetails.cvc) {
                throw new Error('Invalid payment details');
            }

            // Get subscription details
            const selectedPlanJson = sessionStorage.getItem('selectedPlan');
            if (!selectedPlanJson) {
                throw new Error('No subscription plan selected');
            }

            const selectedPlan = JSON.parse(selectedPlanJson);

            // Generate a mock transaction ID
            const transactionId = 'txn_' + Math.random().toString(36).substring(2);

            // Calculate next billing date (30 days from now)
            const nextBillingDate = new Date();
            nextBillingDate.setDate(nextBillingDate.getDate() + 30);

            // Save transaction details to session storage for the confirmation page
            sessionStorage.setItem('transactionId', transactionId);
            sessionStorage.setItem('nextBillingDate', nextBillingDate.toISOString());

            return {
                success: true,
                transactionId,
                subscriptionId,
                amount: selectedPlan.price,
                currency: 'USD',
                nextBillingDate: nextBillingDate.toISOString()
            };
        } catch (error) {
            console.error('Payment error:', error);
            return {
                success: false,
                message: error.message || 'Payment processing failed'
            };
        } finally {
            utils.hideLoader();
        }
    },

    getSubscriptionDetails: async (subscriptionId) => {
        utils.showLoader();
        try {
            // In a real implementation, this would fetch subscription details from your backend
            await new Promise(resolve => setTimeout(resolve, 600));

            // Get subscription details from session storage
            const selectedPlanJson = sessionStorage.getItem('selectedPlan');
            const nextBillingDate = sessionStorage.getItem('nextBillingDate');

            if (!selectedPlanJson) {
                throw new Error('Subscription not found');
            }

            const selectedPlan = JSON.parse(selectedPlanJson);

            return {
                id: subscriptionId,
                status: 'active',
                planId: selectedPlan.id,
                planName: selectedPlan.name,
                price: selectedPlan.price,
                interval: selectedPlan.interval,
                currentPeriodStart: new Date().toISOString(),
                currentPeriodEnd: nextBillingDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                cancelAtPeriodEnd: false
            };
        } catch (error) {
            console.error('Error fetching subscription:', error);
            return {
                success: false,
                message: error.message || 'Failed to fetch subscription details'
            };
        } finally {
            utils.hideLoader();
        }
    },

    // Music Library
    getTrendingMusic: async () => {
        utils.showLoader();
        try {
            // In a real implementation, this would fetch from your backend
            await new Promise(resolve => setTimeout(resolve, 800));

            return [
                { id: 1, title: 'Summer Vibes', artist: 'Chill Wave', image: 'imgs/album-01.png' },
                { id: 2, title: 'Midnight Dreams', artist: 'Luna Sky', image: 'imgs/album-02.png' },
                { id: 3, title: 'Urban Beats', artist: 'City Pulse', image: 'imgs/album-03.png' },
                { id: 4, title: 'Ocean Calm', artist: 'Aqua Sounds', image: 'imgs/album-04.png' },
                { id: 5, title: 'Electric Soul', artist: 'Voltage', image: 'imgs/album-05.png' }
            ];
        } finally {
            utils.hideLoader();
        }
    },

    getFeaturedArtists: async () => {
        utils.showLoader();
        try {
            // In a real implementation, this would fetch from your backend
            await new Promise(resolve => setTimeout(resolve, 800));

            return [
                { id: 1, name: 'Luna Sky', followers: '2.3M', image: 'imgs/album-03.png' },
                { id: 2, name: 'Voltage', followers: '1.8M', image: 'imgs/album-04.png' },
                { id: 3, name: 'Chill Wave', followers: '950K', image: 'imgs/album-01.png' },
                { id: 4, name: 'City Pulse', followers: '1.2M', image: 'imgs/album-05.png' }
            ];
        } finally {
            utils.hideLoader();
        }
    },

    getNewReleases: async () => {
        utils.showLoader();
        try {
            // In a real implementation, this would fetch from your backend
            await new Promise(resolve => setTimeout(resolve, 800));

            return [
                { id: 1, title: 'Neon Nights', artist: 'Glow', releaseDate: '2023-03-15', image: 'imgs/album-05.png' },
                { id: 2, title: 'Desert Wind', artist: 'Mirage', releaseDate: '2023-03-10', image: 'imgs/album-02.png' },
                { id: 3, title: 'Crystal Clear', artist: 'Prism', releaseDate: '2023-03-05', image: 'imgs/album-04.png' },
                { id: 4, title: 'Rainy Day', artist: 'Storm', releaseDate: '2023-03-01', image: 'imgs/album-03.png' }
            ];
        } finally {
            utils.hideLoader();
        }
    }
};
