/*
 * Consolidated Library Page Styles
 * This file combines styles from library-clean.css and relevant parts of glimmer.css
 */

/* ===== Variables ===== */
:root {
    /* Colors - Only adding those not in style.css */
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-primary: #ffffff;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime-rgb: 167, 255, 74;
    --accent-color-hover: #ff3385;

    /* Glimmer Variables */
    --glimmer-gradient: linear-gradient(90deg, transparent, var(--neon-blue), var(--cosmic-pink), transparent);
    --glimmer-duration: 3s;
    --glow-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
    --glow-shadow-alt: 0 0 15px var(--cosmic-pink), 0 0 30px rgba(255, 0, 110, 0.3);
    --transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== Main Container ===== */
.library-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
}

/* ===== Hero Section ===== */
.library-hero {
    position: relative;
    overflow: hidden;
    padding: 4rem 2rem;
    margin-bottom: 2rem;
    margin-top: 5rem;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 3rem;
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

/* Glowing overlay */
.hero-content::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.15) 0%,
        transparent 70%
    ), linear-gradient(
        45deg,
        rgba(var(--neon-blue-rgb), 0.2) 0%,
        transparent 25%,
        rgba(255, 255, 255, 0.15) 50%,
        transparent 75%,
        rgba(255, 0, 110, 0.2) 100%
    );
    opacity: 0.8;
    mix-blend-mode: overlay;
    animation: shimmer 12s infinite ease-in-out;
    background-size: 200% 200%;
    border-radius: inherit;
    z-index: 1;
}

/* Add backdrop gradient similar to Artist Page */
.hero-content::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: 0;
    opacity: 0.5;
}

.hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 800;
    letter-spacing: -0.5px;
    position: relative;
    z-index: 2;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    position: relative;
    z-index: 2;
}

/* ===== Library Stats ===== */
.library-stats {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2rem;
    position: relative;
    z-index: 2;
}

.stat-item {
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateY(0);
    border-radius: 12px;
    padding: 1.5rem;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
}

.stat-item::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-icon {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 0.5rem;
    transition: transform 0.3s ease;
}

.stat-item:hover .stat-icon {
    transform: scale(1.2);
    color: white;
}

/* ===== Search Section ===== */
.search-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    padding: 1.5rem 1.5rem 2.5rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.search-container {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-bar {
    width: 100%;
    padding: 1rem 3rem;
    border-radius: 50px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    font-size: 1rem;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.search-bar:focus {
    outline: none;
    border-color: rgba(var(--neon-blue-rgb), 0.5);
    box-shadow: 0 0 15px rgba(var(--neon-blue-rgb), 0.2);
}

.search-icon {
    position: absolute;
    left: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-clear {
    position: absolute;
    right: 4rem;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.search-clear:hover {
    opacity: 1;
}

.search-submit-btn {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: var(--button-gradient);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.search-submit-btn:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: var(--button-hover-shadow);
}

.search-filters {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.filter-btn.active {
    background: var(--button-gradient);
    color: white;
    border: none;
    box-shadow: var(--button-shadow);
}

/* ===== Section Headers ===== */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    border-radius: 3px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.song-count {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.view-all-link {
    color: var(--accent-color);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
}

.view-all-link:hover {
    color: var(--accent-color-hover);
    text-decoration: underline;
}

.create-playlist-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--button-gradient);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.create-playlist-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3);
}

/* ===== Playlists Section ===== */
.library-section {
    margin-bottom: 3rem;
}

/* ===== Carousel Styles ===== */
.carousel {
    margin: 0;
    padding: 30px 20px 60px;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.03);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    overflow: hidden !important;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.05);
    width: 100%;
    box-sizing: border-box;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.carousel-cell {
    width: 100%;
    padding: 0 5px;
}

.items {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    padding: 0.5rem;
}

.item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    height: 280px;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

.item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.item::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
    z-index: 1;
}

.item:hover::before {
    transform: scaleX(1);
}

.img-container {
    height: 160px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.item:hover .img-container img {
    transform: scale(1.1);
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.item:hover .play-overlay {
    opacity: 1;
}

.play-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--button-gradient);
    border: none;
    color: white;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    transform: scale(0.8);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.item:hover .play-button {
    transform: scale(1);
}

.play-button:hover {
    transform: scale(1.1);
    box-shadow: 0 0 30px rgba(0, 224, 255, 0.4);
}

.item-content {
    padding: 1rem;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.text-content h3 {
    font-size: 1rem;
    margin: 0 0 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.text-content p {
    font-size: 0.85rem;
    margin: 0;
    color: var(--text-secondary);
}

/* Track Progress */
.track-progress {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.25rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    border-radius: 2px;
}

.fill-75 {
    width: 75%;
}

.fill-45 {
    width: 45%;
}

.progress-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.play-time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* ===== Dynamic Sections Grid ===== */
.dynamic-sections-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

/* ===== Animations ===== */
@keyframes shimmer {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Particles Animation */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    animation: float 15s infinite ease-in-out;
    opacity: 0.2;
}

.particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { top: 70%; left: 30%; animation-delay: 1s; }
.particle:nth-child(3) { top: 40%; left: 50%; animation-delay: 2s; }
.particle:nth-child(4) { top: 60%; left: 70%; animation-delay: 3s; }
.particle:nth-child(5) { top: 30%; left: 90%; animation-delay: 4s; }
.particle:nth-child(6) { top: 80%; left: 20%; animation-delay: 5s; }
.particle:nth-child(7) { top: 50%; left: 40%; animation-delay: 6s; }
.particle:nth-child(8) { top: 10%; left: 60%; animation-delay: 7s; }
.particle:nth-child(9) { top: 90%; left: 80%; animation-delay: 8s; }
.particle:nth-child(10) { top: 25%; left: 15%; animation-delay: 9s; }
.particle:nth-child(11) { top: 75%; left: 35%; animation-delay: 10s; }
.particle:nth-child(12) { top: 45%; left: 55%; animation-delay: 11s; }
.particle:nth-child(13) { top: 65%; left: 75%; animation-delay: 12s; }
.particle:nth-child(14) { top: 35%; left: 95%; animation-delay: 13s; }
.particle:nth-child(15) { top: 85%; left: 25%; animation-delay: 14s; }

@keyframes float {
    0%, 100% {
        transform: translateY(0) translateX(0);
    }
    25% {
        transform: translateY(-30px) translateX(15px);
    }
    50% {
        transform: translateY(-15px) translateX(-15px);
    }
    75% {
        transform: translateY(30px) translateX(15px);
    }
}

/* ===== Footer Styles ===== */
.site-footer {
    background: rgba(13, 17, 23, 0.9);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-top: 4rem;
    width: 100%;
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.2);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.footer-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.copyright {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
}

.footer-nav {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.footer-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-nav a:hover {
    color: var(--neon-blue);
    text-decoration: underline;
}

/* ===== Responsive Styles ===== */
@media (max-width: 1200px) {
    .items {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 992px) {
    .items {
        grid-template-columns: repeat(3, 1fr);
    }

    .library-stats {
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .items {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-content {
        padding: 2rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .library-stats {
        gap: 1rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}

@media (max-width: 576px) {
    .items {
        grid-template-columns: 1fr;
    }

    .library-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat-item {
        width: 100%;
    }

    .search-filters {
        justify-content: center;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }
}

/* Reduce motion if user prefers */
@media (prefers-reduced-motion: reduce) {
    .hero-content::before,
    .item:hover .img-container img,
    .item:hover,
    .stat-item:hover,
    .particle {
        animation: none;
        transition: none;
        transform: none;
    }
}
