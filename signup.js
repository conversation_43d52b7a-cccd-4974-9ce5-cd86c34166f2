import { API, utils } from './api.js';

document.addEventListener('DOMContentLoaded', () => {
    // Initialize the signup form
    const signupForm = document.getElementById('signupForm');
    if (signupForm) {
        signupForm.addEventListener('submit', handleSignup);
    }
    
    // Check if user is already logged in
    if (utils.isAuthenticated()) {
        // Redirect to home page if already logged in
        window.location.href = 'index.html';
    }
});

/**
 * Handle signup form submission
 * @param {Event} event - The form submission event
 */
async function handleSignup(event) {
    event.preventDefault();
    
    // Get form values
    const formData = new FormData(event.target);
    const userData = Object.fromEntries(formData);
    
    // Basic validation
    if (userData.password !== userData.confirmPassword) {
        showMessage('Passwords do not match', true);
        return;
    }
    
    // Show loading indicator
    utils.showLoader();
    
    try {
        // Call the signup API
        const response = await API.signup(userData);
        
        if (response.success) {
            // Show success message
            showMessage('Signup successful! Redirecting to login...', false);
            
            // Redirect to login page
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
        } else {
            // Show error message
            showMessage(response.message || 'Signup failed. Please try again.', true);
        }
    } catch (error) {
        console.error('Signup error:', error);
        showMessage('An error occurred. Please try again later.', true);
    } finally {
        utils.hideLoader();
    }
}

/**
 * Display a message to the user
 * @param {string} message - The message to display
 * @param {boolean} isError - Whether the message is an error
 */
function showMessage(message, isError = false) {
    const messageElement = document.getElementById('message');
    if (messageElement) {
        messageElement.textContent = message;
        messageElement.className = `message ${isError ? 'error' : 'success'}`;
        messageElement.style.display = 'block';
        
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, 3000);
    } else {
        // Fallback to alert if message element doesn't exist
        if (isError) {
            alert('Error: ' + message);
        } else {
            alert(message);
        }
    }
}
