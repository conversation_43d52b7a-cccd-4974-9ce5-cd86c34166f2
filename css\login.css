/* Login Page Styles */
body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background-color: #1c1c1e;
    color: #eaeaea;
    font-family: "Plus Jakarta Sans", sans-serif;
}

.welcome-header {
    color: #2ca6f7;
    font-size: 2.5em;
    margin-top: 2rem;
    margin-bottom: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00E0FF, #FF006E);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
    text-align: center;
}

.login-container {
    background: linear-gradient(315deg, rgba(13, 17, 23, 0.95), rgba(25, 32, 44, 0.95));
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    width: 350px;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.login-container:hover {
    box-shadow: 0 6px 25px rgba(0, 224, 255, 0.2);
}

.login-container .logo img {
    width: 220px;
    height: 220px;
    display: block;
    margin: 5px auto 5px auto;
}

.login-container h1 {
    color: #eaeaea;
    font-size: 1.8em;
    margin-bottom: 1.5rem;
    text-align: center;
}

.input-group {
    margin-bottom: 1.2rem;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    padding-right: 25px;
    text-align: center;
}

.input-group input {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #eaeaea;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.input-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.input-group input:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.15);
}

.remember-forgot {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    gap: 2rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #eaeaea;
}

.forgot-password {
    color: #00E0FF;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.forgot-password:hover {
    opacity: 0.8;
}

/* CAPTCHA Container */
.captcha-container {
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.captcha-error {
    color: #ff3860;
    font-size: 0.85rem;
    margin-top: 8px;
    display: none;
}

/* Custom CAPTCHA Styling */
.custom-captcha {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 10px 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 10px;
    width: 100%;
    box-sizing: border-box;
}

.captcha-question {
    font-size: 1.1rem;
    font-weight: 500;
    margin-right: 10px;
    color: #eaeaea;
    flex-grow: 1;
}

#captchaAnswer {
    width: 80px;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #eaeaea;
    font-size: 0.9rem;
    margin-right: 10px;
}

.refresh-captcha {
    background: transparent;
    border: none;
    color: #00E0FF;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.refresh-captcha:hover {
    background: rgba(0, 224, 255, 0.1);
    transform: rotate(180deg);
}

.login-button {
    width: 100%;
    padding: 12px;
    background: linear-gradient(45deg, #00E0FF, #FF006E);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
}

.signup-link {
    margin-top: 1.5rem;
    color: #eaeaea;
    text-align: center;
}

.signup-link a {
    color: #00E0FF;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.signup-link a:hover {
    opacity: 0.8;
}

.message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    border-radius: 5px;
    color: #fff;
    font-weight: bold;
    display: none;
}

.message.error {
    background-color: #ff3b30;
}

.message.success {
    background-color: #34c759;
}

.loader {
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid #00E0FF;
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;
    margin: 20px auto;
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Media Query for Tablet Viewport */
@media (max-width: 1024px) {
    .logo img {
        width: 100px;
        height: 100px;
    }

    .menu li a {
        font-size: 18px;
    }

    .user-profile img {
        width: 40px;
        height: 40px;
    }

    .item {
        flex: 1 0 calc(33.33% - 13.33px);
    }
}

/* Media Query for Mobile Viewport */
@media (max-width: 768px) {
    .logo img {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 480px) {
    .logo img {
        width: 100px;
        height: 100px;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    .container {
        width: 90%;
    }
    .item {
        flex: 1 0 calc(33.33% - 20px);
    }
}
