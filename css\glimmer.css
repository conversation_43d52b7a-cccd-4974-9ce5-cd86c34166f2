/* Glimmer Effects */
.nav-item.active::after {
    content: '';
    display: block;
    height: 4px;
    background: var(--glimmer-gradient);
    animation: glimmer var(--glimmer-duration) linear infinite;
}

@keyframes glimmer {
    0% {
        background-position: -100% 0;
        box-shadow: var(--glow-shadow);
    }
    50% {
        box-shadow: var(--glow-shadow-alt);
    }
    100% {
        background-position: 200% 0;
        box-shadow: var(--glow-shadow);
    }
}

/* Add relative positioning to menu items */
.menu li {
    position: relative;
}

/* Desktop navigation glimmer */
.menu li a[aria-current="page"] {
    position: relative;
    color: var(--text-color); /* Reset to default text color instead of blue */
    transition: all var(--transition-speed) var(--transition-timing);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.2); /* Subtle glow effect */
}

.menu li a[aria-current="page"]:hover {
    transform: translateY(-1px);
}

.menu li a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(
        90deg,
        var(--neon-blue),
        var(--cosmic-pink),
        var(--neon-blue)
    );
    background-size: 200% 100%;
    animation: shimmer 3s linear infinite;
    border-radius: var(--border-radius-sm);
    box-shadow: 
        0 0 10px var(--neon-blue),
        0 0 20px rgba(0, 224, 255, 0.3);
    transform-origin: center;
    transition: transform var(--transition-speed) var(--transition-timing),
                box-shadow var(--transition-speed) var(--transition-timing);
}

@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Hover effect for non-active menu items */
.menu li a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(
        90deg,
        var(--neon-blue),
        var(--cosmic-pink)
    );
    transition: all var(--transition-speed) var(--transition-timing);
    transform: translateX(-50%);
    opacity: 0;
    border-radius: var(--border-radius-sm);
}

.menu li a:not([aria-current="page"]):hover::after {
    width: 100%;
    opacity: 1;
    box-shadow: 
        0 0 10px var(--neon-blue),
        0 0 20px rgba(0, 224, 255, 0.3);
}

/* Mobile navigation glimmer */
.mobile-nav .nav-item[aria-current="page"] {
    position: relative;
    color: var(--text-color);
    transition: transform var(--transition-speed) var(--transition-timing);
}

.mobile-nav .nav-item[aria-current="page"]:hover {
    transform: translateY(-1px);
}

.mobile-nav .nav-item[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(
        90deg,
        var(--neon-blue),
        var(--cosmic-pink),
        var(--neon-blue)
    );
    background-size: 200% 100%;
    animation: shimmer 3s linear infinite;
    border-radius: var(--border-radius-sm);
    box-shadow: 
        0 0 10px var(--neon-blue),
        0 0 20px rgba(0, 224, 255, 0.3);
    transform-origin: center;
    transition: transform var(--transition-speed) var(--transition-timing),
                box-shadow var(--transition-speed) var(--transition-timing);
}

.mobile-nav .nav-item[aria-current="page"]:hover::after {
    transform: scaleX(1.05);
    box-shadow: var(--glow-shadow-alt);
}

/* Reduce motion if user prefers */
@media (prefers-reduced-motion: reduce) {
    .menu li a[aria-current="page"]::after,
    .mobile-nav .nav-item[aria-current="page"]::after {
        animation: none;
        background-position: 0 0;
    }
}
