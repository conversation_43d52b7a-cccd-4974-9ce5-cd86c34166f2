<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Library Debug - Banshee Music App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #0D1117;
            color: white;
        }
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #161B22;
        }
        button {
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .result {
            background-color: #0D1117;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 3px solid #00E0FF;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #FF006E;
            background-color: #2D1B1B;
        }
    </style>
</head>
<body>
    <h1>🎵 Library Manager Debug</h1>
    
    <div class="debug-section">
        <h2>🔍 API Tests</h2>
        <button onclick="testPlaylists()">Test Get Playlists</button>
        <button onclick="testFavorites()">Test Get Favorites</button>
        <button onclick="testRecentlyPlayed()">Test Get Recently Played</button>
        <div id="api-result" class="result"></div>
    </div>

    <div class="debug-section">
        <h2>🎨 UI Tests</h2>
        <button onclick="testRenderPlaylists()">Test Render Playlists</button>
        <button onclick="testRenderFavorites()">Test Render Favorites</button>
        <div id="ui-result" class="result"></div>
        
        <!-- Test containers -->
        <div id="test-playlists-container"></div>
        <div id="test-favorites-container"></div>
    </div>

    <script type="module">
        import libraryAPI from './js/library/api-backend.js';
        import libraryUI from './js/library/ui.js';
        import { Song, Playlist } from './js/library/models.js';

        window.libraryAPI = libraryAPI;
        window.libraryUI = libraryUI;
        window.Song = Song;
        window.Playlist = Playlist;

        function displayResult(elementId, result, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `result ${isError ? 'error' : ''}`;
        }

        window.testPlaylists = async function() {
            try {
                console.log('Testing playlists API...');
                const playlists = await libraryAPI.getPlaylists();
                console.log('Playlists result:', playlists);
                displayResult('api-result', { success: true, playlists });
            } catch (error) {
                console.error('Playlists error:', error);
                displayResult('api-result', { success: false, error: error.message }, true);
            }
        };

        window.testFavorites = async function() {
            try {
                console.log('Testing favorites API...');
                const favorites = await libraryAPI.getFavorites();
                console.log('Favorites result:', favorites);
                displayResult('api-result', { success: true, favorites });
            } catch (error) {
                console.error('Favorites error:', error);
                displayResult('api-result', { success: false, error: error.message }, true);
            }
        };

        window.testRecentlyPlayed = async function() {
            try {
                console.log('Testing recently played API...');
                const recentlyPlayed = await libraryAPI.getRecentlyPlayed();
                console.log('Recently played result:', recentlyPlayed);
                displayResult('api-result', { success: true, recentlyPlayed });
            } catch (error) {
                console.error('Recently played error:', error);
                displayResult('api-result', { success: false, error: error.message }, true);
            }
        };

        window.testRenderPlaylists = async function() {
            try {
                console.log('Testing render playlists...');
                
                // Create a test container
                const container = document.getElementById('test-playlists-container');
                container.innerHTML = '<div id="playlists-container"></div>';
                
                // Test the render function
                await libraryUI.renderPlaylists();
                
                const playlistsContainer = document.getElementById('playlists-container');
                const result = {
                    success: true,
                    containerExists: !!playlistsContainer,
                    containerContent: playlistsContainer ? playlistsContainer.innerHTML : 'No container',
                    childrenCount: playlistsContainer ? playlistsContainer.children.length : 0
                };
                
                console.log('Render playlists result:', result);
                displayResult('ui-result', result);
            } catch (error) {
                console.error('Render playlists error:', error);
                displayResult('ui-result', { success: false, error: error.message }, true);
            }
        };

        window.testRenderFavorites = async function() {
            try {
                console.log('Testing render favorites...');
                
                // Create a test container
                const container = document.getElementById('test-favorites-container');
                container.innerHTML = '<div id="favorites-container"></div>';
                
                // Test the render function
                await libraryUI.renderFavorites();
                
                const favoritesContainer = document.getElementById('favorites-container');
                const result = {
                    success: true,
                    containerExists: !!favoritesContainer,
                    containerContent: favoritesContainer ? favoritesContainer.innerHTML.substring(0, 200) + '...' : 'No container',
                    childrenCount: favoritesContainer ? favoritesContainer.children.length : 0
                };
                
                console.log('Render favorites result:', result);
                displayResult('ui-result', result);
            } catch (error) {
                console.error('Render favorites error:', error);
                displayResult('ui-result', { success: false, error: error.message }, true);
            }
        };

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('Library debug page loaded');
            console.log('libraryAPI:', libraryAPI);
            console.log('libraryUI:', libraryUI);
        });
    </script>
</body>
</html>
