<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="profile.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <a href="../index.html" aria-label="Go to home page">
                    <img src="../imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
                </a>
            </div>
            <ul class="menu" aria-label="Main Navigation">
                <li><a href="../index.html">Home</a></li>
                <li><a href="../explore.html">Explore</a></li>
                <li><a href="../library.html">Library</a></li>
                <li><a href="../player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu">
                    <img class="profile-icon" src="../imgs/profile-icon-B.png" alt="User Profile" loading="lazy">
                </button>
                <div class="dropdown">
                    <ul>
                        <li><a href="profile.html" aria-current="page">Profile</a></li>
                        <li><a href="../settings.html">Settings</a></li>
                        <li><a href="../notifications.html">Notifications</a></li>
                        <li><a href="#" class="logout">Logout</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <div class="profile-container">
        <div class="profile-header">
            <div class="profile-cover">
                <div class="profile-avatar">
                    <img src="../imgs/profile-icon-B.png" alt="User Avatar">
                    <button type="button" class="change-avatar-btn">
                        <i class="fas fa-camera"></i>
                    </button>
                </div>
            </div>
            <div class="profile-info">
                <h1 id="profile-name">Loading...</h1>
                <p id="profile-email">Loading...</p>
                <div class="profile-stats">
                    <div class="stat">
                        <span class="stat-value">0</span>
                        <span class="stat-label">Playlists</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">0</span>
                        <span class="stat-label">Followers</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">0</span>
                        <span class="stat-label">Following</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="profile-content">
            <div class="profile-sidebar">
                <ul class="profile-nav">
                    <li><a href="#account" class="active">Account Settings</a></li>
                    <li><a href="#security">Security</a></li>
                    <li><a href="#subscription">Subscription</a></li>
                    <li><a href="#notifications">Notifications</a></li>
                    <li><a href="#privacy">Privacy</a></li>
                </ul>
            </div>
            
            <div class="profile-main">
                <div id="account" class="profile-section active">
                    <h2>Account Settings</h2>
                    
                    <div id="account-error" class="auth-error"></div>
                    <div id="account-success" class="auth-success"></div>
                    
                    <form id="account-form" class="profile-form">
                        <div class="form-group">
                            <label for="profile-name-input">Full Name</label>
                            <div class="input-with-icon">
                                <i class="fas fa-user"></i>
                                <input type="text" id="profile-name-input" name="name" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="profile-email-input">Email</label>
                            <div class="input-with-icon">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="profile-email-input" name="email" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="profile-username">Username</label>
                            <div class="input-with-icon">
                                <i class="fas fa-at"></i>
                                <input type="text" id="profile-username" name="username">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="profile-bio">Bio</label>
                            <textarea id="profile-bio" name="bio" rows="4"></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="auth-button">Save Changes</button>
                        </div>
                    </form>
                </div>
                
                <div id="security" class="profile-section">
                    <h2>Security</h2>
                    
                    <div id="security-error" class="auth-error"></div>
                    <div id="security-success" class="auth-success"></div>
                    
                    <form id="password-form" class="profile-form">
                        <h3>Change Password</h3>
                        
                        <div class="form-group">
                            <label for="current-password">Current Password</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="current-password" name="current-password" required>
                                <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="new-password">New Password</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="new-password" name="new-password" required>
                                <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength">
                                <div class="strength-meter">
                                    <div class="strength-segment"></div>
                                    <div class="strength-segment"></div>
                                    <div class="strength-segment"></div>
                                    <div class="strength-segment"></div>
                                </div>
                                <span class="strength-text">Password strength</span>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm-new-password">Confirm New Password</label>
                            <div class="input-with-icon">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="confirm-new-password" name="confirm-new-password" required>
                                <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="auth-button">Update Password</button>
                        </div>
                    </form>
                    
                    <div class="security-section">
                        <h3>Login Sessions</h3>
                        <div class="session-list">
                            <div class="session-item current">
                                <div class="session-info">
                                    <i class="fas fa-desktop"></i>
                                    <div>
                                        <h4>Current Session</h4>
                                        <p>Windows • Chrome • <span id="current-ip">127.0.0.1</span></p>
                                        <p class="session-time" id="current-time">Active now</p>
                                    </div>
                                </div>
                                <button type="button" class="session-action" disabled>Current</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="subscription" class="profile-section">
                    <h2>Subscription</h2>
                    
                    <div class="subscription-info">
                        <div class="subscription-header">
                            <h3>Current Plan</h3>
                            <span class="subscription-status free">Free</span>
                        </div>
                        
                        <p>You are currently on the Free plan. Upgrade to Premium to unlock all features.</p>
                        
                        <a href="../subscription.html" class="auth-button">Upgrade to Premium</a>
                    </div>
                    
                    <div class="subscription-features">
                        <h3>Plan Comparison</h3>
                        
                        <div class="plan-comparison">
                            <div class="plan-column">
                                <div class="plan-header">
                                    <h4>Free</h4>
                                    <span class="plan-price">$0</span>
                                </div>
                                <ul class="plan-features">
                                    <li><i class="fas fa-check"></i> Ad-supported listening</li>
                                    <li><i class="fas fa-check"></i> Basic audio quality</li>
                                    <li><i class="fas fa-check"></i> Limited skips</li>
                                    <li><i class="fas fa-times"></i> No offline listening</li>
                                    <li><i class="fas fa-times"></i> No ad-free experience</li>
                                </ul>
                            </div>
                            
                            <div class="plan-column premium">
                                <div class="plan-header">
                                    <h4>Premium</h4>
                                    <span class="plan-price">$2.99<span class="plan-period">/month</span></span>
                                </div>
                                <ul class="plan-features">
                                    <li><i class="fas fa-check"></i> Ad-free listening</li>
                                    <li><i class="fas fa-check"></i> High-quality audio</li>
                                    <li><i class="fas fa-check"></i> Unlimited skips</li>
                                    <li><i class="fas fa-check"></i> Offline listening</li>
                                    <li><i class="fas fa-check"></i> Exclusive content</li>
                                </ul>
                            </div>
                            
                            <div class="plan-column artist">
                                <div class="plan-header">
                                    <h4>Artist</h4>
                                    <span class="plan-price">$4.99<span class="plan-period">/month</span></span>
                                </div>
                                <ul class="plan-features">
                                    <li><i class="fas fa-check"></i> All Premium features</li>
                                    <li><i class="fas fa-check"></i> Upload unlimited tracks</li>
                                    <li><i class="fas fa-check"></i> Artist profile customization</li>
                                    <li><i class="fas fa-check"></i> Analytics dashboard</li>
                                    <li><i class="fas fa-check"></i> Monetization options</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="notifications" class="profile-section">
                    <h2>Notifications</h2>
                    
                    <div id="notifications-success" class="auth-success"></div>
                    
                    <form id="notifications-form" class="profile-form">
                        <div class="notification-group">
                            <h3>Email Notifications</h3>
                            
                            <div class="notification-option">
                                <div>
                                    <h4>New Releases</h4>
                                    <p>Get notified when artists you follow release new music</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="email_new_releases" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="notification-option">
                                <div>
                                    <h4>Playlist Updates</h4>
                                    <p>Get notified when playlists you follow are updated</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="email_playlist_updates" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="notification-option">
                                <div>
                                    <h4>Account Updates</h4>
                                    <p>Get notified about important account updates</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="email_account_updates" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="notification-option">
                                <div>
                                    <h4>Marketing</h4>
                                    <p>Receive promotional emails and special offers</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="email_marketing">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="notification-group">
                            <h3>Push Notifications</h3>
                            
                            <div class="notification-option">
                                <div>
                                    <h4>New Releases</h4>
                                    <p>Get notified when artists you follow release new music</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="push_new_releases" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="notification-option">
                                <div>
                                    <h4>Playlist Updates</h4>
                                    <p>Get notified when playlists you follow are updated</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="push_playlist_updates" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="notification-option">
                                <div>
                                    <h4>Social Activity</h4>
                                    <p>Get notified about follows, comments, and likes</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="push_social_activity" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="auth-button">Save Preferences</button>
                        </div>
                    </form>
                </div>
                
                <div id="privacy" class="profile-section">
                    <h2>Privacy</h2>
                    
                    <div id="privacy-success" class="auth-success"></div>
                    
                    <form id="privacy-form" class="profile-form">
                        <div class="privacy-group">
                            <h3>Profile Privacy</h3>
                            
                            <div class="privacy-option">
                                <div>
                                    <h4>Profile Visibility</h4>
                                    <p>Control who can see your profile</p>
                                </div>
                                <select name="profile_visibility" class="privacy-select">
                                    <option value="public">Public</option>
                                    <option value="followers">Followers Only</option>
                                    <option value="private">Private</option>
                                </select>
                            </div>
                            
                            <div class="privacy-option">
                                <div>
                                    <h4>Listening Activity</h4>
                                    <p>Share what you're listening to with followers</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="share_listening_activity" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="privacy-option">
                                <div>
                                    <h4>Recently Played</h4>
                                    <p>Show your recently played tracks on your profile</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="show_recently_played" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="privacy-group">
                            <h3>Data & Personalization</h3>
                            
                            <div class="privacy-option">
                                <div>
                                    <h4>Personalized Recommendations</h4>
                                    <p>Use your listening history to improve recommendations</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="personalized_recommendations" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            
                            <div class="privacy-option">
                                <div>
                                    <h4>Data Collection</h4>
                                    <p>Allow collection of usage data to improve our service</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="data_collection" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="auth-button">Save Privacy Settings</button>
                        </div>
                    </form>
                    
                    <div class="data-actions">
                        <h3>Your Data</h3>
                        <p>You can download your data or delete your account at any time.</p>
                        
                        <div class="data-buttons">
                            <button type="button" class="secondary-button" id="download-data-btn">
                                <i class="fas fa-download"></i> Download Your Data
                            </button>
                            <button type="button" class="danger-button" id="delete-account-btn">
                                <i class="fas fa-trash-alt"></i> Delete Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delete Account Confirmation Modal -->
    <div id="delete-account-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Delete Account</h2>
                <button type="button" class="close-modal" aria-label="Close modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="delete-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>This action is permanent and cannot be undone. All your data, including playlists, favorites, and account information will be permanently deleted.</p>
                </div>
                
                <form id="delete-account-form">
                    <div class="form-group">
                        <label for="delete-password">Enter your password to confirm</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="delete-password" name="password" required>
                        </div>
                    </div>
                    
                    <div class="form-group checkbox">
                        <input type="checkbox" id="delete-confirm" name="confirm" required>
                        <label for="delete-confirm">I understand that this action is permanent and cannot be undone</label>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="cancel-btn">Cancel</button>
                        <button type="submit" class="danger-button">Delete Account</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="auth.js"></script>
    <script src="profile.js"></script>
</body>
</html>
