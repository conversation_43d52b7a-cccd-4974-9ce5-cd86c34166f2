/* Login Page Styles */
body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background-color: #1c1c1e;
    color: #eaeaea;
    font-family: "Plus Jakarta Sans", sans-serif;
}

.welcome-header {
    color: #2ca6f7;
    font-size: 2.5em;
    margin-top: 2rem;
    margin-bottom: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00E0FF, #FF006E);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
    text-align: center;
}

.login-container {
    background: linear-gradient(315deg, rgba(13, 17, 23, 0.95), rgba(25, 32, 44, 0.95));
    padding: 40px;

/* Demo Credentials Box */
.demo-credentials {
    background: rgba(0, 224, 255, 0.1);
    border: 1px solid rgba(0, 224, 255, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.demo-credentials:hover {
    background: rgba(0, 224, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.2);
}

.demo-credentials h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #00E0FF;
    font-size: 1.2rem;
}

.demo-credentials p {
    margin: 5px 0;
    font-size: 0.9rem;
}

.demo-credentials .demo-note {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 10px;
    font-style: italic;
}
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    width: 350px;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.login-container:hover {
    box-shadow: 0 6px 25px rgba(0, 224, 255, 0.2);
}

.login-container .logo img {
    width: 220px;
    height: 220px;
    display: block;
    margin: 5px auto 5px auto;
}

.login-container h1 {
    color: #eaeaea;
    font-size: 1.8em;
    margin-bottom: 1.5rem;
    text-align: center;
}

.input-group {
    margin-bottom: 1.2rem;
    width: 90%;
    margin-left: auto;
    margin-right: auto;
    padding-right: 25px;
    text-align: center;
}

.input-group input {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #eaeaea;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.input-group input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.input-group input:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.15);
}

.remember-forgot {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    gap: 2rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #eaeaea;
}

.forgot-password {
    color: #00E0FF;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.forgot-password:hover {
    opacity: 0.8;
}

/* CAPTCHA Styles */
.captcha-container {
    margin: 15px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.captcha-question {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 10px;
    color: #eaeaea;
    text-align: center;
}

.captcha-input-group {
    display: flex;
    align-items: center;
}

#captchaAnswer {
    flex: 1;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: #eaeaea;
    font-size: 1rem;
    margin-right: 10px;
}

.refresh-captcha {
    background: transparent;
    border: none;
    color: #00E0FF;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.refresh-captcha:hover {
    background: rgba(0, 224, 255, 0.1);
    transform: rotate(180deg);
}

.captcha-error {
    color: #ff3860;
    font-size: 0.85rem;
    margin-top: 8px;
    display: none;
    text-align: left;
}

/* Loader Styles */
.loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loader {
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 5px solid #00E0FF;
    width: 50px;
    height: 50px;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Message Styles */
.message {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 24px;
    border-radius: 8px;
    background-color: #333;
    color: white;
    font-size: 0.9rem;
    z-index: 1000;
    display: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.3s ease-out;
}

.message.error {
    background-color: #ff3860;
}

.message.success {
    background-color: #34c759;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, 20px); }
    to { opacity: 1; transform: translate(-50%, 0); }
}

/* Media Query for Tablet Viewport */
@media (max-width: 1024px) {
    .logo img {
        width: 100px;
        height: 100px;
    }

    .menu li a {
        font-size: 18px;
    }

    .user-profile img {
        width: 40px;
        height: 40px;
    }

    .item {
        flex: 1 0 calc(33.33% - 13.33px);
    }
}

/* Media Query for Mobile Viewport */
@media (max-width: 768px) {
    .logo img {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 480px) {
    .logo img {
        width: 100px;
        height: 100px;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    .container {
        width: 90%;
    }
    .item {
        flex: 1 0 calc(33.33% - 20px);
    }
}
