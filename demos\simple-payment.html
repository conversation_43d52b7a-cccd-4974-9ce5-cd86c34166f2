<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Plus Jakarta Sans', sans-serif;
            background-color: #1c1c1e;
            color: #eaeaea;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .welcome-header {
            color: #2ca6f7;
            font-size: 2.5em;
            margin-top: 2rem;
            margin-bottom: 2rem;
            font-weight: 700;
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: none;
            text-align: center;
        }
        
        .payment-container {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .plan-summary {
            background: linear-gradient(315deg, rgba(13, 17, 23, 0.95), rgba(25, 32, 44, 0.95));
            border-radius: 12px;
            padding: 2rem;
            flex: 1;
            min-width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .plan-summary h2 {
            margin-top: 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 1rem;
        }
        
        .selected-plan {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .plan-name {
            font-weight: 600;
            font-size: 1.2rem;
        }
        
        .plan-price {
            color: #00E0FF;
        }
        
        .change-plan {
            color: #00E0FF;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .plan-features {
            margin-top: 2rem;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .feature i {
            margin-right: 1rem;
            color: #00E0FF;
        }
        
        .payment-details {
            background: linear-gradient(315deg, rgba(13, 17, 23, 0.95), rgba(25, 32, 44, 0.95));
            border-radius: 12px;
            padding: 2rem;
            flex: 2;
            min-width: 500px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .payment-details h2 {
            margin-top: 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 1rem;
        }
        
        .payment-methods {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .payment-method {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method.active {
            background: rgba(0, 224, 255, 0.1);
            border-color: #00E0FF;
        }
        
        .payment-method i {
            margin-right: 0.5rem;
        }
        
        .card-payment-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .input-group {
            display: flex;
            flex-direction: column;
        }
        
        .input-group label {
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: #aaa;
        }
        
        .input-group input, .input-group select {
            padding: 0.75rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            color: #eaeaea;
            font-size: 1rem;
        }
        
        .card-input-wrapper {
            position: relative;
        }
        
        .card-type-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #aaa;
        }
        
        .input-row {
            display: flex;
            gap: 1rem;
        }
        
        .input-row .input-group {
            flex: 1;
        }
        
        .cvc-input-wrapper {
            position: relative;
        }
        
        .cvc-tooltip {
            position: relative;
            display: inline-block;
        }
        
        .cvc-tooltip i {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #aaa;
            cursor: help;
        }
        
        .tooltip-text {
            visibility: hidden;
            width: 120px;
            background-color: #333;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            margin-left: -60px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.8rem;
        }
        
        .cvc-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        .billing-address {
            margin-top: 1.5rem;
        }
        
        .billing-address h3 {
            margin-top: 0;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .terms-group {
            margin-top: 1.5rem;
        }
        
        .terms-label {
            display: flex;
            align-items: flex-start;
            font-size: 0.9rem;
            color: #aaa;
        }
        
        .terms-label input {
            margin-right: 0.5rem;
            margin-top: 0.2rem;
        }
        
        .terms-link {
            color: #00E0FF;
            text-decoration: none;
        }
        
        .secure-payment {
            display: flex;
            align-items: center;
            margin-top: 1.5rem;
            color: #aaa;
            font-size: 0.9rem;
        }
        
        .secure-payment i {
            margin-right: 0.5rem;
            color: #00E0FF;
        }
        
        .total-section {
            margin-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 1rem;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .total-row.total {
            font-weight: 700;
            font-size: 1.2rem;
            margin-top: 1rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 1rem;
        }
        
        .button-group {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
        }
        
        .back-button {
            display: flex;
            align-items: center;
            color: #eaeaea;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .back-button i {
            margin-right: 0.5rem;
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .payment-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.75rem 2rem;
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
        }
        
        .button-price {
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .payment-error {
            background-color: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
            color: #ff6b6b;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }
        
        .toast {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 1rem 2rem;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
        }
        
        .toast.success {
            background-color: #4CAF50;
        }
        
        .toast.error {
            background-color: #F44336;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        
        @media (max-width: 768px) {
            .payment-container {
                flex-direction: column;
            }
            
            .payment-details {
                min-width: auto;
            }
            
            .input-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <h1 class="welcome-header">Subscribe to Banshee Music</h1>
    
    <div class="payment-container">
        <div class="plan-summary">
            <h2>Order Summary</h2>
            <div class="selected-plan">
                <div class="plan-info">
                    <span class="plan-name" id="selectedPlanName">Premium Plan</span>
                    <span class="plan-price" id="selectedPlanPrice">$2.99/month</span>
                </div>
                <a href="simple-subscription.html" class="change-plan">Change Plan</a>
            </div>
            <div class="plan-features">
                <div class="feature">
                    <i class="fas fa-music"></i>
                    <span>Ad-free music streaming</span>
                </div>
                <div class="feature">
                    <i class="fas fa-download"></i>
                    <span>Offline listening</span>
                </div>
            </div>
        </div>

        <div class="payment-details">
            <h2>Payment Method</h2>
            <div class="payment-methods">
                <button type="button" class="payment-method active">
                    <i class="fas fa-credit-card"></i>
                    <span>Credit Card</span>
                </button>
                <button type="button" class="payment-method">
                    <i class="fab fa-paypal"></i>
                    <span>PayPal</span>
                </button>
            </div>

            <form id="paymentForm" class="card-payment-form">
                <div class="input-group">
                    <label for="card-name">Name on Card</label>
                    <input type="text" id="card-name" name="card-name" 
                           placeholder="Enter cardholder name" required>
                </div>

                <div class="input-group card-number-group">
                    <label for="card-number">Card Number</label>
                    <div class="card-input-wrapper">
                        <input type="text" id="card-number" name="card-number" 
                               placeholder="1234 5678 9012 3456" required>
                        <i class="card-type-icon fab fa-cc-generic"></i>
                    </div>
                </div>

                <div class="input-row">
                    <div class="input-group">
                        <label for="expiry-date">Expiry Date</label>
                        <input type="text" id="expiry-date" name="expiry-date" 
                               placeholder="MM/YY" required>
                    </div>
                    <div class="input-group">
                        <label for="cvc">CVC</label>
                        <div class="cvc-input-wrapper">
                            <input type="text" id="cvc" name="cvc" 
                                   placeholder="123" required>
                            <div class="cvc-tooltip">
                                <i class="fas fa-question-circle"></i>
                                <span class="tooltip-text">3-digit code on back of card</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="billing-address">
                    <h3>Billing Address</h3>
                    <div class="input-group">
                        <label for="country">Country</label>
                        <select id="country" name="country" required>
                            <option value="">Select Country</option>
                            <option value="US">United States</option>
                            <option value="CA">Canada</option>
                            <option value="GB">United Kingdom</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <label for="zip">ZIP/Postal Code</label>
                        <input type="text" id="zip" name="zip" required>
                    </div>
                </div>

                <div class="terms-group">
                    <label class="terms-label">
                        <input type="checkbox" required>
                        <span>I agree to the <a href="#" class="terms-link">Terms & Conditions</a> and 
                        <a href="#" class="terms-link">Privacy Policy</a></span>
                    </label>
                </div>

                <div class="secure-payment">
                    <i class="fas fa-lock"></i>
                    <span>Secure payment powered by Stripe</span>
                </div>

                <div class="total-section">
                    <div class="total-row">
                        <span>Subtotal</span>
                        <span id="subtotal">$2.99</span>
                    </div>
                    <div class="total-row">
                        <span>Tax</span>
                        <span id="tax">$0.00</span>
                    </div>
                    <div class="total-row total">
                        <span>Total</span>
                        <span id="total">$2.99</span>
                    </div>
                </div>

                <div class="button-group">
                    <a href="simple-subscription.html" class="back-button">
                        <i class="fas fa-arrow-left"></i>
                        Back
                    </a>
                    <button type="submit" class="payment-button">
                        <span class="button-text">Subscribe Now</span>
                        <span class="button-price">$2.99/month</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Payment page loaded');
            
            // Load selected plan from session storage
            loadSelectedPlan();
            
            // Initialize card formatting
            initializeCardFormatting();
            
            // Initialize payment method selection
            initializePaymentMethods();
            
            // Initialize form submission
            const paymentForm = document.getElementById('paymentForm');
            if (paymentForm) {
                paymentForm.addEventListener('submit', handlePaymentSubmission);
            }
        });
        
        function loadSelectedPlan() {
            // Get selected plan from session storage
            const selectedPlanJson = sessionStorage.getItem('selectedPlan');
            
            if (!selectedPlanJson) {
                // If no plan is selected, redirect back to subscription page
                window.location.href = 'simple-subscription.html';
                return;
            }
            
            try {
                const selectedPlan = JSON.parse(selectedPlanJson);
                
                // Update UI with selected plan details
                document.getElementById('selectedPlanName').textContent = selectedPlan.name;
                document.getElementById('selectedPlanPrice').textContent = `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;
                
                // Update total section
                document.getElementById('subtotal').textContent = `$${selectedPlan.price.toFixed(2)}`;
                document.getElementById('total').textContent = `$${selectedPlan.price.toFixed(2)}`;
                
                // Update button price
                document.querySelector('.button-price').textContent = `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;
                
            } catch (error) {
                console.error('Error parsing selected plan:', error);
                showError('There was an error loading your selected plan. Please try again.');
            }
        }
        
        function initializeCardFormatting() {
            // Card number formatting
            const cardNumberInput = document.getElementById('card-number');
            if (cardNumberInput) {
                cardNumberInput.addEventListener('input', (e) => {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 16) value = value.slice(0, 16);
                    
                    // Format with spaces every 4 digits
                    const formatted = value.replace(/(\d{4})(?=\d)/g, '$1 ');
                    e.target.value = formatted;
                    
                    // Update card type icon based on first digits
                    updateCardTypeIcon(value);
                });
            }
            
            // Expiry date formatting (MM/YY)
            const expiryInput = document.getElementById('expiry-date');
            if (expiryInput) {
                expiryInput.addEventListener('input', (e) => {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 4) value = value.slice(0, 4);
                    
                    if (value.length > 2) {
                        value = value.slice(0, 2) + '/' + value.slice(2);
                    }
                    
                    e.target.value = value;
                });
            }
            
            // CVC formatting (3-4 digits)
            const cvcInput = document.getElementById('cvc');
            if (cvcInput) {
                cvcInput.addEventListener('input', (e) => {
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 4) value = value.slice(0, 4);
                    e.target.value = value;
                });
            }
        }
        
        function updateCardTypeIcon(cardNumber) {
            const iconElement = document.querySelector('.card-type-icon');
            if (!iconElement) return;
            
            // Remove all card type classes
            iconElement.className = 'card-type-icon fab';
            
            // Determine card type based on first digits
            if (/^4/.test(cardNumber)) {
                iconElement.classList.add('fa-cc-visa');
            } else if (/^5[1-5]/.test(cardNumber)) {
                iconElement.classList.add('fa-cc-mastercard');
            } else if (/^3[47]/.test(cardNumber)) {
                iconElement.classList.add('fa-cc-amex');
            } else if (/^6(?:011|5)/.test(cardNumber)) {
                iconElement.classList.add('fa-cc-discover');
            } else {
                iconElement.classList.add('fa-credit-card');
            }
        }
        
        function initializePaymentMethods() {
            const paymentMethods = document.querySelectorAll('.payment-method');
            
            paymentMethods.forEach(method => {
                method.addEventListener('click', () => {
                    // Remove active class from all methods
                    paymentMethods.forEach(m => m.classList.remove('active'));
                    
                    // Add active class to clicked method
                    method.classList.add('active');
                    
                    // Show/hide appropriate form based on payment method
                    const isPayPal = method.querySelector('.fab.fa-paypal');
                    const cardForm = document.querySelector('.card-payment-form');
                    
                    if (isPayPal) {
                        // For demo purposes, just show a message
                        showToast('PayPal integration would be implemented here');
                    }
                });
            });
        }
        
        function handlePaymentSubmission(event) {
            event.preventDefault();
            
            // Get subscription ID from session storage
            const subscriptionId = sessionStorage.getItem('subscriptionId');
            if (!subscriptionId) {
                showError('Subscription information not found. Please try again.');
                return;
            }
            
            // Show loading state
            const submitButton = event.target.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.querySelector('.button-text').textContent;
            submitButton.disabled = true;
            submitButton.querySelector('.button-text').textContent = 'Processing...';
            
            // Simulate payment processing
            setTimeout(() => {
                // Collect payment details from form
                const paymentDetails = {
                    cardName: document.getElementById('card-name').value,
                    cardNumber: document.getElementById('card-number').value.replace(/\s/g, ''),
                    expiryDate: document.getElementById('expiry-date').value,
                    cvc: document.getElementById('cvc').value,
                    billingAddress: {
                        country: document.getElementById('country').value,
                        zipCode: document.getElementById('zip').value
                    }
                };
                
                console.log('Payment details:', paymentDetails);
                
                // Generate a mock transaction ID
                const transactionId = 'txn_' + Math.random().toString(36).substring(2);
                sessionStorage.setItem('transactionId', transactionId);
                
                // Calculate next billing date (30 days from now)
                const nextBillingDate = new Date();
                nextBillingDate.setDate(nextBillingDate.getDate() + 30);
                sessionStorage.setItem('nextBillingDate', nextBillingDate.toISOString());
                
                // Show success message
                showToast('Payment processed successfully!');
                
                // Redirect to confirmation page
                setTimeout(() => {
                    window.location.href = 'simple-confirmation.html';
                }, 1500);
                
                // Reset button state
                submitButton.disabled = false;
                submitButton.querySelector('.button-text').textContent = originalButtonText;
            }, 2000);
        }
        
        function showError(message) {
            // Remove any existing error
            const existingError = document.querySelector('.payment-error');
            if (existingError) {
                existingError.remove();
            }
            
            // Create error element
            const errorElement = document.createElement('div');
            errorElement.className = 'payment-error';
            errorElement.textContent = message;
            
            // Add to page
            const form = document.getElementById('paymentForm');
            form.prepend(errorElement);
        }
        
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>
