/* Player Page Styles */
body {
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
    font-weight: 400;
    line-height: 1.5;
}

:root {
    --dynamic-primary: var(--neon-blue);
    --dynamic-primary-rgb: var(--neon-blue-rgb);
    --dynamic-secondary: var(--cosmic-pink);
    --dynamic-secondary-rgb: var(--cosmic-pink-rgb);
    --dynamic-accent: rgba(255, 255, 255, 0.1);
    --player-bg: rgba(13, 17, 23, 0.8);
}

.player-container {
    max-width: 600px;
    margin: 120px auto 30px auto; /* Increased top margin from 100px to 120px */
    padding: 20px;
    background: linear-gradient(145deg, var(--player-bg), rgba(255, 255, 255, 0.02));
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
    position: relative;
}

.player-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

#player-heading {
    font-size: 1.8em;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    letter-spacing: 0.5px;
}

.album-art {
    width: 220px;
    height: 220px;
    border-radius: 15px;
    margin: 5px auto;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.album-art:hover {
    transform: scale(1.05);
    box-shadow:
        0 20px 30px rgba(0, 0, 0, 0.3),
        0 0 30px var(--dynamic-primary);
}

.track-info {
    text-align: center;
    margin: 10px 0;
    transition: opacity 0.3s ease;
}

#song-title {
    font-size: 1.4em;
    font-weight: 600;
    margin-bottom: 4px;
    background: linear-gradient(45deg, var(--dynamic-primary), var(--dynamic-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    opacity: 0.9;
    transition: opacity 0.3s ease;
    letter-spacing: 0.5px;
}

#song-title:hover {
    opacity: 1;
}

#artist-name {
    color: rgba(255, 255, 255, 0.7);
    font-size: 1em;
    transition: color 0.3s ease;
    letter-spacing: 1px;
}

#artist-name:hover {
    color: rgba(255, 255, 255, 0.9);
}

.progress-container {
    width: 100%;
    margin: 10px 0;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    cursor: pointer;
    position: relative;
    transition: height 0.2s ease;
}

.progress-bar:hover {
    height: 6px;
}

.progress {
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--dynamic-primary), var(--dynamic-secondary));
    border-radius: 2px;
    position: relative;
}

.progress::after {
    content: '';
    position: absolute;
    right: -4px;
    top: 50%;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.progress-bar:hover .progress::after {
    opacity: 1;
}

.time {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
    margin-top: 5px;
    letter-spacing: 0.5px;
}

/* Main player controls container */
.player-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    margin: 20px 0;
    width: 100%;
}

.control-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
}

.control-button {
    background: transparent;
    border: none;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px; /* Set consistent width */
    height: 40px; /* Set consistent height */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 0;
    background: rgba(255, 255, 255, 0.1); /* Subtle background */
}

.control-button:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.2);
}

.control-button i {
    font-size: 18px; /* Slightly smaller than play/pause */
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Play/pause button specific styles */
#play-pause {
    width: 60px;
    height: 60px;
    background: linear-gradient(145deg, var(--dynamic-primary), var(--dynamic-secondary));
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

#play-pause i {
    font-size: 24px;
}

/* Skip buttons specific hover effect */
#prev:hover, #next:hover {
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Active state for skip buttons */
#prev:active, #next:active {
    transform: scale(0.95);
}

/* Ensure hidden class works properly with absolute positioning */
#play-pause i.hidden {
    display: none;
}

.additional-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
}

.volume-control i {
    color: var(--text-color);
    width: 20px;
}

.volume-control input[type="range"] {
    width: 100px;
    height: 4px;
    -webkit-appearance: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    cursor: pointer;
}

.volume-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
}

#hd-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 0.9em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

#hd-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

#hd-btn.active {
    background: linear-gradient(145deg, var(--dynamic-primary), var(--dynamic-secondary));
    box-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
}

@media (max-width: 768px) {
    .additional-controls {
        flex-direction: column;
        gap: 15px;
    }
}

/* Play/Pause animations */
@keyframes playingPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }
}

@keyframes standbyPulse {
    0% {
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(var(--dynamic-primary-rgb), 0.4);
        opacity: 0.85;
        transform: scale(1);
    }
    50% {
        box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.4),
            0 0 30px var(--dynamic-primary),
            0 0 50px var(--dynamic-secondary);
        opacity: 1;
        transform: scale(1.05);
    }
    100% {
        box-shadow:
            0 4px 15px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(var(--dynamic-primary-rgb), 0.4);
        opacity: 0.85;
        transform: scale(1);
    }
}

/* Only apply glowing animation when NOT playing */
#play-pause:not(.playing) {
    animation: standbyPulse 3s infinite;
}

/* Playing state - no glow */
#play-pause.playing {
    animation: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Different hover effects based on playing state */
#play-pause:not(.playing):hover {
    box-shadow:
        0 6px 25px rgba(0, 0, 0, 0.4),
        0 0 40px var(--dynamic-primary),
        0 0 60px var(--dynamic-secondary);
}

#play-pause.playing:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

/* Add playing state class */
#play-pause.playing {
    animation: playingPulse 2s infinite;
}

.hidden {
    opacity: 0;
    transform: scale(0.8);
    position: absolute;
}

/* Playlist Styling and Animation */
.playlist-section {
    margin-top: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.playlist-container {
    position: relative;
}

.playlist {
    list-style: none;
    padding: 0;
    margin: 0;
}

.playlist-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-radius: 10px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.03);
    transition: all 0.3s ease;
    cursor: pointer;
    animation: slideIn 0.3s ease-out forwards;
    opacity: 0;
    transform: translateX(-20px);
}

.playlist-item:nth-child(1) { animation-delay: 0.1s; }
.playlist-item:nth-child(2) { animation-delay: 0.2s; }
.playlist-item:nth-child(3) { animation-delay: 0.3s; }
.playlist-item:nth-child(4) { animation-delay: 0.4s; }
.playlist-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.playlist-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(5px);
}

.playlist-item.active {
    background: linear-gradient(90deg,
        rgba(var(--dynamic-primary), 0.2),
        rgba(var(--dynamic-secondary), 0.1)
    );
    border-left: 3px solid var(--dynamic-primary);
    padding-left: 12px;
}

.playlist-item-info {
    flex: 1;
    transition: transform 0.3s ease;
}

.playlist-item:hover .playlist-item-info {
    transform: translateX(5px);
}

.playlist-item-title {
    font-weight: 600;
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1em;
}

.playlist-item-artist {
    font-size: 0.85em;
    color: rgba(255, 255, 255, 0.6);
    margin: 2px 0 0 0;
}

.playlist-item-duration {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.85em;
    padding-left: 15px;
}

.playlist-controls {
    margin-left: auto;
    display: flex;
    gap: 8px;
}

.playlist-controls button {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.playlist-controls button:hover {
    background: rgba(255, 255, 255, 0.1);
}

#hd-btn {
    width: auto;
    height: 40px;
    background: linear-gradient(145deg, var(--neon-blue), var(--cosmic-pink));
    color: white;
    border: none;
    border-radius: 12px;
    padding: 6px 12px;
    font-size: 0.8em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

#hd-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(0, 224, 255, 0.3);
}

#hd-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 224, 255, 0.2);
}

#hd-btn.active {
    background: linear-gradient(145deg, var(--cosmic-pink), var(--neon-blue));
    animation: glow 1.5s infinite alternate;
    box-shadow: 0 0 20px rgba(0, 224, 255, 0.5);
}

@keyframes glow {
    from {
        box-shadow: 0 2px 8px rgba(0, 224, 255, 0.2);
    }
    to {
        box-shadow:
            0 4px 12px rgba(0, 224, 255, 0.4),
            0 4px 12px rgba(255, 0, 110, 0.3);
    }
}

/* Add smooth transitions for text elements */
.track-info {
    transition: all 0.3s ease;
}

#song-title, #artist-name {
    transition: all 0.3s ease;
}

/* Add a pulsing effect when playing */
@keyframes playingPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.playing .track-info {
    animation: playingPulse 2s infinite;
}

/* Add responsive design improvements */
@media (max-width: 768px) {
    .player-container {
        margin: 20px auto;
        padding: 20px;
        height: auto;
        min-height: 80vh;
    }

    .album-art {
        width: 180px;
        height: 180px;
    }

    .main-controls {
        gap: 15px;
    }
}

/* Add smooth page transitions */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Add hover effects for interactive elements */
.volume-controls:hover #volume::-webkit-slider-thumb {
    transform: scale(1.2);
    box-shadow: 0 0 10px var(--dynamic-primary);
}

/* Enhanced playlist styling */
.playlist-container {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 12px;
    margin-top: 8px;
}

/* Visualizer styles */
.visualizer-container {
    position: relative;
    width: 100%;
    height: 60px;
    margin-top: auto; /* Push to bottom */
    background: transparent;
    z-index: 1;
    overflow: hidden;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.2);
}

#audioVisualizer {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
}

/* Tooltip styles */
[data-tooltip] {
    position: relative;
}

[data-tooltip]::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-8px);
    padding: 6px 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 12px;
    border-radius: 6px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

[data-tooltip]:hover::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
}

.additional-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}

.volume-control {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
}

/* Fix for the primary controls to ensure horizontal layout */
.primary-controls {
    display: flex;
    flex-direction: row !important; /* Force horizontal layout */
    align-items: center;
    justify-content: center;
    gap: 20px;
    width: 100%;
}

#shuffle-btn {
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.08);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

#shuffle-btn i {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    z-index: 2;
    transition: all 0.3s ease;
    position: relative;
    /* Remove any default margins that might affect centering */
    margin: 0;
    padding: 0;
    line-height: 1;
}

#shuffle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(145deg, var(--dynamic-primary), var(--dynamic-secondary));
    opacity: 0;
    transition: opacity 0.3s ease;
}

#shuffle-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(0, 224, 255, 0.2);
}

#shuffle-btn:hover::before {
    opacity: 0.15;
}

#shuffle-btn:hover i {
    color: var(--dynamic-primary);
}

#shuffle-btn.active {
    background: linear-gradient(145deg, var(--dynamic-primary), var(--dynamic-secondary));
}

#shuffle-btn.active i {
    color: white;
}

#shuffle-btn.active:hover {
    transform: scale(1.1);
    box-shadow:
        0 0 20px rgba(0, 224, 255, 0.4),
        0 0 40px rgba(0, 224, 255, 0.2);
}

@keyframes shuffleActivate {
    0% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
    100% { transform: scale(1) rotate(360deg); }
}

#shuffle-btn.active i {
    animation: shuffleActivate 0.5s ease;
}

/* Album art animation when playing */
@keyframes albumRotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.album-art.playing {
    animation: albumRotate 20s linear infinite;
}

/* Could add these effects */
.visualizer {
    height: 50px;
    margin: 10px 0;
    display: flex;
    justify-content: center;
    gap: 2px;
}

.visualizer-bar {
    width: 4px;
    background: var(--dynamic-primary);
    border-radius: 2px;
    animation: visualize 0.5s infinite;
}

@keyframes visualize {
    0% { height: 10px; }
    50% { height: 40px; }
    100% { height: 10px; }
}

/* Secondary controls */
.secondary-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.secondary-controls button {
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
}

.secondary-controls button:hover {
    color: var(--dynamic-primary);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.secondary-controls button.active {
    color: var(--dynamic-primary);
    background: rgba(var(--dynamic-primary), 0.2);
}

#repeat-btn,
#lyrics-btn,
#queue-btn {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.08);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    /* Remove any default padding that might affect centering */
    padding: 0;
}

#repeat-btn i,
#lyrics-btn i,
#queue-btn i {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    z-index: 2;
    transition: all 0.3s ease;
    position: relative;
    /* Remove any default margins that might affect centering */
    margin: 0;
    padding: 0;
    line-height: 1;
    /* Ensure icon is centered */
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

#repeat-btn::before,
#lyrics-btn::before,
#queue-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(145deg, var(--dynamic-primary), var(--dynamic-secondary));
    opacity: 0;
    transition: opacity 0.3s ease;
}

#repeat-btn:hover,
#lyrics-btn:hover,
#queue-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(0, 224, 255, 0.2);
}

#repeat-btn:hover::before,
#lyrics-btn:hover::before,
#queue-btn:hover::before {
    opacity: 0.15;
}

#repeat-btn:hover i,
#lyrics-btn:hover i,
#queue-btn:hover i {
    color: var(--dynamic-primary);
}

#repeat-btn.active,
#lyrics-btn.active,
#queue-btn.active {
    background: linear-gradient(145deg, var(--dynamic-primary), var(--dynamic-secondary));
}

#repeat-btn.active i,
#lyrics-btn.active i,
#queue-btn.active i {
    color: white;
}

#repeat-btn.active:hover,
#lyrics-btn.active:hover,
#queue-btn.active:hover {
    transform: scale(1.1);
    box-shadow:
        0 0 20px rgba(0, 224, 255, 0.4),
        0 0 40px rgba(0, 224, 255, 0.2);
}

/* Animation for active state */
@keyframes iconActivate {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

#repeat-btn.active i,
#lyrics-btn.active i,
#queue-btn.active i {
    animation: iconActivate 0.3s ease;
}

/* Style the volume slider */
.volume-control input[type="range"] {
    -webkit-appearance: none;
    width: 120px;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    outline: none;
    transition: all 0.3s ease;
    position: relative;
}

/* Slider thumb styles - WebKit (Chrome, Safari) */
.volume-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    margin-top: -4px; /* This centers the thumb vertically */
}

/* Slider thumb styles - Mozilla (Firefox) */
.volume-control input[type="range"]::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    transform: translateY(0); /* Centers the thumb in Firefox */
}

/* Slider track styles */
.volume-control input[type="range"]::-webkit-slider-runnable-track {
    background: linear-gradient(90deg, var(--dynamic-primary), var(--dynamic-secondary));
    height: 4px;
    border-radius: 2px;
    cursor: pointer;
}

.volume-control input[type="range"]::-moz-range-track {
    background: linear-gradient(90deg, var(--dynamic-primary), var(--dynamic-secondary));
    height: 4px;
    border-radius: 2px;
    cursor: pointer;
}

/* Hover effects */
.volume-control input[type="range"]:hover::-webkit-slider-thumb {
    transform: scale(1.2);
    background: var(--dynamic-primary);
    box-shadow: 0 0 12px var(--dynamic-primary);
}

.volume-control input[type="range"]:hover::-moz-range-thumb {
    transform: scale(1.2);
    background: var(--dynamic-primary);
    box-shadow: 0 0 12px var(--dynamic-primary);
}

.volume-control input[type="range"]:hover {
    height: 4px; /* Keep consistent height on hover */
}

@keyframes trackChange {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.track-info {
    animation: trackChange 0.3s ease-out;
}

.album-art {
    animation: trackChange 0.3s ease-out;
}

.lyrics-panel {
    position: absolute;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    transition: right 0.3s ease;
    z-index: 1000;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
}

.lyrics-panel.active {
    right: 0;
}

.lyrics-header {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.lyrics-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    font-size: 1.1em;
    line-height: 1.6;
}

.lyrics-line {
    margin-bottom: 15px;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.lyrics-line.active {
    opacity: 1;
    color: var(--dynamic-primary);
    transform: scale(1.05);
}

.no-lyrics {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 20px;
}

/* Ensure the lyrics panel doesn't overflow the player container */
.player-container {
    position: relative;
    overflow: hidden;
}

.loading-state {
    position: absolute;
    inset: 0;
    background: var(--player-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.loading-state.active {
    opacity: 1;
    pointer-events: all;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--dynamic-accent);
    border-top-color: var(--dynamic-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.player-controls button {
    transform-origin: center;
    transition: transform 0.2s var(--transition-timing),
                color 0.2s var(--transition-timing),
                opacity 0.2s var(--transition-timing);
}

.player-controls button:active {
    transform: scale(0.95);
}

.track-info {
    transition: opacity 0.3s ease;
}

.track-info.changing {
    opacity: 0;
}

/* Add support for reduced motion */
@media (prefers-reduced-motion: reduce) {
    .player-controls button,
    .track-info {
        transition: none;
    }
}
