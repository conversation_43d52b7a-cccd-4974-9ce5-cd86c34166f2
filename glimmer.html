<svg width="100%" height="4" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="glimmer" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
      <stop offset="50%" style="stop-color:rgba(255,255,255,0.5);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
    </linearGradient>
  </defs>
  <rect width="100%" height="4" fill="url(#glimmer)">
    <animateTransform attributeName="transform" type="translate" from="-100" to="100" dur="2s" repeatCount="indefinite" />
  </rect>
</svg>