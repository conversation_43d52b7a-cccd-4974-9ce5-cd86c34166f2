<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API</title>
</head>
<body>
    <h1>API Test</h1>
    <div id="output"></div>

    <script type="module">
        import { API, utils } from './api.js';

        const output = document.getElementById('output');

        function log(message) {
            const p = document.createElement('p');
            p.textContent = message;
            output.appendChild(p);
        }

        async function testAPI() {
            try {
                log('Testing API...');
                
                // Test getSubscriptionPlans
                log('Testing getSubscriptionPlans...');
                const plans = await API.getSubscriptionPlans();
                log(`Found ${plans.length} plans`);
                log(JSON.stringify(plans[0], null, 2));
                
                // Test subscribeToPlan
                log('Testing subscribeToPlan...');
                const subscription = await API.subscribeToPlan('premium');
                log(`Subscription created: ${subscription.subscriptionId}`);
                
                // Test processPayment
                log('Testing processPayment...');
                const paymentDetails = {
                    cardName: 'Test User',
                    cardNumber: '****************',
                    expiryDate: '12/25',
                    cvc: '123',
                    billingAddress: {
                        country: 'US',
                        zipCode: '12345'
                    }
                };
                
                try {
                    const payment = await API.processPayment(paymentDetails, subscription.subscriptionId);
                    log(`Payment processed: ${payment.transactionId}`);
                } catch (error) {
                    log(`Payment error: ${error.message || 'Unknown error'}`);
                }
                
                // Test getSubscriptionDetails
                log('Testing getSubscriptionDetails...');
                const details = await API.getSubscriptionDetails(subscription.subscriptionId);
                log(`Subscription details: ${details.planName}`);
                
                log('All tests completed successfully!');
            } catch (error) {
                log(`ERROR: ${error.message || 'Unknown error'}`);
                console.error(error);
            }
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', testAPI);
    </script>
</body>
</html>
