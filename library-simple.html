<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Library - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* CSS Variables */
        :root {
            /* Colors */
            --background-color: #0D1117;
            --text-color: #ffffff;
            --electric-violet: #6F00FF;
            --neon-blue: #00E0FF;
            --neon-blue-rgb: 0, 224, 255;
            --cosmic-pink: #FF006E;
            --cosmic-pink-rgb: 255, 0, 110;
            --cyber-lime: #A7FF4A;
            --accent-color: var(--cosmic-pink);

            /* Gradients */
            --button-gradient: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
            --header-gradient: linear-gradient(to right, #13151a, #1a1d24);

            /* Shadows */
            --button-hover-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
            --button-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);

            /* Spacing */
            --container-padding: 20px;
            --section-gap: 2rem;

            /* Border Radius */
            --border-radius-lg: 25px;
            --border-radius-md: 16px;
            --border-radius-sm: 12px;

            /* Transitions */
            --transition-speed: 0.3s;
        }

        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--background-color);
            color: var(--text-color);
            font-family: "Plus Jakarta Sans", sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Header Styles */
        header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: var(--header-gradient);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            padding: 0 var(--container-padding);
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo img {
            height: 40px;
            width: auto;
        }

        .menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .menu a {
            color: var(--text-color);
            text-decoration: none;
            font-weight: 500;
            transition: color var(--transition-speed);
            position: relative;
        }

        .menu a:hover,
        .menu a[aria-current="page"] {
            color: var(--neon-blue);
        }

        .menu a[aria-current="page"]::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--button-gradient);
            border-radius: 1px;
        }

        .user-profile {
            position: relative;
        }

        .profile-button {
            background: none;
            border: none;
            cursor: pointer;
        }

        .profile-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
        }

        .dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: rgba(25, 32, 44, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--border-radius-sm);
            padding: 0.5rem 0;
            min-width: 150px;
            display: none;
        }

        .dropdown ul {
            list-style: none;
        }

        .dropdown a {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--text-color);
            text-decoration: none;
            transition: background-color var(--transition-speed);
        }

        .dropdown a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Footer */
        footer {
            background: var(--header-gradient);
            text-align: center;
            padding: 2rem;
            margin-top: 4rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        .library-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 120px 20px 20px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: var(--text-color);
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        .playlists-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .playlist-card {
            background: rgba(25, 32, 44, 0.4);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
        }
        .playlist-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            background: rgba(25, 32, 44, 0.6);
            border-color: rgba(0, 224, 255, 0.3);
        }
        .playlist-card-cover {
            position: relative;
            width: 100%;
            padding-top: 100%;
        }
        .playlist-card-cover img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .playlist-card-info {
            padding: 15px;
        }
        .playlist-card-info h3 {
            font-size: 1rem;
            margin: 0 0 5px;
            color: var(--text-color);
        }
        .playlist-card-info p {
            font-size: 0.8rem;
            margin: 0;
            color: rgba(255, 255, 255, 0.6);
        }
        .songs-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .songs-table th {
            text-align: left;
            padding: 12px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.7);
            font-weight: 600;
            font-size: 0.9rem;
        }
        .songs-table td {
            padding: 12px 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            color: var(--text-color);
            font-size: 0.9rem;
        }
        .songs-table tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }
        .song-info {
            display: flex;
            align-items: center;
        }
        .song-info img {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            margin-right: 10px;
            object-fit: cover;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: rgba(255, 255, 255, 0.6);
        }
        .error {
            text-align: center;
            padding: 40px;
            color: #ff3b30;
        }
        .empty {
            text-align: center;
            padding: 40px;
            color: rgba(255, 255, 255, 0.6);
        }
        .create-btn {
            background: var(--button-gradient);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 10px 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 20px;
        }
        .create-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 224, 255, 0.3), 0 5px 15px rgba(255, 0, 110, 0.3);
        }
    </style>
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <a href="index.html" aria-label="Go to home page">
                    <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
                </a>
            </div>
            <ul class="menu" aria-label="Main Navigation">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html" aria-current="page">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile" loading="lazy">
                </button>
                <div class="dropdown">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><a href="#" class="logout">Logout</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <div class="library-container">
        <div class="section">
            <h2>Your Playlists</h2>
            <button class="create-btn" onclick="createTestPlaylist()">
                <i class="fas fa-plus"></i> Create Playlist
            </button>
            <div id="playlists-section">
                <div class="loading">Loading playlists...</div>
            </div>
        </div>

        <div class="section">
            <h2>Favorite Songs</h2>
            <div id="favorites-section">
                <div class="loading">Loading favorites...</div>
            </div>
        </div>

        <div class="section">
            <h2>Recently Played</h2>
            <div id="recently-played-section">
                <div class="loading">Loading recently played...</div>
            </div>
        </div>
    </div>

    <footer>
        <p>&copy; 2023 Banshee Music App. All rights reserved.</p>
    </footer>

    <script type="module">
        import libraryAPI from './js/library/api-backend.js';
        import { Song, Playlist } from './js/library/models.js';

        // Helper function to create Song instance
        function createSongFromData(songData) {
            return new Song(songData);
        }

        // Helper function to create Playlist instance
        function createPlaylistFromData(playlistData) {
            const playlist = new Playlist(playlistData);
            if (playlistData.songs) {
                playlist.songs = playlistData.songs.map(song => createSongFromData(song));
            }
            return playlist;
        }

        // Render playlists
        async function renderPlaylists() {
            const section = document.getElementById('playlists-section');
            try {
                const playlists = await libraryAPI.getPlaylists();
                
                if (playlists.length === 0) {
                    section.innerHTML = '<div class="empty">No playlists yet. Create your first playlist!</div>';
                    return;
                }

                const grid = document.createElement('div');
                grid.className = 'playlists-grid';

                playlists.forEach(playlistData => {
                    const playlist = createPlaylistFromData(playlistData);
                    const card = document.createElement('div');
                    card.className = 'playlist-card';
                    card.innerHTML = `
                        <div class="playlist-card-cover">
                            <img src="${playlist.coverUrl || 'imgs/album-01.png'}" alt="${playlist.name}" loading="lazy">
                        </div>
                        <div class="playlist-card-info">
                            <h3>${playlist.name}</h3>
                            <p>${playlist.songs ? playlist.songs.length : 0} songs</p>
                        </div>
                    `;
                    grid.appendChild(card);
                });

                section.innerHTML = '';
                section.appendChild(grid);
            } catch (error) {
                console.error('Error loading playlists:', error);
                section.innerHTML = '<div class="error">Error loading playlists</div>';
            }
        }

        // Render favorites
        async function renderFavorites() {
            const section = document.getElementById('favorites-section');
            try {
                const favorites = await libraryAPI.getFavorites();
                
                if (favorites.length === 0) {
                    section.innerHTML = '<div class="empty">No favorite songs yet.</div>';
                    return;
                }

                const table = document.createElement('table');
                table.className = 'songs-table';
                table.innerHTML = `
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Title</th>
                            <th>Artist</th>
                            <th>Album</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                `;

                const tbody = table.querySelector('tbody');
                favorites.forEach((songData, index) => {
                    const song = createSongFromData(songData);
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>
                            <div class="song-info">
                                <img src="${song.coverUrl || 'imgs/album-01.png'}" alt="${song.title}" loading="lazy">
                                <span>${song.title}</span>
                            </div>
                        </td>
                        <td>${song.artist}</td>
                        <td>${song.album}</td>
                        <td>${song.formattedDuration()}</td>
                    `;
                    tbody.appendChild(row);
                });

                section.innerHTML = '';
                section.appendChild(table);
            } catch (error) {
                console.error('Error loading favorites:', error);
                section.innerHTML = '<div class="error">Error loading favorites</div>';
            }
        }

        // Render recently played
        async function renderRecentlyPlayed() {
            const section = document.getElementById('recently-played-section');
            try {
                const recentlyPlayed = await libraryAPI.getRecentlyPlayed();
                
                if (recentlyPlayed.length === 0) {
                    section.innerHTML = '<div class="empty">No recently played songs.</div>';
                    return;
                }

                const table = document.createElement('table');
                table.className = 'songs-table';
                table.innerHTML = `
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Title</th>
                            <th>Artist</th>
                            <th>Album</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                `;

                const tbody = table.querySelector('tbody');
                recentlyPlayed.slice(0, 10).forEach((songData, index) => {
                    const song = createSongFromData(songData);
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>
                            <div class="song-info">
                                <img src="${song.coverUrl || 'imgs/album-01.png'}" alt="${song.title}" loading="lazy">
                                <span>${song.title}</span>
                            </div>
                        </td>
                        <td>${song.artist}</td>
                        <td>${song.album}</td>
                        <td>${song.formattedDuration()}</td>
                    `;
                    tbody.appendChild(row);
                });

                section.innerHTML = '';
                section.appendChild(table);
            } catch (error) {
                console.error('Error loading recently played:', error);
                section.innerHTML = '<div class="error">Error loading recently played</div>';
            }
        }

        // Create test playlist function
        window.createTestPlaylist = async function() {
            try {
                const playlist = await libraryAPI.createPlaylist({
                    name: `Test Playlist ${Date.now()}`,
                    description: 'A test playlist created from the simple library page',
                    coverUrl: 'imgs/album-01.png'
                });
                console.log('Created playlist:', playlist);
                await renderPlaylists();
                alert('Playlist created successfully!');
            } catch (error) {
                console.error('Error creating playlist:', error);
                alert('Error creating playlist');
            }
        };

        // Initialize the page
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('Simple library page loaded');
            await renderPlaylists();
            await renderFavorites();
            await renderRecentlyPlayed();
        });
    </script>
</body>
</html>
