/**
 * Mini Player Component
 * Reusable audio player for the music app
 */

class MiniPlayer {
    constructor() {
        this.audio = new Audio();
        this.currentSong = null;
        this.isPlaying = false;
        this.volume = 0.7;
        this.currentTime = 0;
        this.duration = 0;
        this.queue = [];
        this.currentIndex = 0;
        
        this.init();
    }

    init() {
        this.createPlayerHTML();
        this.setupEventListeners();
        this.audio.volume = this.volume;
    }

    createPlayerHTML() {
        // Check if mini player already exists
        if (document.querySelector('.mini-player')) {
            return;
        }

        const playerHTML = `
            <div class="mini-player" id="mini-player">
                <div class="now-playing">
                    <img src="imgs/album-01.png" alt="Now Playing" class="now-playing-cover" id="player-cover">
                    <div class="now-playing-info">
                        <h3 class="now-playing-title" id="player-title">Select a song</h3>
                        <p class="now-playing-artist" id="player-artist">No artist</p>
                    </div>
                    <button type="button" class="like-btn" id="player-like" aria-label="Like this song">
                        <i class="far fa-heart"></i>
                    </button>
                </div>
                
                <div class="player-controls">
                    <button type="button" class="control-btn shuffle" id="player-shuffle" aria-label="Shuffle">
                        <i class="fas fa-random"></i>
                    </button>
                    <button type="button" class="control-btn previous" id="player-previous" aria-label="Previous song">
                        <i class="fas fa-step-backward"></i>
                    </button>
                    <button type="button" class="control-btn play-pause" id="player-play-pause" aria-label="Play or pause">
                        <i class="fas fa-play"></i>
                    </button>
                    <button type="button" class="control-btn next" id="player-next" aria-label="Next song">
                        <i class="fas fa-step-forward"></i>
                    </button>
                    <button type="button" class="control-btn repeat" id="player-repeat" aria-label="Repeat">
                        <i class="fas fa-redo"></i>
                    </button>
                </div>
                
                <div class="player-progress">
                    <span class="current-time" id="player-current-time">0:00</span>
                    <div class="progress-bar-container" id="player-progress-container">
                        <div class="progress-bar" id="player-progress-bar"></div>
                    </div>
                    <span class="total-time" id="player-total-time">0:00</span>
                </div>
                
                <div class="player-options">
                    <button type="button" class="control-btn queue" id="player-queue" aria-label="Show queue">
                        <i class="fas fa-list"></i>
                    </button>
                    <button type="button" class="control-btn volume" id="player-volume" aria-label="Volume">
                        <i class="fas fa-volume-up"></i>
                    </button>
                    <div class="volume-slider" id="volume-slider">
                        <input type="range" min="0" max="1" step="0.1" value="0.7" id="volume-control">
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', playerHTML);
    }

    setupEventListeners() {
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', () => {
            this.bindEvents();
        });
        
        // If DOM is already ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.bindEvents());
        } else {
            this.bindEvents();
        }
    }

    bindEvents() {
        // Play/Pause button
        const playPauseBtn = document.getElementById('player-play-pause');
        if (playPauseBtn) {
            playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        }

        // Previous/Next buttons
        const previousBtn = document.getElementById('player-previous');
        const nextBtn = document.getElementById('player-next');
        if (previousBtn) previousBtn.addEventListener('click', () => this.previousSong());
        if (nextBtn) nextBtn.addEventListener('click', () => this.nextSong());

        // Progress bar
        const progressContainer = document.getElementById('player-progress-container');
        if (progressContainer) {
            progressContainer.addEventListener('click', (e) => this.seekTo(e));
        }

        // Volume control
        const volumeControl = document.getElementById('volume-control');
        if (volumeControl) {
            volumeControl.addEventListener('input', (e) => this.setVolume(e.target.value));
        }

        // Audio events
        this.audio.addEventListener('timeupdate', () => this.updateProgress());
        this.audio.addEventListener('loadedmetadata', () => this.updateDuration());
        this.audio.addEventListener('ended', () => this.nextSong());
        this.audio.addEventListener('play', () => this.onPlay());
        this.audio.addEventListener('pause', () => this.onPause());
        this.audio.addEventListener('error', (e) => this.onError(e));

        // Like button
        const likeBtn = document.getElementById('player-like');
        if (likeBtn) {
            likeBtn.addEventListener('click', () => this.toggleLike());
        }
    }

    playSong(song) {
        this.currentSong = song;
        this.audio.src = song.audioUrl || song.previewUrl || '';
        
        // Update UI
        this.updatePlayerInfo();
        
        // Play the song
        if (this.audio.src) {
            this.audio.play().catch(e => {
                console.error('Failed to play audio:', e);
                this.showToast('Failed to play audio. Preview may not be available.', 'error');
            });
        } else {
            this.showToast('No audio preview available for this song', 'warning');
        }
    }

    togglePlayPause() {
        if (!this.currentSong) {
            this.showToast('No song selected', 'warning');
            return;
        }

        if (this.isPlaying) {
            this.audio.pause();
        } else {
            this.audio.play().catch(e => {
                console.error('Failed to play audio:', e);
                this.showToast('Failed to play audio', 'error');
            });
        }
    }

    previousSong() {
        if (this.queue.length === 0) return;
        
        this.currentIndex = this.currentIndex > 0 ? this.currentIndex - 1 : this.queue.length - 1;
        this.playSong(this.queue[this.currentIndex]);
    }

    nextSong() {
        if (this.queue.length === 0) return;
        
        this.currentIndex = this.currentIndex < this.queue.length - 1 ? this.currentIndex + 1 : 0;
        this.playSong(this.queue[this.currentIndex]);
    }

    seekTo(e) {
        if (!this.audio.duration) return;
        
        const progressContainer = e.currentTarget;
        const rect = progressContainer.getBoundingClientRect();
        const clickPosition = e.clientX - rect.left;
        const progressWidth = progressContainer.offsetWidth;
        const seekTime = (clickPosition / progressWidth) * this.audio.duration;
        
        this.audio.currentTime = seekTime;
    }

    setVolume(volume) {
        this.volume = parseFloat(volume);
        this.audio.volume = this.volume;
        
        // Update volume icon
        const volumeBtn = document.getElementById('player-volume');
        const icon = volumeBtn?.querySelector('i');
        if (icon) {
            icon.className = this.volume === 0 ? 'fas fa-volume-mute' : 
                           this.volume < 0.5 ? 'fas fa-volume-down' : 'fas fa-volume-up';
        }
    }

    updatePlayerInfo() {
        if (!this.currentSong) return;

        const cover = document.getElementById('player-cover');
        const title = document.getElementById('player-title');
        const artist = document.getElementById('player-artist');

        if (cover) cover.src = this.currentSong.coverUrl || 'imgs/album-01.png';
        if (title) title.textContent = this.currentSong.title || 'Unknown Title';
        if (artist) artist.textContent = this.currentSong.artist || 'Unknown Artist';
    }

    updateProgress() {
        if (!this.audio.duration) return;

        const currentTime = this.audio.currentTime;
        const duration = this.audio.duration;
        const progressPercent = (currentTime / duration) * 100;

        const progressBar = document.getElementById('player-progress-bar');
        const currentTimeEl = document.getElementById('player-current-time');

        if (progressBar) progressBar.style.width = `${progressPercent}%`;
        if (currentTimeEl) currentTimeEl.textContent = this.formatTime(currentTime);
    }

    updateDuration() {
        const totalTimeEl = document.getElementById('player-total-time');
        if (totalTimeEl) totalTimeEl.textContent = this.formatTime(this.audio.duration);
    }

    onPlay() {
        this.isPlaying = true;
        const playPauseBtn = document.getElementById('player-play-pause');
        const icon = playPauseBtn?.querySelector('i');
        if (icon) {
            icon.classList.remove('fa-play');
            icon.classList.add('fa-pause');
        }
    }

    onPause() {
        this.isPlaying = false;
        const playPauseBtn = document.getElementById('player-play-pause');
        const icon = playPauseBtn?.querySelector('i');
        if (icon) {
            icon.classList.remove('fa-pause');
            icon.classList.add('fa-play');
        }
    }

    onError(e) {
        console.error('Audio error:', e);
        this.showToast('Audio playback error', 'error');
    }

    toggleLike() {
        if (!this.currentSong) return;
        
        const likeBtn = document.getElementById('player-like');
        const icon = likeBtn?.querySelector('i');
        
        if (icon) {
            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                icon.style.color = '#ff006e';
                this.showToast('Added to favorites', 'success');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                icon.style.color = '';
                this.showToast('Removed from favorites', 'success');
            }
        }
    }

    formatTime(seconds) {
        if (!seconds || isNaN(seconds)) return '0:00';
        
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff3b30' : type === 'warning' ? '#ff9500' : type === 'success' ? '#00e0ff' : '#007aff'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10001;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => toast.style.transform = 'translateX(0)', 100);
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // Public methods for external use
    addToQueue(song) {
        this.queue.push(song);
    }

    clearQueue() {
        this.queue = [];
        this.currentIndex = 0;
    }

    setQueue(songs) {
        this.queue = songs;
        this.currentIndex = 0;
    }
}

// Create global mini player instance
window.miniPlayer = new MiniPlayer();

export default window.miniPlayer;
