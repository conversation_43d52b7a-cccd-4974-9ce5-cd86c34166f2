<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music Player - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="variables.css">
    <link rel="stylesheet" href="animations.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="navbar.css">
    <link rel="stylesheet" href="buttons.css">
    <link rel="stylesheet" href="player.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <nav role="navigation" aria-label="Main navigation">
            <div class="logo">
                <a href="index.html" aria-label="Go to home page">
                    <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
                </a>
            </div>
            <ul class="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html" aria-current="page">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile" loading="lazy">
                </button>
                <div class="dropdown">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><a href="#" class="logout">Logout</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main id="main-content" class="container">
        <section class="player-container" aria-labelledby="player-heading">
            <h2 id="player-heading">Now Playing</h2>

            <div class="player-content">
                <audio id="audio" preload="metadata"></audio>
                <img class="album-art" src="imgs/album-02.png" alt="Album Art" aria-describedby="song-title artist-name">

                <div class="track-info">
                    <h3 id="song-title">Song Title</h3>
                    <p id="artist-name">Artist Name</p>
                </div>

                <div class="progress-container" aria-label="Track progress">
                    <div class="progress-bar">
                        <div class="progress" id="progress"></div>
                    </div>
                    <div class="time">
                        <span id="current-time">0:00</span>
                        <span id="total-time">0:00</span>
                    </div>
                </div>

                <div class="player-controls">
                    <div class="control-buttons">
                        <button type="button" id="prev" class="control-button" aria-label="Previous track">
                            <i class="fas fa-backward"></i>
                        </button>

                        <button type="button" id="play-pause" class="control-button" aria-label="Play or pause track">
                            <i class="fas fa-play play-icon"></i>
                            <i class="fas fa-pause pause-icon hidden"></i>
                        </button>

                        <button type="button" id="next" class="control-button" aria-label="Next track">
                            <i class="fas fa-forward"></i>
                        </button>
                    </div>

                    <div class="additional-controls">
                        <div class="volume-control">
                            <i class="fas fa-volume-up"></i>
                            <input type="range"
                                   id="volume"
                                   min="0"
                                   max="1"
                                   step="0.01"
                                   value="1"
                                   aria-label="Volume control">
                        </div>
                        <button type="button" id="hd-btn" class="hd-button" aria-label="Toggle HD quality">
                            HD
                        </button>
                    </div>
                </div>

                <div class="playlist-section">
                    <div class="playlist-header">
                        <h3>Current Playlist</h3>
                        <span id="playlist-name">My Favorites</span>
                    </div>
                    <div class="playlist-container">
                        <ul id="playlist" class="playlist">
                            <!-- Playlist items will be dynamically inserted here -->
                        </ul>
                    </div>
                </div>
                <div class="lyrics-panel" id="lyrics-panel">
                    <div class="lyrics-header">
                        <h3>Lyrics</h3>
                        <button type="button" class="close-lyrics" id="close-lyrics" aria-label="Close lyrics panel">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="lyrics-content" id="lyrics-content">
                        <!-- Lyrics will be displayed here -->
                    </div>
                </div>
            </div>
        </section>
    </main>
    <script src="player.js"></script>
    <script>
        // Inline script for dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const profileButton = document.querySelector('.profile-button');
            const dropdown = document.querySelector('.dropdown');
            const userProfile = document.querySelector('.user-profile');

            if (profileButton && dropdown && userProfile) {
                // Check if device is mobile (no hover)
                const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

                if (isMobile) {
                    // For mobile devices, use click instead of hover
                    profileButton.addEventListener('click', function(e) {
                        e.stopPropagation();
                        dropdown.classList.toggle('show');
                        const isExpanded = dropdown.classList.contains('show');
                        profileButton.setAttribute('aria-expanded', isExpanded);
                    });

                    document.addEventListener('click', function(e) {
                        if (!dropdown.contains(e.target) && !profileButton.contains(e.target)) {
                            dropdown.classList.remove('show');
                            profileButton.setAttribute('aria-expanded', 'false');
                        }
                    });
                } else {
                    // For desktop, enhance with keyboard accessibility
                    profileButton.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            dropdown.classList.toggle('show');
                            const isExpanded = dropdown.classList.contains('show');
                            profileButton.setAttribute('aria-expanded', isExpanded);
                        }
                    });

                    // Close with Escape key
                    document.addEventListener('keydown', function(e) {
                        if (e.key === 'Escape' && dropdown.classList.contains('show')) {
                            dropdown.classList.remove('show');
                            profileButton.setAttribute('aria-expanded', 'false');
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>
