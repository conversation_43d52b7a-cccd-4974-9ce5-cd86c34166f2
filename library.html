<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Music Library - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/library.css">
    <link rel="stylesheet" href="css/mini-player.css">
    <link rel="stylesheet" href="https://unpkg.com/flickity@2/dist/flickity.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <a href="index.html" aria-label="Go to home page">
                    <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
                </a>
            </div>
            <ul class="menu" aria-label="Main Navigation">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html" aria-current="page">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile" loading="lazy">
                </button>
                <div class="dropdown">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html">Notifications</a></li>
                        <li><a href="#" class="logout">Logout</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="container">
        <div class="loader" id="loader"></div>

        <!-- Library Header -->
        <section class="library-header">
            <div class="header-content">
                <h1>Your Music Library</h1>
                <p>Manage your songs, playlists, and favorites</p>
                <div class="library-stats">
                    <div class="stat">
                        <span class="stat-number" id="total-songs">0</span>
                        <span class="stat-label">Songs</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number" id="total-playlists">0</span>
                        <span class="stat-label">Playlists</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number" id="total-favorites">0</span>
                        <span class="stat-label">Favorites</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions">
            <button type="button" class="action-btn create-playlist-btn" id="create-playlist-btn">
                <i class="fas fa-plus"></i>
                Create Playlist
            </button>
            <button type="button" class="action-btn shuffle-all-btn" id="shuffle-all-btn">
                <i class="fas fa-random"></i>
                Shuffle All
            </button>
            <button type="button" class="action-btn view-toggle-btn" id="view-toggle-btn">
                <i class="fas fa-th-large"></i>
                Grid View
            </button>
        </section>

        <!-- Recently Played -->
        <section class="section recently-played">
            <div class="section-header">
                <h2>Recently Played</h2>
                <button type="button" class="view-all-btn">View All</button>
            </div>
            <div class="carousel" id="recently-played-carousel">
                <!-- Recently played items will be loaded here -->
            </div>
        </section>

        <!-- Your Playlists -->
        <section class="section playlists">
            <div class="section-header">
                <h2>Your Playlists</h2>
                <button type="button" class="view-all-btn">View All</button>
            </div>
            <div class="playlists-grid" id="playlists-grid">
                <!-- Playlists will be loaded here -->
            </div>
        </section>

        <!-- Favorite Songs -->
        <section class="section favorites">
            <div class="section-header">
                <h2>Favorite Songs</h2>
                <button type="button" class="view-all-btn">View All</button>
            </div>
            <div class="songs-grid" id="favorites-grid">
                <!-- Favorite songs will be loaded here -->
            </div>
        </section>

        <!-- All Songs -->
        <section class="section all-songs">
            <div class="section-header">
                <h2>All Songs</h2>
                <div class="section-controls">
                    <button type="button" class="sort-btn" id="sort-btn">
                        <i class="fas fa-sort"></i>
                        Sort by Date Added
                    </button>
                    <button type="button" class="filter-btn" id="filter-btn">
                        <i class="fas fa-filter"></i>
                        Filter
                    </button>
                </div>
            </div>
            <div class="songs-container" id="all-songs-container">
                <!-- All songs will be loaded here -->
            </div>
        </section>
    </main>

    <!-- Create Playlist Modal -->
    <div class="modal" id="create-playlist-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Playlist</h3>
                <button type="button" class="close-btn" id="close-modal-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="create-playlist-form">
                <div class="form-group">
                    <label for="playlist-name">Playlist Name</label>
                    <input type="text" id="playlist-name" name="name" required placeholder="Enter playlist name">
                </div>
                <div class="form-group">
                    <label for="playlist-description">Description (Optional)</label>
                    <textarea id="playlist-description" name="description" placeholder="Enter playlist description"></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="cancel-btn" id="cancel-btn">Cancel</button>
                    <button type="submit" class="create-btn">Create Playlist</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <footer>
        <p>&copy; 2024 Banshee Music App. All rights reserved.</p>
    </footer>

    <script src="https://unpkg.com/flickity@2/dist/flickity.pkgd.min.js"></script>
    <script src="js/navbar.js"></script>
    <script type="module">
        import libraryAPI from './js/library/api-backend.js';
        import { Song, Playlist } from './js/library/models.js';
        import miniPlayer from './js/mini-player.js';

        // Global variables
        let currentLibrary = null;
        let currentView = 'grid'; // 'grid' or 'list'
        let currentSort = 'dateAdded'; // 'dateAdded', 'name', 'artist'

        document.addEventListener('DOMContentLoaded', async () => {
            console.log('Library page loaded');
            
            // Initialize components
            await initializeLibrary();
            setupEventListeners();
            
            // Add animation class to navbar for current page
            const currentPageLink = document.querySelector('.menu a[aria-current="page"]');
            if (currentPageLink) {
                currentPageLink.parentElement.classList.add('active');
            }
        });

        /**
         * Initialize the library and load all data
         */
        async function initializeLibrary() {
            showLoader();
            
            try {
                // Initialize the library API
                await libraryAPI.initializeLibrary();
                
                // Load all library data
                await Promise.all([
                    loadRecentlyPlayed(),
                    loadPlaylists(),
                    loadFavorites(),
                    loadAllSongs()
                ]);
                
                // Update stats
                updateLibraryStats();
                
            } catch (error) {
                console.error('Error initializing library:', error);
                showToast('Failed to load library', 'error');
            } finally {
                hideLoader();
            }
        }

        /**
         * Load recently played songs
         */
        async function loadRecentlyPlayed() {
            try {
                const recentSongs = await libraryAPI.getRecentlyPlayed();
                const carousel = document.getElementById('recently-played-carousel');
                
                if (recentSongs.length === 0) {
                    carousel.innerHTML = '<p class="empty-state">No recently played songs</p>';
                    return;
                }
                
                carousel.innerHTML = recentSongs.map(song => createSongCard(song, 'recent')).join('');
                
                // Initialize Flickity carousel
                new Flickity(carousel, {
                    cellAlign: 'left',
                    contain: true,
                    wrapAround: false,
                    pageDots: false,
                    prevNextButtons: true,
                    groupCells: true
                });
                
            } catch (error) {
                console.error('Error loading recently played:', error);
            }
        }

        /**
         * Load user playlists
         */
        async function loadPlaylists() {
            try {
                const playlists = await libraryAPI.getPlaylists();
                const grid = document.getElementById('playlists-grid');
                
                if (playlists.length === 0) {
                    grid.innerHTML = '<p class="empty-state">No playlists yet. Create your first playlist!</p>';
                    return;
                }
                
                grid.innerHTML = playlists.map(playlist => createPlaylistCard(playlist)).join('');
                
            } catch (error) {
                console.error('Error loading playlists:', error);
            }
        }

        /**
         * Load favorite songs
         */
        async function loadFavorites() {
            try {
                const favorites = await libraryAPI.getFavorites();
                const grid = document.getElementById('favorites-grid');
                
                if (favorites.length === 0) {
                    grid.innerHTML = '<p class="empty-state">No favorite songs yet</p>';
                    return;
                }
                
                grid.innerHTML = favorites.slice(0, 6).map(song => createSongCard(song, 'favorite')).join('');
                
            } catch (error) {
                console.error('Error loading favorites:', error);
            }
        }

        /**
         * Load all songs in library
         */
        async function loadAllSongs() {
            try {
                const songs = await libraryAPI.getAllSongs();
                const container = document.getElementById('all-songs-container');
                
                if (songs.length === 0) {
                    container.innerHTML = '<p class="empty-state">Your library is empty. Add some songs from the Explore page!</p>';
                    return;
                }
                
                // Sort songs based on current sort option
                const sortedSongs = sortSongs(songs, currentSort);
                
                if (currentView === 'grid') {
                    container.className = 'songs-grid';
                    container.innerHTML = sortedSongs.map(song => createSongCard(song, 'library')).join('');
                } else {
                    container.className = 'songs-list';
                    container.innerHTML = sortedSongs.map(song => createSongListItem(song)).join('');
                }
                
            } catch (error) {
                console.error('Error loading all songs:', error);
            }
        }

        /**
         * Create a song card element
         */
        function createSongCard(song, type = 'default') {
            const isFavorite = song.isFavorite || false;
            const playedAt = song.playedAt ? new Date(song.playedAt).toLocaleDateString() : '';

            return `
                <div class="song-card" data-song-id="${song.id}">
                    <div class="song-cover">
                        <img src="${song.coverUrl || 'imgs/album-01.png'}" alt="${song.title}" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-btn" onclick="playSong('${song.id}')">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="song-info">
                        <h3 class="song-title">${song.title}</h3>
                        <p class="song-artist">${song.artist}</p>
                        ${type === 'recent' && playedAt ? `<p class="played-at">Played ${playedAt}</p>` : ''}
                    </div>
                    <div class="song-actions">
                        <button type="button" class="action-btn favorite-btn ${isFavorite ? 'active' : ''}"
                                onclick="toggleFavorite('${song.id}')">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button type="button" class="action-btn more-btn" onclick="showSongMenu('${song.id}')">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>
            `;
        }

        /**
         * Create a playlist card element
         */
        function createPlaylistCard(playlist) {
            const songCount = playlist.songs ? playlist.songs.length : 0;
            const coverImage = playlist.coverUrl || (playlist.songs && playlist.songs[0] ? playlist.songs[0].coverUrl : 'imgs/album-01.png');

            return `
                <div class="playlist-card" data-playlist-id="${playlist.id}" onclick="openPlaylist('${playlist.id}')">
                    <div class="playlist-cover">
                        <img src="${coverImage}" alt="${playlist.name}" loading="lazy">
                        <div class="play-overlay">
                            <button type="button" class="play-btn" onclick="playPlaylist('${playlist.id}')">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                    </div>
                    <div class="playlist-info">
                        <h3 class="playlist-name">${playlist.name}</h3>
                        <p class="playlist-details">${songCount} songs</p>
                        ${playlist.description ? `<p class="playlist-description">${playlist.description}</p>` : ''}
                    </div>
                </div>
            `;
        }

        /**
         * Create a song list item (for list view)
         */
        function createSongListItem(song) {
            const isFavorite = song.isFavorite || false;

            return `
                <div class="song-list-item" data-song-id="${song.id}">
                    <div class="song-number">${song.trackNumber || ''}</div>
                    <div class="song-cover-small">
                        <img src="${song.coverUrl || 'imgs/album-01.png'}" alt="${song.title}" loading="lazy">
                    </div>
                    <div class="song-details">
                        <div class="song-title">${song.title}</div>
                        <div class="song-artist">${song.artist}</div>
                    </div>
                    <div class="song-album">${song.album || ''}</div>
                    <div class="song-duration">${song.duration || ''}</div>
                    <div class="song-actions">
                        <button type="button" class="action-btn favorite-btn ${isFavorite ? 'active' : ''}"
                                onclick="toggleFavorite('${song.id}')">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button type="button" class="action-btn play-btn" onclick="playSong('${song.id}')">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="action-btn more-btn" onclick="showSongMenu('${song.id}')">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>
            `;
        }

        /**
         * Sort songs based on criteria
         */
        function sortSongs(songs, sortBy) {
            const sortedSongs = [...songs];

            switch (sortBy) {
                case 'name':
                    return sortedSongs.sort((a, b) => a.title.localeCompare(b.title));
                case 'artist':
                    return sortedSongs.sort((a, b) => a.artist.localeCompare(b.artist));
                case 'dateAdded':
                default:
                    return sortedSongs.sort((a, b) => new Date(b.addedAt || 0) - new Date(a.addedAt || 0));
            }
        }

        /**
         * Update library statistics
         */
        async function updateLibraryStats() {
            try {
                const [songs, playlists, favorites] = await Promise.all([
                    libraryAPI.getAllSongs(),
                    libraryAPI.getPlaylists(),
                    libraryAPI.getFavorites()
                ]);

                document.getElementById('total-songs').textContent = songs.length;
                document.getElementById('total-playlists').textContent = playlists.length;
                document.getElementById('total-favorites').textContent = favorites.length;

            } catch (error) {
                console.error('Error updating stats:', error);
            }
        }

        /**
         * Set up event listeners
         */
        function setupEventListeners() {
            // Create playlist button
            document.getElementById('create-playlist-btn').addEventListener('click', showCreatePlaylistModal);

            // Shuffle all button
            document.getElementById('shuffle-all-btn').addEventListener('click', shuffleAllSongs);

            // View toggle button
            document.getElementById('view-toggle-btn').addEventListener('click', toggleView);

            // Sort button
            document.getElementById('sort-btn').addEventListener('click', showSortMenu);

            // Modal close buttons
            document.getElementById('close-modal-btn').addEventListener('click', hideCreatePlaylistModal);
            document.getElementById('cancel-btn').addEventListener('click', hideCreatePlaylistModal);

            // Create playlist form
            document.getElementById('create-playlist-form').addEventListener('submit', handleCreatePlaylist);

            // Close modal when clicking outside
            document.getElementById('create-playlist-modal').addEventListener('click', (e) => {
                if (e.target.id === 'create-playlist-modal') {
                    hideCreatePlaylistModal();
                }
            });
        }

        /**
         * Show create playlist modal
         */
        function showCreatePlaylistModal() {
            document.getElementById('create-playlist-modal').style.display = 'flex';
            document.getElementById('playlist-name').focus();
        }

        /**
         * Hide create playlist modal
         */
        function hideCreatePlaylistModal() {
            document.getElementById('create-playlist-modal').style.display = 'none';
            document.getElementById('create-playlist-form').reset();
        }

        /**
         * Handle create playlist form submission
         */
        async function handleCreatePlaylist(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const name = formData.get('name').trim();
            const description = formData.get('description').trim();

            if (!name) {
                showToast('Please enter a playlist name', 'error');
                return;
            }

            try {
                const playlist = new Playlist({
                    name: name,
                    description: description,
                    songs: []
                });

                await libraryAPI.createPlaylist(playlist);

                hideCreatePlaylistModal();
                showToast('Playlist created successfully!', 'success');

                // Reload playlists
                await loadPlaylists();
                await updateLibraryStats();

            } catch (error) {
                console.error('Error creating playlist:', error);
                showToast('Failed to create playlist', 'error');
            }
        }

        /**
         * Toggle view between grid and list
         */
        function toggleView() {
            currentView = currentView === 'grid' ? 'list' : 'grid';
            const toggleBtn = document.getElementById('view-toggle-btn');

            if (currentView === 'grid') {
                toggleBtn.innerHTML = '<i class="fas fa-th-large"></i> Grid View';
            } else {
                toggleBtn.innerHTML = '<i class="fas fa-list"></i> List View';
            }

            // Reload all songs with new view
            loadAllSongs();
        }

        /**
         * Shuffle all songs in library
         */
        async function shuffleAllSongs() {
            try {
                const songs = await libraryAPI.getAllSongs();
                if (songs.length === 0) {
                    showToast('No songs in library to shuffle', 'warning');
                    return;
                }

                // Shuffle the songs array
                const shuffledSongs = [...songs].sort(() => Math.random() - 0.5);

                // Play the first song and queue the rest
                if (shuffledSongs.length > 0) {
                    await miniPlayer.playSong(shuffledSongs[0]);
                    showToast(`Shuffling ${songs.length} songs`, 'success');
                }

            } catch (error) {
                console.error('Error shuffling songs:', error);
                showToast('Failed to shuffle songs', 'error');
            }
        }

        /**
         * Play a specific song
         */
        window.playSong = async function(songId) {
            try {
                const songs = await libraryAPI.getAllSongs();
                const song = songs.find(s => s.id === songId);

                if (song) {
                    await miniPlayer.playSong(song);
                    // Add to recently played
                    await libraryAPI.addToRecentlyPlayed(song);
                }
            } catch (error) {
                console.error('Error playing song:', error);
                showToast('Failed to play song', 'error');
            }
        };

        /**
         * Toggle favorite status of a song
         */
        window.toggleFavorite = async function(songId) {
            try {
                const songs = await libraryAPI.getAllSongs();
                const song = songs.find(s => s.id === songId);

                if (song) {
                    if (song.isFavorite) {
                        await libraryAPI.removeFromFavorites(songId);
                        showToast('Removed from favorites', 'success');
                    } else {
                        await libraryAPI.addToFavorites(song);
                        showToast('Added to favorites', 'success');
                    }

                    // Reload relevant sections
                    await Promise.all([
                        loadFavorites(),
                        loadAllSongs(),
                        updateLibraryStats()
                    ]);
                }
            } catch (error) {
                console.error('Error toggling favorite:', error);
                showToast('Failed to update favorite', 'error');
            }
        };

        /**
         * Show song context menu
         */
        window.showSongMenu = function(songId) {
            // TODO: Implement context menu for song actions
            console.log('Show menu for song:', songId);
        };

        /**
         * Open a playlist
         */
        window.openPlaylist = function(playlistId) {
            // TODO: Navigate to playlist detail page
            console.log('Open playlist:', playlistId);
        };

        /**
         * Play a playlist
         */
        window.playPlaylist = async function(playlistId) {
            try {
                const playlists = await libraryAPI.getPlaylists();
                const playlist = playlists.find(p => p.id === playlistId);

                if (playlist && playlist.songs && playlist.songs.length > 0) {
                    await miniPlayer.playSong(playlist.songs[0]);
                    showToast(`Playing ${playlist.name}`, 'success');
                } else {
                    showToast('Playlist is empty', 'warning');
                }
            } catch (error) {
                console.error('Error playing playlist:', error);
                showToast('Failed to play playlist', 'error');
            }
        };

        /**
         * Show sort menu
         */
        function showSortMenu() {
            // TODO: Implement sort menu
            console.log('Show sort menu');
        }

        /**
         * Show loader
         */
        function showLoader() {
            document.getElementById('loader').style.display = 'block';
        }

        /**
         * Hide loader
         */
        function hideLoader() {
            document.getElementById('loader').style.display = 'none';
        }

        /**
         * Show toast notification
         */
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;

            const container = document.getElementById('toast-container');
            container.appendChild(toast);

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Hide and remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => container.removeChild(toast), 300);
            }, 3000);
        }
    </script>
</body>
</html>
