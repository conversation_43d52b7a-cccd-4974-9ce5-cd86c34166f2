/* Navbar Styles */
header {
    background: var(--background-color);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    flex-shrink: 0;
}

.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.menu a:hover {
    color: var(--accent-color);
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

.user-profile {
    position: relative;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(var(--neon-blue-rgb), 0.3);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: calc(100% + 5px);
    right: 0;
    background: rgba(20, 25, 35, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 0.5rem;
    min-width: 220px;
    visibility: hidden;
    margin-top: 0;
    transform: translateY(-10px);
    opacity: 0;
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s linear 0.2s;
    z-index: 1000;
    border: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    pointer-events: none; /* Prevent clicks when hidden */
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px;
    background: rgba(20, 25, 35, 0.95);
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-color);
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
}

.dropdown a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(3px);
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout {
    color: #ff5a5a;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    header {
        padding: 1rem;
    }

    nav {
        flex-direction: column;
        gap: 1rem;
    }

    .logo img {
        width: 60px;
        height: 60px;
    }

    .menu {
        gap: 1rem;
    }

    .menu a {
        font-size: 1rem;
        padding: 0.3rem 0.8rem;
    }
}