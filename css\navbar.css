/* Navbar Styles */
header {
    background: var(--background-color);
    padding: 1rem 2rem;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    flex-shrink: 0;
}

.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.menu a:hover {
    color: var(--accent-color);
}

.menu a[aria-current="page"] {
    background: var(--button-gradient);
    color: white;
}

.user-profile {
    position: relative;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--background-color);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    padding: 0.5rem;
    min-width: 200px;
    display: none;
    margin-top: 0.5rem;
}

.dropdown.show {
    display: block;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown a {
    color: var(--text-color);
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: block;
    transition: background-color 0.3s ease;
    border-radius: 5px;
}

.dropdown a:hover {
    background-color: var(--hover-color);
}

.dropdown .logout {
    color: var(--error-color);
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    header {
        padding: 1rem;
    }

    nav {
        flex-direction: column;
        gap: 1rem;
    }

    .logo img {
        width: 60px;
        height: 60px;
    }

    .menu {
        gap: 1rem;
    }

    .menu a {
        font-size: 1rem;
        padding: 0.3rem 0.8rem;
    }
}