/* Signup Page Styles */

body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background-color: #1c1c1e;
    color: #ffffff;
    font-family: "Plus Jakarta Sans", sans-serif;
}

.welcome-header {
    color: hsla(208, 12%, 30%, 0.95);
    font-size: 2.5em;
    margin-top: 2rem;
    margin-bottom: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #03A9F4, #FF69B4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
    text-align: center;
}

.signup-container {
    background: linear-gradient(315deg, rgba(13, 17, 23, 0.95), rgba(25, 32, 44, 0.95));
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
    width: 400px;
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.signup-container:hover {
    box-shadow: 0 6px 25px rgba(43, 149, 221, 0.2);
    border-color: rgba(43, 149, 221, 0.3);
}

.logo {
    text-align: center;
    margin-bottom: 20px;
}

.logo img {
    width: 100px;
    height: 100px;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

h1 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8em;
    font-weight: 600;
}

.input-group {
    margin-bottom: 20px;
    position: relative;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 0.95em;
}

.input-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    background-color: rgba(0, 0, 0, 0.2);
    color: #ffffff;
    font-size: 1em;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.input-group input:focus {
    border-color: #2b95dd;
    background-color: rgba(0, 0, 0, 0.25);
    outline: none;
    box-shadow: 0 0 0 2px rgba(43, 149, 221, 0.2);
}

.input-group .password-toggle {
    position: absolute;
    right: 12px;
    top: 38px;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.6);
    transition: color 0.3s ease;
}

.input-group .password-toggle:hover {
    color: #2b95dd;
}

.password-strength {
    margin-top: 5px;
    font-size: 0.85em;
    display: flex;
    gap: 5px;
}

.strength-bar {
    height: 4px;
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.strength-bar.active {
    background: #2b95dd;
}

.strength-bar.weak.active { background: #ff3366; }
.strength-bar.medium.active { background: #ffd700; }
.strength-bar.strong.active { background: #4CAF50; }

.signup-button {
    width: 100%;
    padding: 14px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(45deg, #03A9F4, #FF69B4);
    color: white;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.signup-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
}

.signup-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(43, 149, 221, 0.2);
}

.login-link {
    text-align: center;
    margin-top: 20px;
    font-size: 0.95em;
    color: rgba(255, 255, 255, 0.8);
}

.login-link a {
    color: #2b95dd;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.login-link a:hover {
    text-decoration: underline;
    color: rgb(70, 195, 255);
}

.social-signup {
    margin-top: 25px;
    text-align: center;
}

.social-signup p {
    margin-bottom: 15px;
    font-size: 0.95em;
    color: rgba(255, 255, 255, 0.8);
}

.social-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.social-button {
    padding: 10px 20px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.social-button:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.social-button i {
    font-size: 1.2em;
}

.terms-group {
    margin: 20px 0;
    text-align: center;
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.8);
}

.terms-group a {
    color: #2b95dd;
    text-decoration: none;
}

.terms-group a:hover {
    text-decoration: underline;
}

.validation-message {
    font-size: 0.85em;
    margin-top: 5px;
    display: none;
}

.validation-message.error {
    color: #ff3366;
    display: block;
}

.validation-message.success {
    color: #4CAF50;
    display: block;
}
