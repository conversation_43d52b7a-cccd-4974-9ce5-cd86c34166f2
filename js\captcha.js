/**
 * Simple Custom CAPTCHA
 * Generates a basic math problem for the user to solve
 */

document.addEventListener('DOMContentLoaded', () => {
    // Initialize CAPTCHA
    generateCaptcha();

    // Add event listener for refresh button
    const refreshButton = document.getElementById('refreshCaptcha');
    if (refreshButton) {
        refreshButton.addEventListener('click', generateCaptcha);
    }
});

// Store the correct answer
let correctAnswer = null;

/**
 * Generate a new CAPTCHA math problem
 */
function generateCaptcha() {
    const captchaQuestion = document.getElementById('captchaQuestion');
    const captchaAnswer = document.getElementById('captchaAnswer');

    if (!captchaQuestion || !captchaAnswer) return;

    // Clear previous answer
    captchaAnswer.value = '';

    // Generate two random numbers between 1 and 10
    const num1 = Math.floor(Math.random() * 10) + 1;
    const num2 = Math.floor(Math.random() * 10) + 1;

    // Choose a random operation (addition, subtraction, or multiplication)
    const operations = ['+', '-', '×'];
    const operation = operations[Math.floor(Math.random() * operations.length)];

    // Calculate the correct answer
    switch (operation) {
        case '+':
            correctAnswer = num1 + num2;
            break;
        case '-':
            // Ensure the result is positive
            if (num1 >= num2) {
                correctAnswer = num1 - num2;
            } else {
                correctAnswer = num2 - num1;
                // Swap numbers so the larger one comes first
                const temp = num1;
                num1 = num2;
                num2 = temp;
            }
            break;
        case '×':
            correctAnswer = num1 * num2;
            break;
    }

    // Display the CAPTCHA question
    captchaQuestion.textContent = `What is ${num1} ${operation} ${num2}?`;

    // Clear any previous error
    const captchaError = document.getElementById('captchaError');
    if (captchaError) {
        captchaError.textContent = '';
        captchaError.style.display = 'none';
    }
}

/**
 * Validate the CAPTCHA answer
 * @returns {boolean} True if the answer is correct, false otherwise
 */
function validateCaptcha() {
    const captchaAnswer = document.getElementById('captchaAnswer');
    const captchaError = document.getElementById('captchaError');

    if (!captchaAnswer || !captchaError) return false;

    const userAnswer = parseInt(captchaAnswer.value, 10);

    if (isNaN(userAnswer)) {
        captchaError.textContent = 'Please enter a valid number';
        captchaError.style.display = 'block';
        return false;
    }

    if (userAnswer !== correctAnswer) {
        captchaError.textContent = 'Incorrect answer, please try again';
        captchaError.style.display = 'block';
        generateCaptcha(); // Generate a new CAPTCHA
        return false;
    }

    return true;
}

// Export the functions for use in login.js
window.validateCaptcha = validateCaptcha;
window.generateCaptcha = generateCaptcha;
