/* Home Page Specific Styles */

/* Star Animation Styles */
.stars {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.star {
    position: absolute;
    width: 3px;
    height: 3px;
    background: #fff;
    border-radius: 50%;
    animation: twinkle 3s ease-in-out infinite;
    opacity: 0;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.9), 0 0 20px rgba(255, 255, 255, 0.5);
    filter: blur(0.5px);
}

/* Add color variations to stars */
.star:nth-child(3n+1) {
    background: rgba(0, 224, 255, 0.9); /* Blue stars */
    box-shadow: 0 0 10px rgba(0, 224, 255, 0.9), 0 0 20px rgba(0, 224, 255, 0.5);
}

.star:nth-child(3n+2) {
    background: rgba(255, 255, 255, 0.9); /* White stars */
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.9), 0 0 20px rgba(255, 255, 255, 0.5);
}

.star:nth-child(3n+3) {
    background: rgba(255, 0, 110, 0.9); /* Pink stars */
    box-shadow: 0 0 10px rgba(255, 0, 110, 0.9), 0 0 20px rgba(255, 0, 110, 0.5);
}

/* Stagger star animations */
.star:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
.star:nth-child(2) { top: 20%; left: 25%; animation-delay: 0.3s; }
.star:nth-child(3) { top: 15%; left: 50%; animation-delay: 0.6s; }
.star:nth-child(4) { top: 30%; left: 70%; animation-delay: 0.9s; }
.star:nth-child(5) { top: 40%; left: 85%; animation-delay: 1.2s; }
.star:nth-child(6) { top: 50%; left: 15%; animation-delay: 1.5s; }
.star:nth-child(7) { top: 60%; left: 35%; animation-delay: 1.8s; }
.star:nth-child(8) { top: 75%; left: 60%; animation-delay: 2.1s; }
.star:nth-child(9) { top: 80%; left: 80%; animation-delay: 2.4s; }
.star:nth-child(10) { top: 85%; left: 5%; animation-delay: 2.7s; }
.star:nth-child(11) { top: 5%; left: 90%; animation-delay: 3.0s; }
.star:nth-child(12) { top: 25%; left: 40%; animation-delay: 3.3s; }
.star:nth-child(13) { top: 35%; left: 65%; animation-delay: 3.6s; }
.star:nth-child(14) { top: 45%; left: 95%; animation-delay: 3.9s; }
.star:nth-child(15) { top: 55%; left: 75%; animation-delay: 4.2s; }
.star:nth-child(16) { top: 65%; left: 45%; animation-delay: 4.5s; }
.star:nth-child(17) { top: 70%; left: 20%; animation-delay: 4.8s; }
.star:nth-child(18) { top: 90%; left: 30%; animation-delay: 5.1s; }
.star:nth-child(19) { top: 95%; left: 55%; animation-delay: 5.4s; }
.star:nth-child(20) { top: 15%; left: 75%; animation-delay: 5.7s; }

/* Hero Section */
.hero {
    position: relative;
    min-height: 50vh; /* Reduced from 70vh */
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    padding: 3rem 1rem; /* Reduced from 5rem */
    overflow: hidden;
    background: linear-gradient(to bottom, rgba(13, 17, 23, 0.8), rgba(13, 17, 23, 0.95));
    border-radius: var(--border-radius-lg);
    border: 2px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 30px rgba(0, 224, 255, 0.15), 0 0 60px rgba(255, 0, 110, 0.1);
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    width: 100%;
    max-width: 800px;
    padding: 2rem;
    margin: 0 auto;
    background: rgba(13, 17, 23, 0.8);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: inset 0 0 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 224, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.hero-content h1 {
    font-size: clamp(2.2rem, 5vw, 3.5rem);
    margin-bottom: 1rem; /* Reduced from 1.5rem */
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.hero-content p {
    font-size: clamp(1rem, 2vw, 1.3rem);
    margin-bottom: 1.5rem; /* Reduced from 2.5rem */
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Quick Access Section */
.quick-access {
    margin: 2rem auto; /* Reduced from 3rem */
    text-align: center;
    max-width: 1200px;
    padding: 1.5rem; /* Reduced from 2rem */
    display: flex;
    flex-direction: column;
    align-items: center;
}

.quick-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    padding: 1rem 0;
    flex-wrap: wrap;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-color);
    transition: transform 0.3s;
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 120px;
    box-sizing: border-box;
}

.quick-link:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
}

.quick-link img {
    width: 48px;
    height: 48px;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.quick-link:hover img {
    transform: scale(1.1);
}

.quick-link span {
    font-size: 1rem;
    font-weight: 500;
}

/* Section Styles */
.section {
    margin: 2.5rem auto; /* Reduced from 4rem */
    max-width: 1200px;
    padding: 0 1rem;
}

.section-header {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem; /* Reduced from 2rem */
    padding: 0 1rem;
    text-align: center;
}

.section-header h2 {
    font-size: 2rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    margin: 0 0 0.5rem 0;
    text-align: center;
    width: 100%;
}

/* View All links removed */

/* Carousel Styles */
.carousel {
    margin-bottom: 3rem;
    padding: 0.5rem;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
}

.carousel-cell {
    width: 320px;
    margin-right: 30px;
    border-radius: 12px;
    padding: 8px;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
}

.carousel-cell .card {
    width: 100%;
    height: 100%;
    margin: 0;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
}

/* Flickity Customization */
.flickity-button {
    background: var(--button-gradient-start);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.flickity-button:hover {
    background: var(--button-gradient-end);
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

.flickity-prev-next-button {
    width: 40px;
    height: 40px;
}

.flickity-page-dots {
    bottom: -30px;
}

.flickity-page-dots .dot {
    background: var(--button-gradient-start);
    opacity: 0.5;
    width: 10px;
    height: 10px;
    margin: 0 6px;
}

.flickity-page-dots .dot.is-selected {
    background: var(--button-gradient-end);
    opacity: 1;
    transform: scale(1.2);
}

/* Responsive Styles */
@media screen and (max-width: 1024px) {
    .section {
        padding: 0 1rem;
    }

    .carousel-cell {
        width: 300px;
        margin-right: 30px;
    }
}

@media screen and (max-width: 768px) {
    .hero {
        padding: 2rem 1rem; /* Reduced from 3rem */
        min-height: 40vh; /* Reduced from 60vh */
        margin-top: 15px;
    }

    .hero-content {
        padding: 2rem;
        width: 90%;
    }

    .quick-access {
        margin: 2rem auto;
        padding: 1.5rem;
    }

    .quick-links {
        gap: 1.5rem;
    }

    .quick-link {
        width: 100px;
        padding: 1.25rem;
    }

    .quick-link img {
        width: 40px;
        height: 40px;
        margin-bottom: 0.75rem;
    }

    .section {
        margin: 2rem auto; /* Reduced from 3rem */
    }

    .section-header h2 {
        font-size: 1.6rem;
    }

    .carousel-cell {
        width: 250px;
        margin-right: 20px;
        padding: 6px;
    }

    .carousel-cell .card .img-container {
        height: 150px;
    }
}

@media screen and (max-width: 480px) {
    .hero {
        padding: 1.5rem 1rem; /* Reduced from 2rem */
        min-height: 35vh; /* Reduced from 50vh */
        margin-top: 10px;
    }

    .hero-content {
        padding: 1rem; /* Reduced from 1.5rem */
        width: 95%;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .quick-access {
        margin: 1.5rem auto;
        padding: 1rem;
    }

    .quick-links {
        gap: 1rem;
        flex-wrap: wrap;
    }

    .quick-link {
        width: 90px;
        padding: 1rem;
        flex: 0 1 calc(33.333% - 1rem);
    }

    .quick-link img {
        width: 35px;
        height: 35px;
        margin-bottom: 0.5rem;
    }

    .quick-link span {
        font-size: 0.8rem;
    }

    .section {
        margin: 1.5rem auto; /* Reduced from 2rem */
    }

    .section-header {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .section-header h2 {
        font-size: 1.4rem;
    }

    .carousel-cell {
        width: 200px;
        margin-right: 15px;
        padding: 5px;
    }

    .carousel-cell .card .img-container {
        height: 130px;
    }

    .carousel-cell .card-content {
        padding: 8px;
    }
}
