/*
 * Consolidated Home Page Styles
 * This file combines styles from card.css and relevant parts of style.css specific to the home page
 */

/* ===== Variables ===== */
:root {
    /* Colors */
    --background-color: #0D1117;
    --text-color: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-primary: #ffffff;
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    --cyber-lime-rgb: 167, 255, 74;
    --accent-color: var(--cosmic-pink);
    --accent-color-hover: #ff3385;
    --dynamic-primary: var(--neon-blue);
    --dynamic-secondary: var(--cosmic-pink);

    /* Gradients */
    --button-gradient: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --button-shine: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));

    /* Shadows */
    --card-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    --button-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    --button-hover-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    --card-hover-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);

    /* Border Radius */
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 20px;

    /* Transitions */
    --transition-speed: 0.3s;
    --transition-fast: 0.2s;
    --transition-slow: 0.5s;
}

/* ===== Container Styles ===== */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    margin-top: 120px; /* Account for fixed header */
}

/* ===== Hero Section ===== */
.hero {
    position: relative;
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    padding: 6rem 1rem;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(13, 17, 23, 0.8) 0%, rgba(13, 17, 23, 0.9) 100%);
}

/* Add a subtle animated background pattern */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(111, 0, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(0, 224, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 0, 110, 0.05) 0%, transparent 50%);
    opacity: 0.8;
    z-index: 1;
    animation: pulse 15s infinite alternate ease-in-out;
}

@keyframes pulse {
    0% {
        opacity: 0.5;
        background-position: 0% 0%;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        opacity: 0.5;
        background-position: 100% 100%;
    }
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    width: 100%;
    max-width: 800px;
    padding: 3.5rem;
    margin: 0 auto;
    background: rgba(13, 17, 23, 0.8);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(111, 0, 255, 0.1),
        0 0 20px rgba(0, 224, 255, 0.1);
    animation: float 6s infinite ease-in-out;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink), var(--electric-violet));
    background-size: 200% auto;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 800;
    letter-spacing: -0.02em;
    animation: shine 8s linear infinite;
    text-shadow: 0 0 30px rgba(111, 0, 255, 0.2);
}

@keyframes shine {
    0% {
        background-position: 0% center;
    }
    100% {
        background-position: 200% center;
    }
}

.hero-content p {
    font-size: clamp(1.1rem, 2vw, 1.5rem);
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.hero-content .cta-button {
    display: inline-block;
    padding: 1.2rem 3rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    color: white;
    background: var(--button-gradient);
    background-size: 200% auto;
    border-radius: 50px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-transform: uppercase;
    letter-spacing: 1.5px;
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.2),
        0 0 15px rgba(0, 224, 255, 0.3),
        0 0 15px rgba(255, 0, 110, 0.3);
    position: relative;
    overflow: hidden;
}

.hero-content .cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--button-shine);
    transition: all 0.6s ease;
    opacity: 0.5;
}

.hero-content .cta-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 224, 255, 0.5),
        0 0 20px rgba(255, 0, 110, 0.5);
    background-position: right center;
}

.hero-content .cta-button:hover::before {
    left: 100%;
}

.hero-content .cta-button:active {
    transform: translateY(-2px) scale(1.02);
}

/* Star Animation */
.stars {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
    opacity: 0.6;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #fff;
    border-radius: 50%;
    animation: shooting-star 1.5s linear infinite;
    opacity: 0;
}

@keyframes shooting-star {
    0% {
        transform: translate(-100%, -100%);
        opacity: 1;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translate(200%, 200%);
        opacity: 0;
    }
}

/* Stagger star animations */
.star:nth-child(1) { animation-delay: 0s; }
.star:nth-child(2) { animation-delay: 0.15s; }
.star:nth-child(3) { animation-delay: 0.3s; }
.star:nth-child(4) { animation-delay: 0.45s; }
.star:nth-child(5) { animation-delay: 0.6s; }
.star:nth-child(6) { animation-delay: 0.75s; }
.star:nth-child(7) { animation-delay: 0.9s; }
.star:nth-child(8) { animation-delay: 1.05s; }
.star:nth-child(9) { animation-delay: 1.2s; }
.star:nth-child(10) { animation-delay: 1.35s; }
.star:nth-child(11) { animation-delay: 1.5s; }
.star:nth-child(12) { animation-delay: 1.65s; }
.star:nth-child(13) { animation-delay: 1.8s; }
.star:nth-child(14) { animation-delay: 1.95s; }
.star:nth-child(15) { animation-delay: 2.1s; }
.star:nth-child(16) { animation-delay: 2.25s; }
.star:nth-child(17) { animation-delay: 2.4s; }
.star:nth-child(18) { animation-delay: 2.55s; }
.star:nth-child(19) { animation-delay: 2.7s; }
.star:nth-child(20) { animation-delay: 2.85s; }
.star:nth-child(21) { animation-delay: 3s; }
.star:nth-child(22) { animation-delay: 3.15s; }
.star:nth-child(23) { animation-delay: 3.3s; }
.star:nth-child(24) { animation-delay: 3.45s; }
.star:nth-child(25) { animation-delay: 3.6s; }
.star:nth-child(26) { animation-delay: 3.75s; }
.star:nth-child(27) { animation-delay: 3.9s; }
.star:nth-child(28) { animation-delay: 4.05s; }
.star:nth-child(29) { animation-delay: 4.2s; }
.star:nth-child(30) { animation-delay: 4.35s; }
.star:nth-child(31) { animation-delay: 4.5s; }
.star:nth-child(32) { animation-delay: 4.65s; }
.star:nth-child(33) { animation-delay: 4.8s; }
.star:nth-child(34) { animation-delay: 4.95s; }
.star:nth-child(35) { animation-delay: 5.1s; }
.star:nth-child(36) { animation-delay: 5.25s; }
.star:nth-child(37) { animation-delay: 5.4s; }
.star:nth-child(38) { animation-delay: 5.55s; }
.star:nth-child(39) { animation-delay: 5.7s; }
.star:nth-child(40) { animation-delay: 5.85s; }
.star:nth-child(41) { animation-delay: 6s; }
.star:nth-child(42) { animation-delay: 6.15s; }
.star:nth-child(43) { animation-delay: 6.3s; }
.star:nth-child(44) { animation-delay: 6.45s; }
.star:nth-child(45) { animation-delay: 6.6s; }
.star:nth-child(46) { animation-delay: 6.75s; }
.star:nth-child(47) { animation-delay: 6.9s; }
.star:nth-child(48) { animation-delay: 7.05s; }
.star:nth-child(49) { animation-delay: 7.2s; }
.star:nth-child(50) { animation-delay: 7.35s; }

/* ===== Quick Access Section ===== */
.quick-access {
    margin: 3rem auto 4rem;
    text-align: center;
    max-width: 1200px;
    padding: 3rem 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.quick-access h2 {
    margin-bottom: 2rem;
    font-size: 2rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    position: relative;
}

.quick-access h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    border-radius: 3px;
}

.quick-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2.5rem;
    padding: 1rem 0;
    flex-wrap: wrap;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 120px;
    height: 120px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.quick-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(var(--neon-blue-rgb), 0.1) 0%,
        rgba(var(--cosmic-pink-rgb), 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.quick-link:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.2),
        0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
}

.quick-link:hover::before {
    opacity: 1;
}

.quick-link img {
    width: 48px;
    height: 48px;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
    filter: drop-shadow(0 0 8px rgba(var(--neon-blue-rgb), 0.3));
}

.quick-link:hover img {
    transform: scale(1.1);
}

.quick-link span {
    font-size: 0.95rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.quick-link:hover span {
    color: var(--neon-blue);
}

/* ===== Section Styles ===== */
.section {
    margin: 5rem 0;
    padding: 1rem;
    position: relative;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 0 1rem;
}

.section h2 {
    font-size: 2rem;
    font-weight: 700;
    position: relative;
    display: inline-block;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    margin: 0;
}

.section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    border-radius: 3px;
    transition: width 0.3s ease;
}

.section h2:hover::after {
    width: 100%;
}

.view-all {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.view-all:hover {
    color: var(--neon-blue);
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.view-all i {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.view-all:hover i {
    transform: translateX(3px);
}

.section .section-description {
    color: rgba(255, 255, 255, 0.7);
    max-width: 800px;
    margin: 0 auto 24px;
    text-align: center;
    font-size: 1.1em;
    line-height: 1.6;
}

/* ===== Card Styles ===== */
.card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 20px 24px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box; /* Add this to fix overlapping issues */
    overflow: hidden; /* Prevent content from overflowing */
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    pointer-events: none;
}

.card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
    z-index: 1;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow:
        0 15px 30px rgba(0, 0, 0, 0.2),
        0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.card:hover::before {
    opacity: 1;
}

.card:hover::after {
    transform: scaleX(1);
}

.card h1,
.card h2,
.card h3 {
    color: #ffffff;
    font-weight: 600;
    margin-bottom: 12px;
    letter-spacing: 0.5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card p {
    color: rgba(255, 255, 255, 0.85);
    font-size: 0.95em;
    line-height: 1.6;
    /* Limit to 3 lines of text with ellipsis */
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 4.8em; /* 3 lines × 1.6 line-height */
}

.card .button,
.card button,
.card a.button {
    margin-top: auto;
    display: inline-block;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    color: #fff;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease, transform 0.2s ease;
    white-space: nowrap; /* Prevent text wrapping */
    overflow: hidden; /* Hide overflow text */
    text-overflow: ellipsis; /* Add ellipsis for overflow text */
    max-width: 100%; /* Ensure button doesn't exceed card width */
    box-sizing: border-box; /* Include padding in width calculation */
}

.card .button:hover,
.card button:hover,
.card a.button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 12px rgba(0, 224, 255, 0.3);
}

/* Card with image */
.card .img-container {
    width: 100%;
    margin-bottom: 16px;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    height: 160px;
}

.card .img-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        to bottom,
        transparent 70%,
        rgba(0, 0, 0, 0.5) 100%
    );
    opacity: 0.7;
    z-index: 1;
    transition: opacity 0.3s ease;
}

.card .img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);
    filter: brightness(0.9);
}

.card:hover .img-container img {
    transform: scale(1.08);
    filter: brightness(1.1) contrast(1.1);
}

.card:hover .img-container::before {
    opacity: 0.4;
}

/* Play overlay */
.card .img-container .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.card:hover .img-container .play-overlay {
    opacity: 1;
}

.card .img-container .play-button {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: var(--button-gradient);
    border: none;
    color: white;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    transform: scale(0.8);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.card:hover .img-container .play-button {
    transform: scale(1);
}

.card .img-container .play-button:hover {
    transform: scale(1.1);
    box-shadow: 0 0 30px rgba(0, 224, 255, 0.4);
}

/* Card Grid Layout */
.section .cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
    margin-top: 24px;
    padding: 0 10px;
    align-items: stretch;
    box-sizing: border-box; /* Add this to fix overlapping issues */
}

/* Specific section styles */
.trending .cards,
.featured-artists .cards,
.new-releases .cards {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* ===== Carousel Styles ===== */
.carousel-container {
    margin: 2rem 0;
    padding: 1rem 0;
    overflow: hidden;
    box-sizing: border-box;
}

.carousel-cell {
    width: 300px;
    margin-right: 20px;
    box-sizing: border-box;
}

.carousel-cell .card {
    width: 100%;
    height: 100%;
    margin: 0;
    box-sizing: border-box;
    min-height: 300px; /* Set a minimum height for consistency */
}

/* ===== Back to Top Button ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--button-gradient);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 100;
    box-shadow: var(--button-shadow);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-5px);
    box-shadow: var(--button-hover-shadow);
}

/* ===== Footer Styles ===== */
.site-footer {
    background: rgba(13, 17, 23, 0.9);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-top: 4rem;
    width: 100%;
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.2);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.footer-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.copyright {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
}

.footer-nav {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.footer-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-nav a:hover {
    color: var(--neon-blue);
    text-decoration: underline;
}

/* ===== Responsive Styles ===== */
@media (max-width: 1200px) {
    .section .cards {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 992px) {
    .section .cards {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    .hero-content {
        padding: 2rem;
    }
}

@media (max-width: 768px) {
    .section .cards {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;
    }

    .card {
        padding: 16px 20px;
    }

    .card h3 {
        font-size: 1.1em;
    }

    .card p {
        font-size: 0.9em;
    }

    .carousel-cell {
        width: 250px;
    }

    .hero {
        padding: 3rem 1rem;
    }

    .hero-content {
        padding: 2rem;
        width: 90%;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .quick-links {
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .section .cards {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 12px;
    }

    .card {
        padding: 12px 16px;
    }

    .card h3 {
        font-size: 1em;
        margin-bottom: 8px;
    }

    .card p {
        font-size: 0.8em;
    }

    .card .button,
    .card button,
    .card a.button {
        padding: 8px 12px;
        font-size: 0.9em;
    }

    .carousel-cell {
        width: 200px;
    }

    .hero {
        padding: 2rem 1rem;
    }

    .hero-content {
        padding: 1.5rem;
        width: 95%;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }

    .hero-content p {
        font-size: 0.9rem;
    }

    .quick-link {
        width: 80px;
    }
}

/* Reduce motion if user prefers */
@media (prefers-reduced-motion: reduce) {
    .hero-content::before,
    .card:hover .img-container img,
    .card:hover,
    .star {
        animation: none;
        transition: none;
        transform: none;
    }
}
