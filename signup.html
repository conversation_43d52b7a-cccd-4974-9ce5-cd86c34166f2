<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="signup.css">
</head>
<body>
    <h1 class="welcome-header">Join <PERSON></h1>
    <div class="signup-container">
        <div class="logo">
            <img src="imgs/logo-B.png" alt="Banshee Logo">
        </div>
        <h1>Create Your Account</h1>
        <form id="signupForm" method="POST" action="/api/auth/signup" autocomplete="on" novalidate>
            <input type="hidden" name="csrf_token" id="csrf_token">
            <div class="input-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required
                       minlength="3" maxlength="50"
                       pattern="^[a-zA-Z0-9_-]+$"
                       autocomplete="username">
                <div class="validation-message" aria-live="polite"></div>
            </div>
            <div class="input-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required
                       pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                       autocomplete="email">
                <div class="validation-message" aria-live="polite"></div>
            </div>
            <div class="input-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required
                       minlength="8" maxlength="128"
                       pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
                       autocomplete="new-password">
                <i class="fas fa-eye password-toggle" aria-label="Toggle password visibility"></i>
                <div class="password-strength" aria-live="polite">
                    <div class="strength-bar weak"></div>
                    <div class="strength-bar medium"></div>
                    <div class="strength-bar strong"></div>
                </div>
                <div class="validation-message" aria-live="polite"></div>
            </div>
            <div class="input-group">
                <label for="confirm-password">Confirm Password</label>
                <input type="password" id="confirm-password" name="confirm-password" required
                       minlength="8" maxlength="128"
                       autocomplete="new-password">
                <i class="fas fa-eye password-toggle" aria-label="Toggle password visibility"></i>
                <div class="validation-message" aria-live="polite"></div>
            </div>
            <div class="terms-group">
                <label>
                    <input type="checkbox" required>
                    I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                </label>
            </div>
            <button type="submit" class="signup-button">Create Account</button>
        </form>
        <div class="social-signup">
            <p>Or sign up with</p>
            <div class="social-buttons">
                <button class="social-button">
                    <i class="fab fa-google"></i>
                    Google
                </button>
                <button class="social-button">
                    <i class="fab fa-facebook-f"></i>
                    Facebook
                </button>
                <button class="social-button">
                    <i class="fab fa-apple"></i>
                    Apple
                </button>
            </div>
        </div>
        <p class="login-link">Already have an account? <a href="login.html">Login here</a></p>
    </div>
    <div id="message" class="message"></div>
    <div id="loader" class="loader-container">
        <div class="loader"></div>
    </div>
    <script src="signup.js" type="module"></script>
</body>
</html>
