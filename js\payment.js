import { API, utils } from './api.js';

const cardTypes = {
    visa: /^4/,
    mastercard: /^5[1-5]/,
    amex: /^3[47]/,
    discover: /^6/
};

function detectCardType(number) {
    for (const [type, regex] of Object.entries(cardTypes)) {
        if (regex.test(number)) {
            return type;
        }
    }
    return 'generic';
}

function formatCardNumber(input) {
    let value = input.value.replace(/\D/g, '');
    let formattedValue = '';

    if (value.length > 0) {
        const cardType = detectCardType(value);
        const cardTypeIcon = input.parentElement.querySelector('.card-type-icon');
        if (cardTypeIcon) {
            cardTypeIcon.className = `card-type-icon fab fa-cc-${cardType}`;
        }

        // Format based on card type
        if (cardType === 'amex') {
            formattedValue = value.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3');
        } else {
            formattedValue = value.replace(/(\d{4})/g, '$1 ').trim();
        }
    }

    input.value = formattedValue;
}

function formatExpiryDate(input) {
    let value = input.value.replace(/\D/g, '');
    if (value.length >= 2) {
        const month = parseInt(value.substring(0, 2));
        if (month > 12) value = '12' + value.slice(2);
        value = value.replace(/(\d{2})(\d{0,2})/, '$1/$2');
    }
    input.value = value;
}

function validateCVC(input) {
    let value = input.value.replace(/\D/g, '');
    input.value = value.slice(0, 4);
}

// Initialize form validation and formatting
document.addEventListener('DOMContentLoaded', () => {
    // Load selected plan details from session storage
    loadSelectedPlan();

    const cardNumber = document.getElementById('card-number');
    const expiryDate = document.getElementById('expiry-date');
    const cvc = document.getElementById('cvc');

    cardNumber.addEventListener('input', () => formatCardNumber(cardNumber));
    expiryDate.addEventListener('input', () => formatExpiryDate(expiryDate));
    cvc.addEventListener('input', () => validateCVC(cvc));

    // Payment method selection
    const paymentMethods = document.querySelectorAll('.payment-method');
    paymentMethods.forEach(method => {
        method.addEventListener('click', () => {
            paymentMethods.forEach(m => m.classList.remove('active'));
            method.classList.add('active');
        });
    });

    // Initialize form submission
    const paymentForm = document.getElementById('paymentForm');
    if (paymentForm) {
        paymentForm.addEventListener('submit', handlePaymentSubmission);
    }

    // Restore form progress
    restoreProgress();
});

function loadSelectedPlan() {
    // Get selected plan from session storage
    const selectedPlanJson = sessionStorage.getItem('selectedPlan');

    if (!selectedPlanJson) {
        // If no plan is selected, redirect back to subscription page
        window.location.href = 'subscription.html';
        return;
    }

    try {
        const selectedPlan = JSON.parse(selectedPlanJson);

        // Update UI with selected plan details
        document.getElementById('selectedPlanName').textContent = selectedPlan.name;
        document.getElementById('selectedPlanPrice').textContent = `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;

        // Update total section
        document.getElementById('subtotal').textContent = `$${selectedPlan.price.toFixed(2)}`;
        document.getElementById('total').textContent = `$${selectedPlan.price.toFixed(2)}`;

        // Update button price
        document.querySelector('.button-price').textContent = `$${selectedPlan.price.toFixed(2)}/${selectedPlan.interval}`;

    } catch (error) {
        console.error('Error parsing selected plan:', error);
        showError('There was an error loading your selected plan. Please try again.');
    }
}

async function handlePaymentSubmission(event) {
    event.preventDefault();

    // Get subscription ID from session storage
    const subscriptionId = sessionStorage.getItem('subscriptionId');
    if (!subscriptionId) {
        showError('Subscription information not found. Please try again.');
        return;
    }

    // Show loading state
    const submitButton = event.target.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.querySelector('.button-text').textContent;
    submitButton.disabled = true;
    submitButton.querySelector('.button-text').textContent = 'Processing...';

    try {
        // Collect payment details from form
        const paymentDetails = {
            cardName: document.getElementById('card-name').value,
            cardNumber: document.getElementById('card-number').value.replace(/\s/g, ''),
            expiryDate: document.getElementById('expiry-date').value,
            cvc: document.getElementById('cvc').value,
            billingAddress: {
                country: document.getElementById('country').value,
                zipCode: document.getElementById('zip').value
            }
        };

        // Process payment
        const response = await API.processPayment(paymentDetails, subscriptionId);

        if (response.success) {
            // Save transaction details to session storage for confirmation page
            sessionStorage.setItem('transactionId', response.transactionId);
            sessionStorage.setItem('nextBillingDate', response.nextBillingDate);

            // Redirect to confirmation page
            window.location.href = 'confirmation.html';
        } else {
            showError('Payment processing failed. Please try again.');
        }
    } catch (error) {
        console.error('Payment error:', error);
        showError(error.message || 'An error occurred while processing your payment. Please try again.');
    } finally {
        // Reset button state
        submitButton.disabled = false;
        submitButton.querySelector('.button-text').textContent = originalButtonText;
    }
}

function showError(message) {
    // Create error message element
    const errorElement = document.createElement('div');
    errorElement.className = 'payment-error';
    errorElement.textContent = message;

    // Add to page
    const paymentForm = document.getElementById('paymentForm');
    const existingError = document.querySelector('.payment-error');

    if (existingError) {
        existingError.remove();
    }

    paymentForm.prepend(errorElement);

    // Scroll to error
    errorElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

// Save form progress
const saveProgress = debounce(() => {
    const formData = {
        cardName: document.getElementById('card-name').value,
        // Don't save sensitive data
        lastUpdated: new Date().toISOString()
    };
    sessionStorage.setItem('paymentProgress', JSON.stringify(formData));
}, 1000);

// Restore form progress
function restoreProgress() {
    const savedData = sessionStorage.getItem('paymentProgress');
    if (savedData) {
        const formData = JSON.parse(savedData);
        document.getElementById('card-name').value = formData.cardName;
    }
}

// Helper function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}