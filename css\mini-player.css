/**
 * Mini Player Styles
 * Reusable mini player component
 */

/* Mini Player Container */
.mini-player {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background: linear-gradient(90deg, rgba(13, 17, 23, 0.98), rgba(25, 32, 44, 0.98));
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.mini-player.hidden {
    transform: translateY(100%);
}

/* Now Playing Section */
.now-playing {
    display: flex;
    align-items: center;
    width: 300px;
    min-width: 200px;
}

.now-playing-cover {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin-right: 15px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.now-playing-cover:hover {
    transform: scale(1.05);
}

.now-playing-info {
    flex: 1;
    min-width: 0;
}

.now-playing-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 4px;
    color: var(--text-color, #ffffff);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.now-playing-artist {
    font-size: 0.85rem;
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.like-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.like-btn:hover {
    color: #ff006e;
    background: rgba(255, 0, 110, 0.1);
    transform: scale(1.1);
}

/* Player Controls */
.player-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 0 0 auto;
}

.control-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.control-btn:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.control-btn.play-pause {
    background: var(--button-gradient, linear-gradient(45deg, #00E0FF, #FF006E));
    color: white;
    font-size: 1.2rem;
    width: 48px;
    height: 48px;
    box-shadow: 0 4px 15px rgba(0, 224, 255, 0.3);
}

.control-btn.play-pause:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 224, 255, 0.4);
}

.control-btn.shuffle.active,
.control-btn.repeat.active {
    color: #00e0ff;
    background: rgba(0, 224, 255, 0.1);
}

/* Progress Section */
.player-progress {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    margin: 0 30px;
    min-width: 200px;
}

.current-time,
.total-time {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    min-width: 35px;
    text-align: center;
}

.progress-bar-container {
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.progress-bar-container:hover {
    height: 8px;
}

.progress-bar {
    height: 100%;
    background: var(--button-gradient, linear-gradient(45deg, #00E0FF, #FF006E));
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    background: white;
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.progress-bar-container:hover .progress-bar::after {
    opacity: 1;
}

/* Player Options */
.player-options {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.volume-slider {
    position: absolute;
    bottom: 100%;
    right: 0;
    background: rgba(25, 32, 44, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.player-options:hover .volume-slider {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

#volume-control {
    writing-mode: bt-lr; /* IE */
    -webkit-appearance: slider-vertical; /* WebKit */
    width: 30px;
    height: 100px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    border-radius: 15px;
}

#volume-control::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--button-gradient, linear-gradient(45deg, #00E0FF, #FF006E));
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

#volume-control::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--button-gradient, linear-gradient(45deg, #00E0FF, #FF006E));
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .player-progress {
        margin: 0 20px;
    }
    
    .now-playing {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .mini-player {
        height: auto;
        flex-direction: column;
        padding: 15px;
        gap: 15px;
    }
    
    .now-playing {
        width: 100%;
        justify-content: center;
    }
    
    .player-controls {
        order: 2;
    }
    
    .player-progress {
        order: 1;
        margin: 0;
        width: 100%;
    }
    
    .player-options {
        order: 3;
        justify-content: center;
    }
    
    .volume-slider {
        position: fixed;
        bottom: 120px;
        right: 20px;
    }
}

@media (max-width: 480px) {
    .mini-player {
        padding: 10px;
        gap: 10px;
    }
    
    .now-playing-cover {
        width: 50px;
        height: 50px;
    }
    
    .control-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .control-btn.play-pause {
        width: 42px;
        height: 42px;
        font-size: 1.1rem;
    }
    
    .now-playing-title {
        font-size: 0.9rem;
    }
    
    .now-playing-artist {
        font-size: 0.8rem;
    }
}

/* Animation for showing/hiding */
@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.mini-player.show {
    animation: slideUp 0.3s ease;
}

/* Ensure content doesn't overlap with mini player */
body {
    padding-bottom: 80px;
}

@media (max-width: 768px) {
    body {
        padding-bottom: 120px;
    }
}
