/* Library Manager Styles */

:root {
    --sidebar-width: 280px;
    --mini-player-height: 90px;
    --section-gap: 2rem;
    --card-bg: rgba(25, 32, 44, 0.4);
    --card-hover-bg: rgba(25, 32, 44, 0.6);
    --border-color: rgba(255, 255, 255, 0.1);
    --hover-border: rgba(0, 224, 255, 0.3);
}

/* Layout */
.library-container {
    display: flex;
    min-height: calc(100vh - var(--mini-player-height));
    padding-top: 90px; /* Account for fixed header */
}

.library-sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, rgba(13, 17, 23, 0.95) 0%, rgba(19, 23, 32, 0.95) 100%);
    border-right: 1px solid var(--border-color);
    padding: 20px;
    position: fixed;
    top: 90px; /* Account for fixed header */
    left: 0;
    height: calc(100vh - 90px - var(--mini-player-height));
    overflow-y: auto;
    z-index: 10;
}

.library-main {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 30px;
    min-height: calc(100vh - 90px - var(--mini-player-height));
}

/* Sidebar Styles */
.sidebar-section {
    margin-bottom: 30px;
}

.sidebar-section h2 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-color);
    font-weight: 600;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 10px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
}

.sidebar-nav a:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
}

.sidebar-nav a.active {
    background: linear-gradient(90deg, rgba(0, 224, 255, 0.1), transparent);
    color: var(--neon-blue);
    border-left: 3px solid var(--neon-blue);
}

.playlists-list {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;
}

.playlist-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.playlist-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.playlist-item-cover {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 10px;
}

.playlist-item-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.playlist-item-info h3 {
    font-size: 0.9rem;
    margin: 0 0 3px;
    color: var(--text-color);
}

.playlist-item-info p {
    font-size: 0.8rem;
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
}

/* Main Content Styles */
.main-content, .content-section {
    width: 100%;
}

.content-section.hidden {
    display: none;
}

.main-content h1, .content-section h1 {
    font-size: 2rem;
    margin-bottom: 20px;
    color: var(--text-color);
    font-weight: 700;
}

.playlists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: var(--section-gap);
}

.playlist-card {
    background: var(--card-bg);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.playlist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    background: var(--card-hover-bg);
    border-color: var(--hover-border);
}

.playlist-card-cover {
    position: relative;
    width: 100%;
    padding-top: 100%; /* 1:1 Aspect Ratio */
}

.playlist-card-cover img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.playlist-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.playlist-card:hover .playlist-card-overlay {
    opacity: 1;
}

.playlist-card-info {
    padding: 15px;
}

.playlist-card-info h3 {
    font-size: 1rem;
    margin: 0 0 5px;
    color: var(--text-color);
}

.playlist-card-info p {
    font-size: 0.8rem;
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
}

.view-all {
    font-size: 0.9rem;
    color: var(--neon-blue);
    text-decoration: none;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: var(--cosmic-pink);
    text-decoration: underline;
}

/* Favorites and Recently Played Previews */
.favorites-preview, .recently-played-preview {
    margin-bottom: var(--section-gap);
}

.songs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 20px;
}

.song-card {
    background: var(--card-bg);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.song-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    background: var(--card-hover-bg);
    border-color: var(--hover-border);
}

.song-card-cover {
    position: relative;
    width: 100%;
    padding-top: 100%; /* 1:1 Aspect Ratio */
}

.song-card-cover img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.song-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.song-card:hover .song-card-overlay {
    opacity: 1;
}

.song-card-info {
    padding: 15px;
}

.song-card-info h3 {
    font-size: 1rem;
    margin: 0 0 5px;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.song-card-info p {
    font-size: 0.8rem;
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Songs Table */
.songs-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.songs-table th {
    text-align: left;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    color: rgba(255, 255, 255, 0.7);
    font-weight: 600;
    font-size: 0.9rem;
}

.songs-table td {
    padding: 12px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    font-size: 0.9rem;
}

.songs-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.song-info {
    display: flex;
    align-items: center;
}

.song-info img {
    width: 40px;
    height: 40px;
    border-radius: 4px;
    margin-right: 10px;
    object-fit: cover;
}

/* Playlist Header */
.playlist-header {
    display: flex;
    margin-bottom: 30px;
    background: linear-gradient(135deg, rgba(25, 32, 44, 0.6), rgba(13, 17, 23, 0.6));
    border-radius: 12px;
    padding: 30px;
    border: 1px solid var(--border-color);
}

.playlist-cover {
    width: 200px;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 30px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.playlist-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.playlist-info {
    flex: 1;
}

.playlist-info h1 {
    font-size: 2.5rem;
    margin: 0 0 10px;
    color: var(--text-color);
}

.playlist-info p {
    font-size: 1rem;
    margin: 0 0 15px;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
}

.playlist-meta {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

.playlist-meta span {
    margin-right: 10px;
}

.playlist-actions {
    display: flex;
    gap: 10px;
}

/* Buttons */
.play-btn, .control-btn, .option-btn {
    background: none;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-btn:hover, .control-btn:hover, .option-btn:hover {
    color: var(--neon-blue);
}

.play-all-btn, .create-btn, .edit-playlist-btn, .save-btn {
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.play-all-btn:hover, .create-btn:hover, .edit-playlist-btn:hover, .save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 224, 255, 0.3), 0 5px 15px rgba(255, 0, 110, 0.3);
}

.delete-playlist-btn, .cancel-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 10px 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.delete-playlist-btn:hover {
    background: rgba(255, 59, 48, 0.2);
    color: #ff3b30;
    border-color: rgba(255, 59, 48, 0.3);
}

.cancel-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Mini Player */
.mini-player {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: var(--mini-player-height);
    background: linear-gradient(90deg, rgba(13, 17, 23, 0.95), rgba(25, 32, 44, 0.95));
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 20px;
    z-index: 100;
}

.now-playing {
    display: flex;
    align-items: center;
    width: 250px;
}

.now-playing-cover {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    margin-right: 15px;
    object-fit: cover;
}

.now-playing-info h3 {
    font-size: 1rem;
    margin: 0 0 5px;
    color: var(--text-color);
}

.now-playing-info p {
    font-size: 0.8rem;
    margin: 0;
    color: rgba(255, 255, 255, 0.6);
}

.player-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    flex: 1;
}

.control-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.play-pause {
    background: var(--button-gradient);
    color: white;
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
}

.player-progress {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 300px;
}

.current-time, .total-time {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    width: 40px;
    text-align: center;
}

.progress-bar {
    flex: 1;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    position: relative;
    cursor: pointer;
}

.progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 30%; /* Example progress */
    background: var(--button-gradient);
    border-radius: 2px;
}

.player-options {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 150px;
    justify-content: flex-end;
}

.option-btn {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: linear-gradient(135deg, rgba(25, 32, 44, 0.95), rgba(13, 17, 23, 0.95));
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    border: 1px solid var(--border-color);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-color);
}

.close-modal {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-size: 0.9rem;
}

.form-group input[type="text"],
.form-group textarea {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background: rgba(0, 0, 0, 0.2);
    color: var(--text-color);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group textarea:focus {
    border-color: var(--neon-blue);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 224, 255, 0.2);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

.form-group.checkbox {
    display: flex;
    align-items: center;
}

.form-group.checkbox input {
    margin-right: 10px;
}

.form-group.checkbox label {
    margin-bottom: 0;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
}

/* Toast Styles */
.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 24px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.3s ease-out;
}

.toast.success {
    border-left: 4px solid var(--neon-blue);
}

.toast.error {
    border-left: 4px solid #ff3b30;
}

.toast.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translate(-50%, 20px); }
    to { opacity: 1; transform: translate(-50%, 0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translate(-50%, 0); }
    to { opacity: 0; transform: translate(-50%, 20px); }
}

/* Empty State */
.empty-message {
    text-align: center;
    padding: 30px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 1rem;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .library-sidebar {
        width: 240px;
    }
    
    .library-main {
        margin-left: 240px;
    }
    
    .playlist-cover {
        width: 150px;
        height: 150px;
    }
}

@media (max-width: 768px) {
    .library-container {
        flex-direction: column;
    }
    
    .library-sidebar {
        width: 100%;
        position: static;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        padding: 15px;
    }
    
    .library-main {
        margin-left: 0;
        padding: 20px;
    }
    
    .playlist-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 20px;
    }
    
    .playlist-cover {
        margin-right: 0;
        margin-bottom: 20px;
    }
    
    .player-progress {
        width: 200px;
    }
    
    .now-playing {
        width: 180px;
    }
    
    .player-options {
        width: 120px;
    }
}

@media (max-width: 480px) {
    .mini-player {
        flex-wrap: wrap;
        height: auto;
        padding: 10px;
    }
    
    .now-playing {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .player-controls {
        order: 3;
        width: 100%;
        margin-top: 10px;
    }
    
    .player-progress {
        order: 2;
        width: 70%;
    }
    
    .player-options {
        order: 4;
        width: 100%;
        justify-content: center;
        margin-top: 10px;
    }
    
    .songs-table th:nth-child(3),
    .songs-table td:nth-child(3),
    .songs-table th:nth-child(4),
    .songs-table td:nth-child(4) {
        display: none;
    }
}
