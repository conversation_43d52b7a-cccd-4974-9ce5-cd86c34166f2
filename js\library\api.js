/**
 * API functions for music library management
 */

import { Song, Playlist, Library } from './models.js';

/**
 * Library API class for managing music library data
 */
class LibraryAPI {
    constructor() {
        this.currentUser = null;
        this.library = null;
        this.initializeLibrary();
    }

    /**
     * Initialize the library for the current user
     */
    initializeLibrary() {
        // In a real app, we would get the current user from authentication
        // For now, we'll use a mock user ID
        this.currentUser = 'user_123';
        
        // Try to load the library from localStorage
        const savedLibrary = localStorage.getItem(`library_${this.currentUser}`);
        if (savedLibrary) {
            try {
                const parsedLibrary = JSON.parse(savedLibrary);
                this.library = new Library(this.currentUser);
                
                // Restore playlists
                if (parsedLibrary.playlists) {
                    this.library.playlists = parsedLibrary.playlists.map(p => {
                        const playlist = new Playlist(p);
                        playlist.songs = p.songs.map(s => new Song(s));
                        return playlist;
                    });
                }
                
                // Restore favorites
                if (parsedLibrary.favorites) {
                    this.library.favorites = parsedLibrary.favorites.map(s => new Song(s));
                }
                
                // Restore recently played
                if (parsedLibrary.recentlyPlayed) {
                    this.library.recentlyPlayed = parsedLibrary.recentlyPlayed.map(s => new Song(s));
                }
                
                // Restore queue
                if (parsedLibrary.queue) {
                    this.library.queue = parsedLibrary.queue.map(s => new Song(s));
                }
                
                // Restore downloads
                if (parsedLibrary.downloads) {
                    this.library.downloads = parsedLibrary.downloads.map(s => new Song(s));
                }
            } catch (error) {
                console.error('Error parsing saved library:', error);
                this.library = new Library(this.currentUser);
            }
        } else {
            // Create a new library
            this.library = new Library(this.currentUser);
            
            // Add some sample data
            this.addSampleData();
        }
    }

    /**
     * Save the library to localStorage
     */
    saveLibrary() {
        localStorage.setItem(`library_${this.currentUser}`, JSON.stringify(this.library));
    }

    /**
     * Add sample data to the library
     */
    addSampleData() {
        // Create some sample songs
        const songs = [
            new Song({
                id: 'song_1',
                title: 'Blinding Lights',
                artist: 'The Weeknd',
                album: 'After Hours',
                duration: 200,
                coverUrl: 'imgs/album-01.png',
                genre: ['Pop', 'R&B'],
                releaseDate: '2020-03-20',
                popularity: 95
            }),
            new Song({
                id: 'song_2',
                title: 'Levitating',
                artist: 'Dua Lipa',
                album: 'Future Nostalgia',
                duration: 203,
                coverUrl: 'imgs/album-02.png',
                genre: ['Pop', 'Dance'],
                releaseDate: '2020-10-01',
                popularity: 92
            }),
            new Song({
                id: 'song_3',
                title: 'Save Your Tears',
                artist: 'The Weeknd',
                album: 'After Hours',
                duration: 215,
                coverUrl: 'imgs/album-01.png',
                genre: ['Pop', 'R&B'],
                releaseDate: '2020-03-20',
                popularity: 90
            }),
            new Song({
                id: 'song_4',
                title: 'Stay',
                artist: 'The Kid LAROI, Justin Bieber',
                album: 'F*CK LOVE 3: OVER YOU',
                duration: 141,
                coverUrl: 'imgs/album-03.png',
                genre: ['Pop', 'Hip-Hop'],
                releaseDate: '2021-07-09',
                popularity: 88
            }),
            new Song({
                id: 'song_5',
                title: 'Good 4 U',
                artist: 'Olivia Rodrigo',
                album: 'SOUR',
                duration: 178,
                coverUrl: 'imgs/album-04.png',
                genre: ['Pop', 'Rock'],
                releaseDate: '2021-05-14',
                popularity: 87
            }),
            new Song({
                id: 'song_6',
                title: 'Montero (Call Me By Your Name)',
                artist: 'Lil Nas X',
                album: 'MONTERO',
                duration: 137,
                coverUrl: 'imgs/album-05.png',
                genre: ['Pop', 'Hip-Hop'],
                releaseDate: '2021-03-26',
                popularity: 86
            }),
            new Song({
                id: 'song_7',
                title: 'Kiss Me More',
                artist: 'Doja Cat ft. SZA',
                album: 'Planet Her',
                duration: 208,
                coverUrl: 'imgs/album-02.png',
                genre: ['Pop', 'R&B'],
                releaseDate: '2021-04-09',
                popularity: 85
            }),
            new Song({
                id: 'song_8',
                title: 'Peaches',
                artist: 'Justin Bieber ft. Daniel Caesar, Giveon',
                album: 'Justice',
                duration: 198,
                coverUrl: 'imgs/album-03.png',
                genre: ['Pop', 'R&B'],
                releaseDate: '2021-03-19',
                popularity: 84
            })
        ];

        // Create some sample playlists
        const playlist1 = new Playlist({
            id: 'playlist_1',
            name: 'My Favorites',
            description: 'A collection of my favorite songs',
            coverUrl: 'imgs/album-01.png',
            createdBy: this.currentUser,
            isPublic: true
        });

        const playlist2 = new Playlist({
            id: 'playlist_2',
            name: 'Workout Mix',
            description: 'Perfect for hitting the gym',
            coverUrl: 'imgs/album-03.png',
            createdBy: this.currentUser,
            isPublic: true
        });

        // Add songs to playlists
        playlist1.addSong(songs[0]);
        playlist1.addSong(songs[2]);
        playlist1.addSong(songs[4]);
        playlist1.addSong(songs[6]);

        playlist2.addSong(songs[1]);
        playlist2.addSong(songs[3]);
        playlist2.addSong(songs[5]);
        playlist2.addSong(songs[7]);

        // Add playlists to library
        this.library.addPlaylist(playlist1);
        this.library.addPlaylist(playlist2);

        // Add some songs to favorites
        this.library.addToFavorites(songs[0]);
        this.library.addToFavorites(songs[3]);
        this.library.addToFavorites(songs[6]);

        // Add some songs to recently played
        this.library.addToRecentlyPlayed(songs[0]);
        this.library.addToRecentlyPlayed(songs[2]);
        this.library.addToRecentlyPlayed(songs[5]);

        // Save the library
        this.saveLibrary();
    }

    /**
     * Get all playlists for the current user
     * @returns {Array} Array of playlists
     */
    getPlaylists() {
        return this.library.playlists;
    }

    /**
     * Get a playlist by ID
     * @param {string} playlistId - The ID of the playlist to get
     * @returns {Playlist} The playlist
     */
    getPlaylist(playlistId) {
        return this.library.playlists.find(p => p.id === playlistId);
    }

    /**
     * Create a new playlist
     * @param {Object} playlistData - The playlist data
     * @returns {Playlist} The created playlist
     */
    createPlaylist(playlistData) {
        const playlist = new Playlist({
            ...playlistData,
            createdBy: this.currentUser,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        });
        
        this.library.addPlaylist(playlist);
        this.saveLibrary();
        
        return playlist;
    }

    /**
     * Update a playlist
     * @param {string} playlistId - The ID of the playlist to update
     * @param {Object} playlistData - The updated playlist data
     * @returns {Playlist} The updated playlist
     */
    updatePlaylist(playlistId, playlistData) {
        const playlist = this.getPlaylist(playlistId);
        
        if (!playlist) {
            throw new Error(`Playlist with ID ${playlistId} not found`);
        }
        
        // Update the playlist properties
        Object.assign(playlist, {
            ...playlistData,
            updatedAt: new Date().toISOString()
        });
        
        this.saveLibrary();
        
        return playlist;
    }

    /**
     * Delete a playlist
     * @param {string} playlistId - The ID of the playlist to delete
     */
    deletePlaylist(playlistId) {
        this.library.removePlaylist(playlistId);
        this.saveLibrary();
    }

    /**
     * Add a song to a playlist
     * @param {string} playlistId - The ID of the playlist
     * @param {Song} song - The song to add
     */
    addSongToPlaylist(playlistId, song) {
        const playlist = this.getPlaylist(playlistId);
        
        if (!playlist) {
            throw new Error(`Playlist with ID ${playlistId} not found`);
        }
        
        playlist.addSong(song);
        this.saveLibrary();
    }

    /**
     * Remove a song from a playlist
     * @param {string} playlistId - The ID of the playlist
     * @param {string} songId - The ID of the song to remove
     */
    removeSongFromPlaylist(playlistId, songId) {
        const playlist = this.getPlaylist(playlistId);
        
        if (!playlist) {
            throw new Error(`Playlist with ID ${playlistId} not found`);
        }
        
        playlist.removeSong(songId);
        this.saveLibrary();
    }

    /**
     * Get all favorite songs
     * @returns {Array} Array of favorite songs
     */
    getFavorites() {
        return this.library.favorites;
    }

    /**
     * Add a song to favorites
     * @param {Song} song - The song to add to favorites
     */
    addToFavorites(song) {
        this.library.addToFavorites(song);
        this.saveLibrary();
    }

    /**
     * Remove a song from favorites
     * @param {string} songId - The ID of the song to remove from favorites
     */
    removeFromFavorites(songId) {
        this.library.removeFromFavorites(songId);
        this.saveLibrary();
    }

    /**
     * Check if a song is in favorites
     * @param {string} songId - The ID of the song to check
     * @returns {boolean} True if the song is in favorites
     */
    isFavorite(songId) {
        return this.library.favorites.some(song => song.id === songId);
    }

    /**
     * Get recently played songs
     * @returns {Array} Array of recently played songs
     */
    getRecentlyPlayed() {
        return this.library.recentlyPlayed;
    }

    /**
     * Add a song to recently played
     * @param {Song} song - The song to add to recently played
     */
    addToRecentlyPlayed(song) {
        this.library.addToRecentlyPlayed(song);
        this.saveLibrary();
    }

    /**
     * Get the playback queue
     * @returns {Array} Array of songs in the queue
     */
    getQueue() {
        return this.library.queue;
    }

    /**
     * Add a song to the playback queue
     * @param {Song} song - The song to add to the queue
     */
    addToQueue(song) {
        this.library.addToQueue(song);
        this.saveLibrary();
    }

    /**
     * Remove a song from the playback queue
     * @param {number} index - The index of the song to remove
     */
    removeFromQueue(index) {
        this.library.removeFromQueue(index);
        this.saveLibrary();
    }

    /**
     * Clear the playback queue
     */
    clearQueue() {
        this.library.clearQueue();
        this.saveLibrary();
    }

    /**
     * Get downloaded songs
     * @returns {Array} Array of downloaded songs
     */
    getDownloads() {
        return this.library.downloads;
    }

    /**
     * Add a song to downloads
     * @param {Song} song - The song to add to downloads
     */
    addToDownloads(song) {
        this.library.addToDownloads(song);
        this.saveLibrary();
    }

    /**
     * Remove a song from downloads
     * @param {string} songId - The ID of the song to remove from downloads
     */
    removeFromDownloads(songId) {
        this.library.removeFromDownloads(songId);
        this.saveLibrary();
    }

    /**
     * Check if a song is downloaded
     * @param {string} songId - The ID of the song to check
     * @returns {boolean} True if the song is downloaded
     */
    isDownloaded(songId) {
        return this.library.downloads.some(song => song.id === songId);
    }
}

// Create and export a singleton instance
const libraryAPI = new LibraryAPI();
export default libraryAPI;
