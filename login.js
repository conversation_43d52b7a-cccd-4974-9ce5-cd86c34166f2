import { API, utils } from './api.js';

// Add demo users to localStorage for testing
function initializeDemoUsers() {
    // Check if we already have users in localStorage
    const existingUsers = localStorage.getItem('banshee_users');
    if (!existingUsers) {
        // Create demo users
        const demoUsers = [
            {
                id: 1,
                username: 'admin',
                password: 'password',
                name: 'Admin User',
                email: '<EMAIL>',
                role: 'admin'
            },
            {
                id: 2,
                username: 'user',
                password: 'password',
                name: 'Regular User',
                email: '<EMAIL>',
                role: 'user'
            },
            {
                id: 3,
                username: 'demo',
                password: 'demo123',
                name: 'Demo User',
                email: '<EMAIL>',
                role: 'user'
            }
        ];

        // Save to localStorage
        localStorage.setItem('banshee_users', JSON.stringify(demoUsers));
        console.log('Demo users initialized');
    }
}

document.addEventListener('DOMContentLoaded', () => {
    // Initialize demo users
    initializeDemoUsers();

    // Initialize the login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Check if user is already logged in
    if (utils.isAuthenticated()) {
        // Redirect to home page if already logged in
        window.location.href = 'index.html';
    }

    // Initialize CAPTCHA
    initCaptcha();

    // Show demo credentials
    showDemoCredentials();
});

/**
 * Initialize the CAPTCHA challenge
 */
function initCaptcha() {
    const captchaContainer = document.getElementById('captchaContainer');
    if (!captchaContainer) return;

    // Generate a simple math CAPTCHA
    const num1 = Math.floor(Math.random() * 10) + 1;
    const num2 = Math.floor(Math.random() * 10) + 1;
    const answer = num1 + num2;

    // Store the answer in a data attribute (in a real app, this would be server-side)
    captchaContainer.dataset.answer = answer.toString();

    // Display the CAPTCHA challenge
    const captchaQuestion = document.getElementById('captchaQuestion');
    if (captchaQuestion) {
        captchaQuestion.textContent = `What is ${num1} + ${num2}?`;
    }

    // Add refresh button functionality
    const refreshButton = document.getElementById('refreshCaptcha');
    if (refreshButton) {
        refreshButton.addEventListener('click', initCaptcha);
    }
}

/**
 * Validate the CAPTCHA response
 * @returns {boolean} True if the CAPTCHA is valid
 */
function validateCaptcha() {
    const captchaContainer = document.getElementById('captchaContainer');
    const captchaInput = document.getElementById('captchaAnswer');
    const captchaError = document.getElementById('captchaError');

    if (!captchaContainer || !captchaInput || !captchaError) return true;

    const expectedAnswer = captchaContainer.dataset.answer;
    const userAnswer = captchaInput.value.trim();

    if (!userAnswer) {
        captchaError.textContent = 'Please complete the CAPTCHA';
        captchaError.style.display = 'block';
        return false;
    }

    if (userAnswer !== expectedAnswer) {
        captchaError.textContent = 'Incorrect CAPTCHA answer';
        captchaError.style.display = 'block';
        initCaptcha(); // Generate a new CAPTCHA
        captchaInput.value = ''; // Clear the input
        return false;
    }

    captchaError.style.display = 'none';
    return true;
}

/**
 * Show demo credentials on the page
 */
function showDemoCredentials() {
    const loginContainer = document.querySelector('.login-container');
    if (!loginContainer) return;

    const demoBox = document.createElement('div');
    demoBox.className = 'demo-credentials';
    demoBox.innerHTML = `
        <h3>Demo Credentials</h3>
        <p><strong>Username:</strong> demo</p>
        <p><strong>Password:</strong> demo123</p>
        <p class="demo-note">Click to autofill</p>
    `;

    // Add click event to autofill credentials
    demoBox.addEventListener('click', () => {
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');

        if (usernameInput) usernameInput.value = 'demo';
        if (passwordInput) passwordInput.value = 'demo123';
    });

    // Insert before the form
    loginContainer.insertBefore(demoBox, document.getElementById('loginForm'));
}

/**
 * Handle login form submission
 * @param {Event} event - The form submission event
 */
async function handleLogin(event) {
    event.preventDefault();

    // Get form values
    const username = event.target.username.value;
    const password = event.target.password.value;
    const rememberMe = event.target.remember.checked;

    // Validate CAPTCHA
    if (!validateCaptcha()) {
        return;
    }

    // Show loading indicator
    utils.showLoader();

    try {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('banshee_users') || '[]');

        // Find user with matching credentials
        const user = users.find(u => u.username === username && u.password === password);

        if (user) {
            // Generate a token
            const token = 'mock-jwt-token-' + Math.random().toString(36).substring(2);

            // Store token
            if (rememberMe) {
                localStorage.setItem('token', token);
            } else {
                sessionStorage.setItem('token', token);
            }

            // Store user data
            if (rememberMe) {
                localStorage.setItem('user', JSON.stringify(user));
            } else {
                sessionStorage.setItem('user', JSON.stringify(user));
            }

            // Show success message
            showMessage(`Welcome back, ${user.name}! Redirecting...`, false);

            // Redirect to home page
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        } else {
            // Show error message
            showMessage('Invalid username or password. Please try again.', true);

            // Generate a new CAPTCHA
            initCaptcha();
        }
    } catch (error) {
        console.error('Login error:', error);
        showMessage('An error occurred. Please try again later.', true);
        initCaptcha();
    } finally {
        utils.hideLoader();
    }
}

function showMessage(message, isError = false) {
    const messageElement = document.getElementById('message');
    messageElement.textContent = message;
    messageElement.className = `message ${isError ? 'error' : 'success'}`;
    messageElement.style.display = 'block';
    setTimeout(() => {
        messageElement.style.display = 'none';
    }, 3000);
}
