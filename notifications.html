<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="notifications.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <a href="subscription.html" aria-label="Go to subscription page">
                    <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
                </a>
            </div>
            <ul class="menu" role="navigation" aria-label="Main Navigation">
                <li role="none"><a href="index.html" role="menuitem">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button class="profile-button" aria-expanded="false" aria-controls="dropdown-menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile" loading="lazy">
                </button>
                <div class="dropdown">
                    <ul>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notifications.html" aria-current="page">Notifications</a></li>
                        <li><a href="#">Logout</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="container">
        <h1>Notifications</h1>
        <div class="notifications-header">
            <button id="markAllReadBtn" class="mark-all-btn">Mark All as Read</button>
            <button id="clearAllBtn" class="clear-all-btn">Clear All</button>
        </div>
        <div class="notifications-container">
            <!-- Notifications will be dynamically inserted here -->
        </div>
    </main>

    <footer>
        <p>&copy; 2023 Banshee Music App. All rights reserved.</p>
    </footer>

    <script src="notifications.js"></script>
</body>
</html>
