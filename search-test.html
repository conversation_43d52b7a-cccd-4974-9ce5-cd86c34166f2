<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Test - Banshee Music App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #0D1117;
            color: white;
        }
        .search-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #161B22;
        }
        input {
            width: 70%;
            padding: 10px;
            margin: 10px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #0D1117;
            color: white;
        }
        button {
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .result {
            background-color: #0D1117;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 3px solid #00E0FF;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #FF006E;
            background-color: #2D1B1B;
        }
        .song-card {
            background: rgba(25, 32, 44, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .song-card img {
            width: 60px;
            height: 60px;
            border-radius: 4px;
            object-fit: cover;
        }
        .song-info {
            flex: 1;
        }
        .song-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .song-artist {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }
        .play-btn {
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <h1>🎵 Music Search Test</h1>
    
    <div class="search-container">
        <h2>🔍 Search Test</h2>
        <input type="text" id="search-input" placeholder="Type artist name (e.g., 'the weeknd')" value="the weeknd">
        <button onclick="testSearch()">Search</button>
        <button onclick="testAPI()">Test API Direct</button>
        <div id="search-result" class="result"></div>
    </div>

    <div class="search-container">
        <h2>🎨 Search Results</h2>
        <div id="songs-container"></div>
    </div>

    <script type="module">
        // Test if modules work
        console.log('Script loaded as module');
        
        // Test basic fetch
        window.testAPI = async function() {
            const resultDiv = document.getElementById('search-result');
            try {
                resultDiv.textContent = 'Testing API connection...';
                
                const response = await fetch('http://localhost:3001/api/library/search?q=the%20weeknd');
                console.log('Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('API Response:', data);
                
                resultDiv.textContent = JSON.stringify(data, null, 2);
                resultDiv.className = 'result';
                
                if (data.songs && data.songs.length > 0) {
                    displaySongs(data.songs);
                }
                
            } catch (error) {
                console.error('API Test Error:', error);
                resultDiv.textContent = `API Error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        };

        // Test search with library API
        window.testSearch = async function() {
            const searchInput = document.getElementById('search-input');
            const resultDiv = document.getElementById('search-result');
            const query = searchInput.value.trim();
            
            if (!query) {
                resultDiv.textContent = 'Please enter a search term';
                resultDiv.className = 'result error';
                return;
            }
            
            try {
                resultDiv.textContent = `Searching for "${query}"...`;
                
                // Try to import the library API
                const { default: libraryAPI } = await import('./js/library/api-backend.js');
                console.log('Library API imported:', libraryAPI);
                
                const results = await libraryAPI.searchMusic(query, 10);
                console.log('Search results:', results);
                
                resultDiv.textContent = `Found ${results.length} results:\n\n${JSON.stringify(results, null, 2)}`;
                resultDiv.className = 'result';
                
                if (results.length > 0) {
                    displaySongs(results);
                }
                
            } catch (error) {
                console.error('Search Error:', error);
                resultDiv.textContent = `Search Error: ${error.message}\n\nStack: ${error.stack}`;
                resultDiv.className = 'result error';
            }
        };

        function displaySongs(songs) {
            const container = document.getElementById('songs-container');
            container.innerHTML = '';
            
            songs.forEach(song => {
                const songCard = document.createElement('div');
                songCard.className = 'song-card';
                songCard.innerHTML = `
                    <img src="${song.coverUrl || 'imgs/album-01.png'}" alt="${song.title}">
                    <div class="song-info">
                        <div class="song-title">${song.title}</div>
                        <div class="song-artist">${song.artist} • ${song.album}</div>
                    </div>
                    <button class="play-btn" onclick="playPreview('${song.audioUrl || song.previewUrl || ''}', '${song.title}')">
                        <i class="fas fa-play"></i> ▶
                    </button>
                `;
                container.appendChild(songCard);
            });
        }

        window.playPreview = function(url, title) {
            if (!url) {
                alert('No preview available for this song');
                return;
            }
            
            console.log(`Playing preview: ${title}`);
            console.log(`Preview URL: ${url}`);
            
            // Create audio element and play
            const audio = new Audio(url);
            audio.play().then(() => {
                console.log('Audio playing successfully');
                alert(`Playing 30-second preview of: ${title}`);
            }).catch(error => {
                console.error('Audio play error:', error);
                alert(`Failed to play preview: ${error.message}`);
            });
        };

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('Page loaded, testing API...');
            // Uncomment to auto-test
            // setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
