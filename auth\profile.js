/**
 * Profile page functionality for Banshee Music App
 */

// Check if user is logged in
function checkAuth() {
    const currentUser = localStorage.getItem('currentUser');
    const authToken = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
    
    if (!currentUser || !authToken) {
        // Redirect to login page
        window.location.href = 'login.html';
    }
}

// Load user profile data
function loadUserProfile() {
    const currentUserData = JSON.parse(localStorage.getItem('currentUser') || '{}');
    const users = JSON.parse(localStorage.getItem('users') || '[]');
    const user = users.find(u => u.id === currentUserData.id);
    
    if (!user) {
        console.error('User not found');
        return;
    }
    
    // Update profile header
    const profileName = document.getElementById('profile-name');
    const profileEmail = document.getElementById('profile-email');
    
    if (profileName) profileName.textContent = user.name;
    if (profileEmail) profileEmail.textContent = user.email;
    
    // Update account form
    const nameInput = document.getElementById('profile-name-input');
    const emailInput = document.getElementById('profile-email-input');
    const usernameInput = document.getElementById('profile-username');
    const bioInput = document.getElementById('profile-bio');
    
    if (nameInput) nameInput.value = user.name;
    if (emailInput) emailInput.value = user.email;
    if (usernameInput) usernameInput.value = user.username || '';
    if (bioInput) bioInput.value = user.bio || '';
    
    // Update current session time
    const currentTime = document.getElementById('current-time');
    if (currentTime && currentUserData.loginTime) {
        const loginDate = new Date(currentUserData.loginTime);
        currentTime.textContent = `Active since ${loginDate.toLocaleString()}`;
    }
}

// Handle tab navigation
function setupTabNavigation() {
    const navLinks = document.querySelectorAll('.profile-nav a');
    const sections = document.querySelectorAll('.profile-section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            
            // Hide all sections
            sections.forEach(section => {
                section.classList.remove('active');
            });
            
            // Show target section
            const targetSection = document.getElementById(targetId);
            if (targetSection) targetSection.classList.add('active');
            
            // Update active link
            navLinks.forEach(link => {
                link.classList.remove('active');
            });
            this.classList.add('active');
        });
    });
}

// Handle account form submission
function setupAccountForm() {
    const accountForm = document.getElementById('account-form');
    const accountError = document.getElementById('account-error');
    const accountSuccess = document.getElementById('account-success');
    
    if (accountForm) {
        accountForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('profile-name-input').value;
            const email = document.getElementById('profile-email-input').value;
            const username = document.getElementById('profile-username').value;
            const bio = document.getElementById('profile-bio').value;
            
            // Clear previous messages
            if (accountError) accountError.style.display = 'none';
            if (accountSuccess) accountSuccess.style.display = 'none';
            
            // Validate form
            if (!name || !email) {
                if (accountError) {
                    accountError.textContent = 'Name and email are required';
                    accountError.style.display = 'block';
                }
                return;
            }
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Saving...';
            submitButton.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                try {
                    // Get current user
                    const currentUserData = JSON.parse(localStorage.getItem('currentUser') || '{}');
                    const users = JSON.parse(localStorage.getItem('users') || '[]');
                    const userIndex = users.findIndex(u => u.id === currentUserData.id);
                    
                    if (userIndex === -1) {
                        throw new Error('User not found');
                    }
                    
                    // Check if email is already taken by another user
                    const emailExists = users.some(u => u.email === email && u.id !== currentUserData.id);
                    if (emailExists) {
                        throw new Error('Email is already taken by another user');
                    }
                    
                    // Update user data
                    users[userIndex].name = name;
                    users[userIndex].email = email;
                    users[userIndex].username = username;
                    users[userIndex].bio = bio;
                    
                    // Save to localStorage
                    localStorage.setItem('users', JSON.stringify(users));
                    
                    // Update current user data
                    currentUserData.name = name;
                    currentUserData.email = email;
                    localStorage.setItem('currentUser', JSON.stringify(currentUserData));
                    
                    // Update profile header
                    const profileName = document.getElementById('profile-name');
                    const profileEmail = document.getElementById('profile-email');
                    
                    if (profileName) profileName.textContent = name;
                    if (profileEmail) profileEmail.textContent = email;
                    
                    // Show success message
                    if (accountSuccess) {
                        accountSuccess.textContent = 'Profile updated successfully';
                        accountSuccess.style.display = 'block';
                    }
                } catch (error) {
                    // Show error message
                    if (accountError) {
                        accountError.textContent = error.message || 'An error occurred';
                        accountError.style.display = 'block';
                    }
                } finally {
                    // Reset button state
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }
            }, 1500);
        });
    }
}

// Handle password form submission
function setupPasswordForm() {
    const passwordForm = document.getElementById('password-form');
    const securityError = document.getElementById('security-error');
    const securitySuccess = document.getElementById('security-success');
    
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmNewPassword = document.getElementById('confirm-new-password').value;
            
            // Clear previous messages
            if (securityError) securityError.style.display = 'none';
            if (securitySuccess) securitySuccess.style.display = 'none';
            
            // Validate form
            if (!currentPassword || !newPassword || !confirmNewPassword) {
                if (securityError) {
                    securityError.textContent = 'All fields are required';
                    securityError.style.display = 'block';
                }
                return;
            }
            
            if (newPassword !== confirmNewPassword) {
                if (securityError) {
                    securityError.textContent = 'New passwords do not match';
                    securityError.style.display = 'block';
                }
                return;
            }
            
            // Validate password strength
            let strength = 0;
            if (newPassword.length >= 8) strength++;
            if (newPassword.match(/[a-z]/) && newPassword.match(/[A-Z]/)) strength++;
            if (newPassword.match(/\d/)) strength++;
            if (newPassword.match(/[^a-zA-Z\d]/)) strength++;
            
            if (strength < 3) {
                if (securityError) {
                    securityError.textContent = 'Password is too weak. It should be at least 8 characters long and include uppercase, lowercase, numbers, and special characters.';
                    securityError.style.display = 'block';
                }
                return;
            }
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Updating...';
            submitButton.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                try {
                    // Get current user
                    const currentUserData = JSON.parse(localStorage.getItem('currentUser') || '{}');
                    const users = JSON.parse(localStorage.getItem('users') || '[]');
                    const userIndex = users.findIndex(u => u.id === currentUserData.id);
                    
                    if (userIndex === -1) {
                        throw new Error('User not found');
                    }
                    
                    // Verify current password
                    if (users[userIndex].password !== currentPassword) {
                        throw new Error('Current password is incorrect');
                    }
                    
                    // Update password
                    users[userIndex].password = newPassword;
                    
                    // Save to localStorage
                    localStorage.setItem('users', JSON.stringify(users));
                    
                    // Show success message
                    if (securitySuccess) {
                        securitySuccess.textContent = 'Password updated successfully';
                        securitySuccess.style.display = 'block';
                    }
                    
                    // Reset form
                    passwordForm.reset();
                } catch (error) {
                    // Show error message
                    if (securityError) {
                        securityError.textContent = error.message || 'An error occurred';
                        securityError.style.display = 'block';
                    }
                } finally {
                    // Reset button state
                    submitButton.textContent = originalText;
                    submitButton.disabled = false;
                }
            }, 1500);
        });
    }
}

// Handle notifications form submission
function setupNotificationsForm() {
    const notificationsForm = document.getElementById('notifications-form');
    const notificationsSuccess = document.getElementById('notifications-success');
    
    if (notificationsForm) {
        notificationsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Saving...';
            submitButton.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                // Show success message
                if (notificationsSuccess) {
                    notificationsSuccess.textContent = 'Notification preferences saved successfully';
                    notificationsSuccess.style.display = 'block';
                }
                
                // Reset button state
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }, 1500);
        });
    }
}

// Handle privacy form submission
function setupPrivacyForm() {
    const privacyForm = document.getElementById('privacy-form');
    const privacySuccess = document.getElementById('privacy-success');
    
    if (privacyForm) {
        privacyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;
            submitButton.textContent = 'Saving...';
            submitButton.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                // Show success message
                if (privacySuccess) {
                    privacySuccess.textContent = 'Privacy settings saved successfully';
                    privacySuccess.style.display = 'block';
                }
                
                // Reset button state
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }, 1500);
        });
    }
}

// Handle download data button
function setupDownloadDataButton() {
    const downloadDataBtn = document.getElementById('download-data-btn');
    
    if (downloadDataBtn) {
        downloadDataBtn.addEventListener('click', function() {
            // Show loading state
            const originalText = this.textContent;
            this.textContent = 'Preparing...';
            this.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                try {
                    // Get current user data
                    const currentUserData = JSON.parse(localStorage.getItem('currentUser') || '{}');
                    const users = JSON.parse(localStorage.getItem('users') || '[]');
                    const user = users.find(u => u.id === currentUserData.id);
                    
                    if (!user) {
                        throw new Error('User not found');
                    }
                    
                    // Create data object
                    const userData = {
                        profile: {
                            id: user.id,
                            name: user.name,
                            email: user.email,
                            username: user.username,
                            bio: user.bio,
                            createdAt: user.createdAt
                        },
                        // In a real app, this would include playlists, favorites, etc.
                        playlists: [],
                        favorites: [],
                        recentlyPlayed: []
                    };
                    
                    // Convert to JSON
                    const dataStr = JSON.stringify(userData, null, 2);
                    
                    // Create download link
                    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
                    const exportName = 'banshee-music-data.json';
                    
                    const linkElement = document.createElement('a');
                    linkElement.setAttribute('href', dataUri);
                    linkElement.setAttribute('download', exportName);
                    linkElement.click();
                } catch (error) {
                    alert('Error downloading data: ' + (error.message || 'An error occurred'));
                } finally {
                    // Reset button state
                    this.textContent = originalText;
                    this.disabled = false;
                }
            }, 1500);
        });
    }
}

// Handle delete account button and modal
function setupDeleteAccountModal() {
    const deleteAccountBtn = document.getElementById('delete-account-btn');
    const deleteAccountModal = document.getElementById('delete-account-modal');
    const deleteAccountForm = document.getElementById('delete-account-form');
    const closeModalButtons = document.querySelectorAll('.close-modal, .cancel-btn');
    
    if (deleteAccountBtn && deleteAccountModal) {
        // Open modal
        deleteAccountBtn.addEventListener('click', function() {
            deleteAccountModal.style.display = 'flex';
        });
        
        // Close modal
        closeModalButtons.forEach(button => {
            button.addEventListener('click', function() {
                deleteAccountModal.style.display = 'none';
            });
        });
        
        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            if (e.target === deleteAccountModal) {
                deleteAccountModal.style.display = 'none';
            }
        });
        
        // Handle form submission
        if (deleteAccountForm) {
            deleteAccountForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const password = document.getElementById('delete-password').value;
                const confirmed = document.getElementById('delete-confirm').checked;
                
                if (!password || !confirmed) {
                    alert('Please enter your password and confirm the deletion');
                    return;
                }
                
                // Show loading state
                const submitButton = this.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Deleting...';
                submitButton.disabled = true;
                
                // Simulate API call
                setTimeout(() => {
                    try {
                        // Get current user
                        const currentUserData = JSON.parse(localStorage.getItem('currentUser') || '{}');
                        const users = JSON.parse(localStorage.getItem('users') || '[]');
                        const userIndex = users.findIndex(u => u.id === currentUserData.id);
                        
                        if (userIndex === -1) {
                            throw new Error('User not found');
                        }
                        
                        // Verify password
                        if (users[userIndex].password !== password) {
                            throw new Error('Password is incorrect');
                        }
                        
                        // Remove user
                        users.splice(userIndex, 1);
                        
                        // Save to localStorage
                        localStorage.setItem('users', JSON.stringify(users));
                        
                        // Clear current user and auth token
                        localStorage.removeItem('currentUser');
                        localStorage.removeItem('authToken');
                        sessionStorage.removeItem('authToken');
                        
                        // Show success message and redirect
                        alert('Your account has been deleted successfully');
                        window.location.href = 'login.html';
                    } catch (error) {
                        // Show error message
                        alert('Error: ' + (error.message || 'An error occurred'));
                        
                        // Reset button state
                        submitButton.textContent = originalText;
                        submitButton.disabled = false;
                    }
                }, 1500);
            });
        }
    }
}

// Handle logout
function setupLogout() {
    const logoutLinks = document.querySelectorAll('.logout');
    
    logoutLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Clear current user and auth token
            localStorage.removeItem('currentUser');
            localStorage.removeItem('authToken');
            sessionStorage.removeItem('authToken');
            
            // Redirect to login page
            window.location.href = 'login.html';
        });
    });
}

// Initialize profile page
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    checkAuth();
    
    // Load user profile data
    loadUserProfile();
    
    // Setup tab navigation
    setupTabNavigation();
    
    // Setup forms
    setupAccountForm();
    setupPasswordForm();
    setupNotificationsForm();
    setupPrivacyForm();
    
    // Setup buttons
    setupDownloadDataButton();
    setupDeleteAccountModal();
    setupLogout();
    
    // Toggle password visibility
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            const icon = this.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
    
    // Password strength meter
    const passwordInputs = document.querySelectorAll('input[type="password"][id="new-password"]');
    passwordInputs.forEach(input => {
        input.addEventListener('input', function() {
            const password = this.value;
            const strengthMeter = this.parentElement.parentElement.querySelector('.password-strength');
            
            if (!strengthMeter) return;
            
            const segments = strengthMeter.querySelectorAll('.strength-segment');
            const strengthText = strengthMeter.querySelector('.strength-text');
            
            // Reset segments
            segments.forEach(segment => {
                segment.className = 'strength-segment';
            });
            
            // Calculate password strength
            let strength = 0;
            
            if (password.length >= 8) strength++;
            if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength++;
            if (password.match(/\d/)) strength++;
            if (password.match(/[^a-zA-Z\d]/)) strength++;
            
            // Update segments based on strength
            for (let i = 0; i < strength; i++) {
                if (segments[i]) {
                    if (strength === 1) segments[i].classList.add('weak');
                    else if (strength === 2) segments[i].classList.add('medium');
                    else if (strength === 3) segments[i].classList.add('strong');
                    else if (strength === 4) segments[i].classList.add('very-strong');
                }
            }
            
            // Update strength text
            if (strengthText) {
                if (strength === 0) strengthText.textContent = 'Password strength';
                else if (strength === 1) strengthText.textContent = 'Weak';
                else if (strength === 2) strengthText.textContent = 'Medium';
                else if (strength === 3) strengthText.textContent = 'Strong';
                else if (strength === 4) strengthText.textContent = 'Very Strong';
            }
        });
    });
});
