/**
 * Explore Page JavaScript
 * Organized and cleaned up for better maintainability
 */

// Import the library API
import libraryAPI from './js/library/api-backend.js';
import { Song } from './js/library/models.js';

// Global variables
let currentSearchResults = [];
let currentPlaylists = [];

document.addEventListener('DOMContentLoaded', () => {
    // Initialize components
    setupLazyLoading();
    initializeEventListeners();
    initializeCarousels();
    loadExploreContent();

    // Add animation class to navbar for current page
    const currentPageLink = document.querySelector('.menu a[aria-current="page"]');
    if (currentPageLink) {
        currentPageLink.parentElement.classList.add('active');
    }
});

/**
 * Setup enhanced lazy loading for images
 */
function setupLazyLoading() {
    // First, handle all carousel images with higher priority
    const carouselImages = document.querySelectorAll('.carousel img, .img-container img');

    if ('loading' in HTMLImageElement.prototype) {
        // <PERSON><PERSON><PERSON> supports native lazy loading
        carouselImages.forEach(img => {
            // Make sure all carousel images have loading="lazy" attribute
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }

            // Add blur-up effect
            img.style.filter = 'blur(5px)';
            img.style.transition = 'filter 0.5s ease-out';

            img.onload = function() {
                img.style.filter = 'blur(0)';
            };
        });

        // Handle all other images
        const otherImages = document.querySelectorAll('img:not(.carousel img):not(.img-container img)');
        otherImages.forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
        });
    } else {
        // Fallback for browsers that don't support lazy loading
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/lazysizes/5.3.2/lazysizes.min.js';
        document.body.appendChild(script);

        // Process all images
        const allImages = document.querySelectorAll('img[loading="lazy"]');
        allImages.forEach(img => {
            img.classList.add('lazyload');

            // Store original source
            const originalSrc = img.src;
            img.setAttribute('data-src', originalSrc);

            // Set placeholder
            img.src = 'data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==';

            // Add blur-up effect for carousel images
            if (img.closest('.carousel') || img.closest('.img-container')) {
                img.style.filter = 'blur(5px)';
                img.style.transition = 'filter 0.5s ease-out';

                img.addEventListener('lazyloaded', function() {
                    img.style.filter = 'blur(0)';
                });
            }
        });
    }

    // Add intersection observer for better performance
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src && !img.src.includes(img.dataset.src)) {
                        img.src = img.dataset.src;
                    }
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: '50px 0px', // Start loading images when they're 50px from viewport
            threshold: 0.01 // Trigger when at least 1% of the image is visible
        });

        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            const filter = button.getAttribute('data-filter');

            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // Filter items
            filterItems(filter);
        });
    });

    // Search functionality
    const searchBar = document.querySelector('.search-bar');
    const searchClear = document.querySelector('.search-clear');
    const searchButton = document.querySelector('.search-button');

    if (searchBar) {
        searchBar.addEventListener('input', () => {
            const searchTerm = searchBar.value.toLowerCase();
            if (searchTerm.length > 2) {
                searchItems(searchTerm);
            } else if (searchTerm.length === 0) {
                resetSearch();
            }
        });

        searchBar.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const searchTerm = searchBar.value.toLowerCase();
                searchItems(searchTerm);
            }
        });
    }

    if (searchClear) {
        searchClear.addEventListener('click', () => {
            searchBar.value = '';
            resetSearch();
        });
    }

    if (searchButton) {
        searchButton.addEventListener('click', () => {
            const searchTerm = searchBar.value.toLowerCase();
            searchItems(searchTerm);
        });
    }

    // Play buttons
    const playButtons = document.querySelectorAll('.play-button');
    playButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            const itemName = button.getAttribute('aria-label')?.replace('Play ', '') || 'Unknown';
            console.log(`Playing: ${itemName}`);
            // Here you would trigger the actual playback
        });
    });

    // Follow buttons
    const followButtons = document.querySelectorAll('.follow-btn');
    followButtons.forEach(button => {
        button.addEventListener('click', function() {
            const isFollowing = this.classList.contains('following');
            const artistName = this.closest('.artist-card').querySelector('.artist-name').textContent;

            if (isFollowing) {
                this.textContent = 'Follow';
                this.classList.remove('following');
                console.log(`Unfollowed ${artistName}`);
            } else {
                this.textContent = 'Following';
                this.classList.add('following');
                console.log(`Followed ${artistName}`);
            }
        });
    });
}

/**
 * Initialize Flickity carousels
 */
function initializeCarousels() {
    const carousels = document.querySelectorAll('.carousel');

    carousels.forEach(carousel => {
        // Flickity is initialized via data-flickity attribute in HTML
        // This is just to ensure all carousels are properly set up
        if (carousel.classList.contains('flickity-enabled')) {
            // Get Flickity instance
            const flkty = Flickity.data(carousel);
            if (flkty) {
                // Force resize to ensure proper layout
                setTimeout(() => {
                    flkty.resize();
                }, 100);
            }
            return;
        }

        // For carousels that aren't initialized yet
        const options = {
            wrapAround: true,
            pageDots: true,
            prevNextButtons: true,
            autoPlay: 5000,
            pauseAutoPlayOnHover: true,
            lazyLoad: 2
        };

        // Initialize Flickity
        const flkty = new Flickity(carousel, options);

        // Force resize after a short delay
        setTimeout(() => {
            flkty.resize();
        }, 100);
    });

    // Add resize event listener to handle window resizing
    window.addEventListener('resize', function() {
        carousels.forEach(carousel => {
            const flkty = Flickity.data(carousel);
            if (flkty) {
                flkty.resize();
            }
        });
    });
}

/**
 * Filter items based on category
 * @param {string} filter - The filter category
 */
function filterItems(filter) {
    const items = document.querySelectorAll('.card');

    items.forEach(item => {
        if (filter === 'all' || item.getAttribute('data-type') === filter) {
            item.style.display = 'flex';
        } else {
            item.style.display = 'none';
        }
    });
}

/**
 * Search items based on search term using real API
 * @param {string} searchTerm - The search term
 */
async function searchItems(searchTerm) {
    if (!searchTerm || searchTerm.length < 2) return;

    showLoader();

    try {
        // Search using the iTunes API through our backend
        const searchResults = await libraryAPI.searchMusic(searchTerm, 20);
        currentSearchResults = searchResults;

        // Clear existing content and show search results
        await displaySearchResults(searchResults, searchTerm);

        // Update search results count
        const resultsCount = document.querySelector('.results-count');
        if (resultsCount) {
            resultsCount.textContent = `${searchResults.length} results found for "${searchTerm}"`;
        }

    } catch (error) {
        console.error('Search failed:', error);
        const resultsCount = document.querySelector('.results-count');
        if (resultsCount) {
            resultsCount.textContent = 'Search failed. Please try again.';
        }
    } finally {
        hideLoader();
    }
}

/**
 * Display search results in the page
 * @param {Array} results - Array of search results
 * @param {string} searchTerm - The search term
 */
async function displaySearchResults(results, searchTerm) {
    // Get or create search results section
    let searchResultsSection = document.querySelector('.search-results-section');

    if (!searchResultsSection) {
        searchResultsSection = document.createElement('section');
        searchResultsSection.className = 'section search-results-section';

        // Insert after the search section
        const searchSection = document.querySelector('.search-section');
        searchSection.parentNode.insertBefore(searchResultsSection, searchSection.nextSibling);
    }

    // Hide other sections during search
    const otherSections = document.querySelectorAll('.featured-artists, .recommended-music');
    otherSections.forEach(section => {
        section.style.display = 'none';
    });

    // Create search results content
    searchResultsSection.innerHTML = `
        <div class="section-header">
            <h2>Search Results</h2>
            <button type="button" class="clear-search-btn">Clear Search</button>
        </div>
        <div class="search-results-grid" id="search-results-grid">
            ${results.length === 0 ? '<p class="no-results">No results found. Try a different search term.</p>' : ''}
        </div>
    `;

    // Add clear search functionality
    const clearSearchBtn = searchResultsSection.querySelector('.clear-search-btn');
    clearSearchBtn.addEventListener('click', resetSearch);

    if (results.length > 0) {
        const grid = searchResultsSection.querySelector('.search-results-grid');

        // Load user's playlists for the "Add to Playlist" functionality
        currentPlaylists = await libraryAPI.getPlaylists();

        results.forEach(song => {
            const songCard = createSongCard(song);
            grid.appendChild(songCard);
        });
    }
}

/**
 * Create a song card element
 * @param {Object} song - Song data
 * @returns {HTMLElement} Song card element
 */
function createSongCard(song) {
    const card = document.createElement('div');
    card.className = 'card song-card';
    card.dataset.songId = song.id;

    card.innerHTML = `
        <div class="img-container">
            <img src="${song.coverUrl}"
                 alt="${song.title}"
                 loading="lazy"
                 width="200"
                 height="200"
                 decoding="async">
            <div class="play-overlay">
                <button type="button" class="play-button" aria-label="Play ${song.title}">
                    <i class="fas fa-play"></i>
                </button>
            </div>
        </div>
        <div class="card-content">
            <div class="text-content">
                <h3>${song.title}</h3>
                <p>${song.artist} • ${song.album}</p>
                <p class="song-duration">${formatDuration(song.duration)}</p>
            </div>
            <div class="song-actions">
                <button type="button" class="button add-to-library-btn" data-song='${JSON.stringify(song)}'>
                    <i class="fas fa-plus"></i> Add to Library
                </button>
                <div class="dropdown-container">
                    <button type="button" class="button add-to-playlist-btn" data-song='${JSON.stringify(song)}'>
                        <i class="fas fa-list"></i> Add to Playlist
                    </button>
                    <div class="playlist-dropdown" style="display: none;">
                        ${currentPlaylists.map(playlist =>
                            `<button type="button" class="playlist-option" data-playlist-id="${playlist.id}" data-song='${JSON.stringify(song)}'>
                                ${playlist.name}
                            </button>`
                        ).join('')}
                    </div>
                </div>
                <button type="button" class="button favorite-btn" data-song='${JSON.stringify(song)}'>
                    <i class="fas fa-heart"></i> Favorite
                </button>
            </div>
        </div>
    `;

    // Add event listeners
    setupSongCardListeners(card, song);

    return card;
}

/**
 * Setup event listeners for song card
 * @param {HTMLElement} card - The song card element
 * @param {Object} song - Song data
 */
function setupSongCardListeners(card, song) {
    // Play button
    const playBtn = card.querySelector('.play-button');
    playBtn.addEventListener('click', () => {
        console.log(`Playing: ${song.title} by ${song.artist}`);
        // Here you would implement actual playback
        if (song.audioUrl) {
            // For now, just log the preview URL
            console.log(`Preview URL: ${song.audioUrl}`);
        }
    });

    // Add to library button
    const addToLibraryBtn = card.querySelector('.add-to-library-btn');
    addToLibraryBtn.addEventListener('click', async () => {
        try {
            await libraryAPI.addSongFromSearch(song);
            addToLibraryBtn.innerHTML = '<i class="fas fa-check"></i> Added';
            addToLibraryBtn.disabled = true;
            showToast(`Added "${song.title}" to your library`);
        } catch (error) {
            console.error('Failed to add song to library:', error);
            showToast('Failed to add song to library', 'error');
        }
    });

    // Add to playlist dropdown
    const addToPlaylistBtn = card.querySelector('.add-to-playlist-btn');
    const playlistDropdown = card.querySelector('.playlist-dropdown');

    addToPlaylistBtn.addEventListener('click', () => {
        playlistDropdown.style.display = playlistDropdown.style.display === 'none' ? 'block' : 'none';
    });

    // Playlist options
    const playlistOptions = card.querySelectorAll('.playlist-option');
    playlistOptions.forEach(option => {
        option.addEventListener('click', async () => {
            const playlistId = option.dataset.playlistId;
            const playlist = currentPlaylists.find(p => p.id === playlistId);

            try {
                await libraryAPI.addSongFromSearchToPlaylist(playlistId, song);
                playlistDropdown.style.display = 'none';
                showToast(`Added "${song.title}" to "${playlist.name}"`);
            } catch (error) {
                console.error('Failed to add song to playlist:', error);
                showToast('Failed to add song to playlist', 'error');
            }
        });
    });

    // Favorite button
    const favoriteBtn = card.querySelector('.favorite-btn');
    favoriteBtn.addEventListener('click', async () => {
        try {
            // First add to library, then to favorites
            await libraryAPI.addSongFromSearch(song);
            await libraryAPI.addToFavorites(song);
            favoriteBtn.innerHTML = '<i class="fas fa-heart" style="color: #ff006e;"></i> Favorited';
            favoriteBtn.disabled = true;
            showToast(`Added "${song.title}" to favorites`);
        } catch (error) {
            console.error('Failed to add song to favorites:', error);
            showToast('Failed to add song to favorites', 'error');
        }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
        if (!card.contains(e.target)) {
            playlistDropdown.style.display = 'none';
        }
    });
}

/**
 * Format duration from seconds to MM:SS
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration
 */
function formatDuration(seconds) {
    if (!seconds) return '0:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Show toast notification
 * @param {string} message - Message to show
 * @param {string} type - Type of toast (success or error)
 */
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? '#ff3b30' : '#00e0ff'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

/**
 * Reset search results
 */
function resetSearch() {
    // Clear search input
    const searchBar = document.querySelector('.search-bar');
    if (searchBar) {
        searchBar.value = '';
    }

    // Hide search results section
    const searchResultsSection = document.querySelector('.search-results-section');
    if (searchResultsSection) {
        searchResultsSection.remove();
    }

    // Show other sections again
    const otherSections = document.querySelectorAll('.featured-artists, .recommended-music');
    otherSections.forEach(section => {
        section.style.display = 'block';
    });

    // Reset results count
    const resultsCount = document.querySelector('.results-count');
    if (resultsCount) {
        resultsCount.textContent = '';
    }

    // Clear current search results
    currentSearchResults = [];
}

/**
 * Show loading spinner
 */
function showLoader() {
    const loader = document.getElementById('loader');
    if (loader) {
        loader.style.display = 'block';
    }
}

/**
 * Hide loading spinner
 */
function hideLoader() {
    const loader = document.getElementById('loader');
    if (loader) {
        loader.style.display = 'none';
    }
}

/**
 * Main function to load explore content
 */
async function loadExploreContent() {
    showLoader();

    try {
        // Simulate API calls with setTimeout
        setTimeout(() => {
            hideLoader();
        }, 500);
    } catch (error) {
        console.error('Error loading explore content:', error);
        hideLoader();
    }
}
