<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend API Test - Banshee Music</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #0D1117;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background-color: #161B22;
        }
        button {
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            opacity: 0.8;
        }
        .result {
            background-color: #0D1117;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 3px solid #00E0FF;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            border-left-color: #FF006E;
            background-color: #2D1B1B;
        }
        .success {
            border-left-color: #00FF88;
            background-color: #1B2D1B;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            background-color: #0D1117;
            color: white;
            border: 1px solid #333;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🎵 Banshee Music Backend API Test</h1>
    
    <div class="test-section">
        <h2>🔍 Health Check</h2>
        <button onclick="testHealthCheck()">Test Health Check</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📚 Library Operations</h2>
        <button onclick="testGetLibrary()">Get Library</button>
        <button onclick="testGetSongs()">Get Songs</button>
        <button onclick="testGetPlaylists()">Get Playlists</button>
        <div id="library-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🎵 Add Sample Song</h2>
        <input type="text" id="song-title" placeholder="Song Title" value="Test Song">
        <input type="text" id="song-artist" placeholder="Artist" value="Test Artist">
        <input type="number" id="song-duration" placeholder="Duration (seconds)" value="180">
        <button onclick="testAddSong()">Add Song</button>
        <div id="song-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📝 Create Playlist</h2>
        <input type="text" id="playlist-name" placeholder="Playlist Name" value="Test Playlist">
        <textarea id="playlist-description" placeholder="Description">A test playlist created from the API test page</textarea>
        <button onclick="testCreatePlaylist()">Create Playlist</button>
        <div id="playlist-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>❤️ Favorites</h2>
        <button onclick="testGetFavorites()">Get Favorites</button>
        <button onclick="testToggleFavorite()">Toggle Favorite (song_1)</button>
        <div id="favorites-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        const USER_ID = 'user_123';

        async function makeRequest(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    ...options
                });
                
                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function displayResult(elementId, result, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function testHealthCheck() {
            const result = await makeRequest('/health');
            displayResult('health-result', result, !result.success);
        }

        async function testGetLibrary() {
            const result = await makeRequest(`/library/${USER_ID}`);
            displayResult('library-result', result, !result.success);
        }

        async function testGetSongs() {
            const result = await makeRequest(`/library/${USER_ID}/songs`);
            displayResult('library-result', result, !result.success);
        }

        async function testGetPlaylists() {
            const result = await makeRequest(`/library/${USER_ID}/playlists`);
            displayResult('library-result', result, !result.success);
        }

        async function testAddSong() {
            const songData = {
                id: 'song_test_' + Date.now(),
                title: document.getElementById('song-title').value,
                artist: document.getElementById('song-artist').value,
                duration: parseInt(document.getElementById('song-duration').value),
                album: 'Test Album',
                coverUrl: 'imgs/album-01.png',
                genre: ['Test'],
                popularity: 50
            };

            const result = await makeRequest(`/library/${USER_ID}/songs`, {
                method: 'POST',
                body: JSON.stringify(songData)
            });
            
            displayResult('song-result', result, !result.success);
        }

        async function testCreatePlaylist() {
            const playlistData = {
                id: 'playlist_test_' + Date.now(),
                name: document.getElementById('playlist-name').value,
                description: document.getElementById('playlist-description').value,
                coverUrl: 'imgs/album-01.png'
            };

            const result = await makeRequest(`/library/${USER_ID}/playlists`, {
                method: 'POST',
                body: JSON.stringify(playlistData)
            });
            
            displayResult('playlist-result', result, !result.success);
        }

        async function testGetFavorites() {
            const result = await makeRequest(`/library/${USER_ID}/favorites`);
            displayResult('favorites-result', result, !result.success);
        }

        async function testToggleFavorite() {
            const result = await makeRequest(`/library/${USER_ID}/songs/song_1/favorite`, {
                method: 'POST'
            });
            displayResult('favorites-result', result, !result.success);
        }

        // Auto-run health check on page load
        window.addEventListener('load', testHealthCheck);
    </script>
</body>
</html>
