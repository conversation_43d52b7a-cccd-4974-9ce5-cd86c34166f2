/* ===== CSS VARIABLES ===== */
:root {
    /* Colors */
    --background-color: #0D1117;
    --text-color: #ffffff;
    --electric-violet: #6F00FF;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    --accent-color: var(--cosmic-pink);

    /* Gradients */
    --button-gradient: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --header-gradient: linear-gradient(to right, #13151a, #1a1d24);
    --button-shine: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));

    /* Shadows */
    --button-hover-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --button-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    --card-shadow: 0 8px 32px rgba(56, 12, 97, 0.15);

    /* Spacing */
    --container-padding: 20px;
    --section-gap: 2rem;

    /* Border Radius */
    --border-radius-lg: 25px;
    --border-radius-md: 16px;
    --border-radius-sm: 12px;

    /* Transitions */
    --transition-speed: 0.3s;
}

/* ===== GLOBAL STYLES ===== */

/* Base Styles */
body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
    margin: 0;
    padding: 0 var(--container-padding);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Main content styles */

/* Button Styles moved to buttons.css */

/* Hero Section */

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    width: 100%;
    max-width: 800px;
    padding: 3rem;
    margin: 120px auto 0; /* Changed to auto margins for horizontal centering */
    background: rgba(13, 17, 23, 0.8);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex; /* Added for better content alignment */
    flex-direction: column;
    align-items: center;
}

/* Stars Animation - moved to animations.css */
.stars {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 2;
    opacity: 0.8;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #fff;
    border-radius: 50%;
    animation: twinkle var(--twinkle-duration) ease-in-out infinite;
    opacity: 0;
    box-shadow:
        0 0 8px #fff,
        0 0 12px #fff,
        0 0 20px var(--neon-blue);
}

/* Star Positions */
.star:nth-child(1) { --twinkle-duration: 3s; top: 10%; left: 10%; }
.star:nth-child(2) { --twinkle-duration: 4s; top: 20%; left: 25%; animation-delay: 0.3s; }
.star:nth-child(3) { --twinkle-duration: 3.5s; top: 15%; left: 60%; animation-delay: 0.7s; }
.star:nth-child(4) { --twinkle-duration: 4.5s; top: 35%; left: 85%; animation-delay: 1.1s; }
.star:nth-child(5) { --twinkle-duration: 3s; top: 45%; left: 15%; animation-delay: 1.4s; }
.star:nth-child(6) { --twinkle-duration: 4s; top: 55%; left: 35%; animation-delay: 1.8s; }
.star:nth-child(7) { --twinkle-duration: 3.5s; top: 65%; left: 75%; animation-delay: 2.1s; }
.star:nth-child(8) { --twinkle-duration: 4.5s; top: 75%; left: 25%; animation-delay: 2.5s; }
.star:nth-child(9) { --twinkle-duration: 3s; top: 85%; left: 85%; animation-delay: 2.8s; }
.star:nth-child(10) { --twinkle-duration: 4s; top: 90%; left: 50%; animation-delay: 3.2s; }

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(0, 224, 255, 0.3);
}

.hero-content p {
    font-size: clamp(1rem, 2vw, 1.2rem);
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

/* Media Queries for Hero Section */
@media screen and (max-width: 768px) {
    .hero {
        padding: 3rem 1rem;
    }

    .hero-content {
        padding: 2rem;
    }

    .star {
        transform: scale(0.8);
    }
}

@media screen and (max-width: 480px) {
    .hero {
        padding: 2rem 1rem;
    }

    .hero-content {
        padding: 1.5rem;
    }

    .star {
        transform: scale(0.6);
    }
}

/* Quick Access Section */
.quick-access {
    margin: -10px auto 0;
    text-align: center;
    max-width: 1200px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.quick-access h2,
.section h2,
.trending h2,
.recommendations h2 {
    font-size: clamp(2.2rem, 4vw, 2.8rem);  /* Increased from previous size */
    font-weight: 700;
    margin-bottom: 2.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    letter-spacing: 0.5px;
    text-shadow: 0 0 20px rgba(0, 224, 255, 0.3);
    position: relative;
    padding-bottom: 0.5rem;
}

.quick-access h2::after,
.section h2::after,
.trending h2::after,
.recommendations h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 180px; /* Increased from 80px */
    height: 2px; /* Slightly thinner */
    background: linear-gradient(
        90deg,
        transparent,
        rgba(0, 224, 255, 0.4) 20%,
        rgba(255, 0, 110, 0.4) 80%,
        transparent
    );
    border-radius: 2px;
    box-shadow:
        0 0 10px rgba(0, 224, 255, 0.2),
        0 0 20px rgba(255, 0, 110, 0.1);
}

.quick-access h2 {
    margin-bottom: 2.5rem;
}

.quick-links {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 2rem;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.quick-link {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 110px;
    max-width: 150px;
    aspect-ratio: 1;
    position: relative;
    overflow: hidden;
}

.quick-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        rgba(var(--neon-blue-rgb), 0.1),
        rgba(var(--cosmic-pink-rgb), 0.1)
    );
    opacity: 0;
    transition: opacity 0.3s ease;
}

.quick-link::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 70%
    );
    opacity: 0;
    transform: scale(0.5);
    transition: transform 0.6s ease, opacity 0.6s ease;
}

.quick-link:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(var(--neon-blue-rgb), 0.2),
        0 0 20px rgba(var(--cosmic-pink-rgb), 0.2);
}

.quick-link:hover::before {
    opacity: 1;
}

.quick-link:hover::after {
    opacity: 1;
    transform: scale(1);
}

.quick-link img {
    width: 45px;
    height: 45px;
    object-fit: contain;
    margin-bottom: 1rem;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.quick-link:hover img {
    transform: scale(1.1) translateY(-5px);
}

.quick-link span {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
    position: relative;
    z-index: 1;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.3s ease;
}

.quick-link:hover span {
    transform: translateY(-3px);
    color: var(--neon-blue);
    text-shadow: 0 0 8px rgba(var(--neon-blue-rgb), 0.3);
}

/* Animation moved to animations.css */
.quick-link:hover {
    animation: quickLinkPulse 2s infinite;
}

/* Add a smooth transition back to normal state */
.quick-link:active {
    transform: translateY(-4px);
    transition: transform 0.1s;
}

/* Media Queries for Quick Access */
@media screen and (max-width: 768px) {
    .quick-access {
        margin: -8px auto 0;
        padding: 1.5rem;
    }

    .quick-links {
        gap: 1.5rem;
    }

    .quick-link {
        min-width: 100px;
        max-width: 120px;
        padding: 1.25rem;
    }

    .quick-link img {
        width: 40px;
        height: 40px;
    }
}

@media screen and (max-width: 480px) {
    .quick-access {
        margin: -5px auto 0;
        padding: 1rem;
    }

    .quick-links {
        gap: 1rem;
        flex-direction: row;
        flex-wrap: wrap;
    }

    .quick-link {
        min-width: 90px;
        max-width: 100px;
        padding: 1rem;
        flex: 0 1 calc(33.333% - 1rem);
    }

    .quick-link img {
        width: 35px;
        height: 35px;
        margin-bottom: 0.75rem;
    }

    .quick-link span {
        font-size: 0.8rem;
    }
}

/* Card styles moved to card.css */

/* Section Headers */
.section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
}

/* Responsive Adjustments */
@media screen and (max-width: 1200px) {
    .cards {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media screen and (max-width: 900px) {
    .cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 600px) {
    .cards {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .card {
        padding: 1rem;
    }

    .section h2 {
        font-size: 1.5rem;
    }
}

/* Common button styles */
.button,
.play-button,
.section .view-all,
.playlist-controls button,
.hero-content .cta-button {
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    box-shadow: var(--button-shadow);
}

/* Common hover effects */
.button:hover,
.play-button:hover,
.section .view-all:hover,
.playlist-controls button:hover,
.hero-content .cta-button:hover {
    transform: translateY(-3px);
    box-shadow:
        0 8px 25px rgba(0, 224, 255, 0.3),
        0 8px 25px rgba(255, 0, 110, 0.3);
}

/* Common active effects */
.button:active,
.play-button:active,
.section .view-all:active,
.playlist-controls button:active,
.hero-content .cta-button:active {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Shine effect */
.button::before,
.play-button::before,
.section .view-all::before,
.playlist-controls button::before,
.hero-content .cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--button-shine);
    transition: 0.5s;
}

/* Shine animation on hover */
.button:hover::before,
.play-button:hover::before,
.section .view-all:hover::before,
.playlist-controls button:hover::before,
.hero-content .cta-button:hover::before {
    left: 100%;
}

/* Button size variations */
.play-button {
    padding: 12px 25px;
    font-size: 1rem;
}

.section .view-all {
    padding: 10px 20px;
    font-size: 0.9rem;
}

.playlist-controls button {
    padding: 10px 20px;
    font-size: 0.9rem;
    margin: 0 5px;
}

/* Star Animation */
.stars {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
    opacity: 0.6;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #fff;
    border-radius: 50%;
    animation: shooting-star 1.5s linear infinite;
    opacity: 0;
}

@keyframes shooting-star {
    0% {
        transform: translate(-100%, -100%);
        opacity: 1;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translate(200%, 200%);
        opacity: 0;
    }
}

/* Stagger star animations */
.star:nth-child(1) { animation-delay: 0s; }
.star:nth-child(2) { animation-delay: 0.15s; }
.star:nth-child(3) { animation-delay: 0.3s; }
.star:nth-child(4) { animation-delay: 0.45s; }
.star:nth-child(5) { animation-delay: 0.6s; }
.star:nth-child(6) { animation-delay: 0.75s; }
.star:nth-child(7) { animation-delay: 0.9s; }
.star:nth-child(8) { animation-delay: 1.05s; }
.star:nth-child(9) { animation-delay: 1.2s; }
.star:nth-child(10) { animation-delay: 1.35s; }

@media screen and (max-width: 768px) {
    body {
        padding: 0 15px;
    }

    header {
        padding: 10px 15px;
    }

    .menu {
        gap: 2rem;
    }

    .quick-links {
        gap: 1rem;
    }
    }

@media screen and (max-width: 480px) {
    body {
        padding: 0 10px;
    }

    .menu {
        gap: 1rem;
    }

    .quick-link {
        width: 80px;
    }
}

/* Button styles moved to buttons.css */

/* Navigation Menu Styles */
.menu {
    list-style: none;
    display: flex;
    letter-spacing: 1.2px;
    margin: 0;
    padding: 0;
    gap: 3rem;
}

.menu li {
    margin: 0;
}

.menu li a {
    color: #fff;
    text-decoration: none;
    font-size: 1.125rem;
    position: relative;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 8px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.menu li a:hover {
    color: var(--text-color);
    text-shadow:
        0 0 8px rgba(var(--neon-blue-rgb), 0.4),
        0 0 16px rgba(var(--cosmic-pink-rgb), 0.3),
        0 0 24px rgba(var(--neon-blue-rgb), 0.2);
    background: none;
    transform: translateY(-2px);
}

.menu li a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    width: 0;
    height: 3px;
    background: var(--button-gradient);
    transform: translateX(-50%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    border-radius: 2px;
}

.menu li a:hover::after {
    width: 100%;
    opacity: 0.8;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    box-shadow:
        0 0 12px rgba(var(--neon-blue-rgb), 0.6),
        0 0 20px rgba(var(--neon-blue-rgb), 0.4);
}

.menu li a[aria-current="page"]::after {
    width: 100%;
    opacity: 1;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 400% 100%;
    animation: colorLoop 3s ease-in-out infinite !important;
}

.menu li a[aria-current="page"] {
    color: var(--text-color) !important;
    text-shadow:
        0 0 8px rgba(var(--neon-blue-rgb), 0.6),
        0 0 16px rgba(var(--neon-blue-rgb), 0.3) !important;
    background: none !important;
    position: relative;
}

/* Enhanced animations for navbar indicators */
@keyframes navGlow {
    0% {
        box-shadow:
            0 0 15px rgba(var(--neon-blue-rgb), 0.4),
            0 0 25px rgba(var(--cosmic-pink-rgb), 0.3);
        transform: translateX(-50%) scaleX(1);
    }
    100% {
        box-shadow:
            0 0 20px rgba(var(--neon-blue-rgb), 0.6),
            0 0 35px rgba(var(--cosmic-pink-rgb), 0.5);
        transform: translateX(-50%) scaleX(1.05);
    }
}

@keyframes colorLoop {
    0% {
        background-position: 0% 50%;
        box-shadow:
            0 0 12px rgba(var(--neon-blue-rgb), 0.7),
            0 0 24px rgba(var(--neon-blue-rgb), 0.4);
        transform: translateX(-50%);
    }
    25% {
        background-position: 25% 50%;
        box-shadow:
            0 0 14px rgba(var(--cosmic-pink-rgb), 0.8),
            0 0 28px rgba(var(--cosmic-pink-rgb), 0.4);
        transform: translateX(-50%);
    }
    50% {
        background-position: 50% 50%;
        box-shadow:
            0 0 12px rgba(var(--neon-blue-rgb), 0.7),
            0 0 24px rgba(var(--neon-blue-rgb), 0.4);
        transform: translateX(-50%);
    }
    75% {
        background-position: 75% 50%;
        box-shadow:
            0 0 14px rgba(var(--cosmic-pink-rgb), 0.8),
            0 0 28px rgba(var(--cosmic-pink-rgb), 0.4);
        transform: translateX(-50%);
    }
    100% {
        background-position: 100% 50%;
        box-shadow:
            0 0 12px rgba(var(--neon-blue-rgb), 0.7),
            0 0 24px rgba(var(--neon-blue-rgb), 0.4);
        transform: translateX(-50%);
    }
}



/* Header Styles */
header {
    background: linear-gradient(
        to right,
        var(--header-gradient-start) 0%,
        var(--header-gradient-end) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
        z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

/* Logo Styles */
.logo img {
    width: 90px;
    height: 90px;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* User Profile Styles */
.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    cursor: pointer;
}

.user-profile img {
    width: 36px; /* Reduced from 40px */
    height: 36px; /* Reduced from 40px */
    border-radius: 50%;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    background: none !important;
    background-color: transparent !important;
}

.user-profile:hover img {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 16px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(var(--neon-blue-rgb), 0.1);
    min-width: 200px;
    z-index: 1000;
    margin-top: 12px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-15px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
}

.user-profile:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown ul {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0;
    margin: 0;
    padding: 12px 0;
}

.dropdown ul li a {
    color: var(--text-color);
    text-decoration: none;
    padding: 14px 24px;
    display: block;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 0;
    position: relative;
    letter-spacing: 0.3px;
}

.dropdown ul li a:hover {
    background: linear-gradient(90deg,
        rgba(var(--neon-blue-rgb), 0.15),
        rgba(var(--cosmic-pink-rgb), 0.1)
    );
    color: var(--text-color);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
    transform: translateX(4px);
    animation: dropdownBackgroundGlow 2s ease-in-out infinite alternate;
    box-shadow:
        inset 0 0 20px rgba(var(--neon-blue-rgb), 0.1),
        0 0 15px rgba(var(--neon-blue-rgb), 0.05);
}

@keyframes dropdownBackgroundGlow {
    0% {
        background: linear-gradient(90deg,
            rgba(var(--neon-blue-rgb), 0.15),
            rgba(var(--cosmic-pink-rgb), 0.1)
        );
        box-shadow:
            inset 0 0 20px rgba(var(--neon-blue-rgb), 0.1),
            0 0 15px rgba(var(--neon-blue-rgb), 0.05);
    }
    100% {
        background: linear-gradient(90deg,
            rgba(var(--neon-blue-rgb), 0.25),
            rgba(var(--cosmic-pink-rgb), 0.2)
        );
        box-shadow:
            inset 0 0 30px rgba(var(--neon-blue-rgb), 0.2),
            0 0 25px rgba(var(--neon-blue-rgb), 0.1);
    }
}

/* Container Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 120px;
    padding: 6rem 1rem;
    overflow: hidden;
    background: linear-gradient(to bottom, rgba(13, 17, 23, 0.8), rgba(13, 17, 23, 0.95));
    border-radius: var(--border-radius-lg);
}

/* Media queries for hero */
@media screen and (max-width: 768px) {
    .hero {
        padding: 3rem 1rem;
        min-height: 60vh;
    }
}

@media screen and (max-width: 480px) {
    .hero {
        padding: 2rem 1rem;
        min-height: 50vh;
    }
}

/* Hero Section */
.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    width: 100%;
    max-width: 800px;
    padding: 3rem;
    margin: 120px auto 0; /* Changed to auto margins for horizontal centering */
    background: rgba(13, 17, 23, 0.8);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex; /* Added for better content alignment */
    flex-direction: column;
    align-items: center;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .hero-content {
        margin: 100px auto 0;
        padding: 2rem;
        width: 90%; /* Added width control for smaller screens */
    }
}

@media screen and (max-width: 480px) {
    .hero-content {
        margin: 80px auto 0;
        padding: 1.5rem;
        width: 95%; /* Slightly wider on mobile */
    }
}

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.hero-content p {
    font-size: clamp(1.1rem, 2vw, 1.5rem);
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Hero button styles moved to buttons.css */

/* Quick Access Section */
.quick-access {
    margin: 0 auto; /* Reduced from 1rem to 0 to bring it closer to hero section */
    text-align: center;
    max-width: 1200px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.quick-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    padding: 1rem 0;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-color);
    transition: transform 0.3s;
    padding: 1rem;
    border-radius: var(--explore-card-radius);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 100px;
}

.quick-link img {
    width: 40px;
    height: 40px;
    margin-bottom: 0.5rem;
}

.quick-link span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Items Grid Layout */
.items {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* Cards Grid Layout moved to card.css */

.item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.08) 100%
    );
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.item:hover {
    transform: translateY(-8px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 10px 20px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(var(--neon-blue-rgb), 0.2);
}

.item img {
    width: 140px;
    height: 140px;
    object-fit: cover;
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.item:hover img {
    transform: scale(1.05);
}

.item h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0.5rem 0;
    color: var(--text-color);
    letter-spacing: 0.5px;
}

.item p {
    font-size: 0.9rem;
    color: var(--text-color-secondary);
    margin-bottom: 1rem;
    opacity: 0.8;
}

/* Button styles moved to buttons.css */

/* Star Animation */
.stars {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
    opacity: 0.6;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #fff;
    border-radius: 50%;
    animation: shooting-star 1.5s linear infinite;
    opacity: 0;
}

@keyframes shooting-star {
    0% {
        transform: translate(-100%, -100%);
        opacity: 1;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translate(200%, 200%);
        opacity: 0;
    }
}

/* Stagger star animations */
.star:nth-child(1) { animation-delay: 0s; }
.star:nth-child(2) { animation-delay: 0.15s; }
.star:nth-child(3) { animation-delay: 0.3s; }
.star:nth-child(4) { animation-delay: 0.45s; }
.star:nth-child(5) { animation-delay: 0.6s; }
.star:nth-child(6) { animation-delay: 0.75s; }
.star:nth-child(7) { animation-delay: 0.9s; }
.star:nth-child(8) { animation-delay: 1.05s; }
.star:nth-child(9) { animation-delay: 1.2s; }
.star:nth-child(10) { animation-delay: 1.35s; }

@media screen and (max-width: 768px) {
    body {
        padding: 0 15px;
    }

    header {
        padding: 10px 15px;
    }

    .menu {
        gap: 1.5rem;
    }

    .menu li a {
        font-size: 1rem;
        padding: 0.5rem 0.75rem;
    }

    .logo img {
        width: 70px;
        height: 70px;
    }

    .user-profile img {
        width: 32px;
        height: 32px;
    }

    .quick-links {
        gap: 1rem;
    }
}

@media screen and (max-width: 480px) {
    body {
        padding: 0 10px;
    }

    .menu {
        gap: 0.75rem;
    }

    .menu li a {
        font-size: 0.9rem;
        padding: 0.4rem 0.5rem;
        letter-spacing: 0.3px;
    }

    .logo img {
        width: 60px;
        height: 60px;
    }

    .user-profile img {
        width: 28px;
        height: 28px;
    }

    /* Reduce animation intensity on mobile for performance */
    .menu li a[aria-current="page"]::after {
        animation: activeNavGlow 4s ease-in-out infinite alternate;
    }

    .menu li a:hover::after {
        animation: navGlow 3s ease-in-out infinite alternate;
    }

    .quick-link {
        width: 80px;
    }
}

/* Recommendations Section */
.recommendations .items {
    display: flex;
    overflow-x: auto;
    padding-bottom: 20px;
}

.recommendations .item {
    flex: 0 0 auto;
    width: 150px;
    margin-right: 20px;
}

.recommendations .item img {
    width: 70%;
    height: 100px;
    object-fit: contain;
    border-radius: 5px;
}

.recommendations .item h3 {
    font-size: 14px;
    margin: 10px 0;
}

.recommendations .item button {
    width: 100%;
    padding: 6px;
    font-size: 12px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

/* Footer */
footer {
    background-color: #2c2c2e;
    color: #fff;
    text-align: center;
    padding: 20px;
    margin-top: 60px;
    position: relative;
    width: 100%;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
}

footer p {
    margin: 0;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 120px;  /* Positioned above music player */
    right: 30px;
    background: linear-gradient(90deg, var(--neon-blue) 0%, var(--cosmic-pink) 100%);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 9999;
}

.back-to-top:hover {
    transform: translateY(-3px);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

/* Search Section Styles */
.search-section {
    margin-top: 40px;
}

.search-container {
    max-width: 800px;
    margin: 0 auto 20px;
    padding: 0 20px;
}

.search-input-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    opacity: 0.7;
}

.search-bar {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.search-bar:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px var(--accent-color);
}

.search-bar::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.filters-container {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 30px;
}

.filter-select {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.filter-select:hover {
    background: rgba(255, 255, 255, 0.15);
}

.filter-select:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.filter-select option {
    background: var(--background-color);
    color: var(--text-color);
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 40px 0;
}

.nav-button {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.nav-button.active {
    background: var(--accent-color);
}

@media (max-width: 768px) {
    .search-container {
        padding: 0 15px;
    }

    .filters-container {
        flex-direction: column;
        padding: 0 15px;
    }

    .filter-select {
        width: 100%;
    }

    .pagination {
        flex-wrap: wrap;
    }
}

/* Responsive Design */
@media screen and (max-width: 1200px) {
    .section .item {
        min-width: 280px;
    }
}

@media screen and (max-width: 768px) {
    .section .items {
        grid-template-columns: 1fr;
    }

    .section .item {
        min-width: 250px;
    }

    .container {
        width: 95%;
        padding: 10px;
    }

    /* Mobile-specific improvements */
    .menu a,
    button,
    .item button,
    .back-to-top {
        min-height: 44px;  /* iOS minimum touch target */
        min-width: 44px;
        padding: 12px 20px;
}

    /* Improve mobile navigation */
    /* Mobile Responsive */
    @media (max-width: 768px) {
    .menu {
        display: none;
    }

    .menu.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--background-color);
        padding: 1rem;
    }
}

    .menu a {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 16px;
    }

    /* Optimize item layout for touch */
    .item {
        padding: 16px;
        margin: 12px;
        border-radius: 12px;  /* Slightly larger for better touch feel */
    }

    /* Ensure sufficient spacing between interactive elements */
    .user-profile {
        padding: 8px;
    }

    .dropdown {
        padding: 12px 0;
    }

    .dropdown a {
        padding: 12px 16px;
        display: block;
    }
}

@media screen and (max-width: 480px) {
    .section .item {
        min-width: 220px;
    }

    .section h2 {
        font-size: 32px;
    }
}

/* Media Query for Mobile Viewport */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
        margin-left: auto;
        margin-right: auto;
    }
}

button {
    transition: background-color var(--transition-speed), transform var(--transition-speed);
}

button:hover {
    background-color: var(--button-gradient-end);
    transform: scale(1.05);
}

/* Section Placeholder Styling */
.placeholder {
    background: #e0e0e0;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 200px;
    border-radius: 8px;
}

.placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.4) 50%, rgba(255,255,255,0) 100%);
    animation: shimmer 1.5s infinite;
}

/* Library Page Specific Styles */
.library-header {
    text-align: center;
    margin-bottom: 40px;
}

.library-header h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
}

.library-header .search-container {
    max-width: 600px;
    margin: 0 auto;
}

.playlists-section .img-container,
.favorite-songs .img-container,
.recently-played .img-container {
    width: 160px;
    height: 160px;
    margin: 0 auto;
    border-radius: 10px;
    overflow: hidden;
}

/* Carousel Styles moved to home.css */

.recommendation-card {
    background: var(--card-bg);
    padding: 1rem;
    border-radius: 12px;
    text-align: center;
    transition: transform 0.3s ease;
}

.recommendation-card:hover {
    transform: translateY(-5px);
}

.recommendation-card .img-container {
    width: 100%;
    aspect-ratio: 1;
    margin-bottom: 1rem;
    overflow: hidden;
    border-radius: 8px;
}

.recommendation-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recommendation-card h3 {
    margin: 0.5rem 0;
    font-size: 1.1rem;
    color: var(--text-primary);
}

/* Flickity Customization moved to home.css */



/* Card styles moved to card.css */




/* Animation keyframes moved to animations.css */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
