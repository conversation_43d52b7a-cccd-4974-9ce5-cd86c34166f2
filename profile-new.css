/* Profile Page Styles */

/* Profile Icon Animation */
.profile-button.active .profile-icon {
    animation: profilePulse 2s infinite alternate;
    border: 2px solid var(--neon-blue);
    box-shadow:
        0 0 10px rgba(0, 224, 255, 0.4),
        0 0 20px rgba(0, 224, 255, 0.2);
}

@keyframes profilePulse {
    0% {
        box-shadow:
            0 0 10px rgba(0, 224, 255, 0.4),
            0 0 20px rgba(0, 224, 255, 0.2);
        transform: scale(1);
    }
    100% {
        box-shadow:
            0 0 15px rgba(0, 224, 255, 0.6),
            0 0 30px rgba(0, 224, 255, 0.3);
        transform: scale(1.05);
    }
}

/* CSS Variables */
:root {
    --profile-section-gap: 1.5rem;
    --profile-header-height: 180px;
    --profile-image-size: 48px;
    --card-hover-transform: translateY(-3px);
    --card-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Main Content */
main {
    padding-top: 80px;
    min-height: 100vh;
    background: var(--background-color);
    color: var(--text-color);
}

/* ===== PROFILE HERO SECTION ===== */
.profile-hero {
    position: relative;
    width: 100%;
    min-height: 340px;
    padding: 4rem 0;
    margin-top: 2rem;
    background: var(--background-color);
    overflow: hidden;
}

.profile-hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.15) 0%,
        rgba(var(--cosmic-pink-rgb), 0.15) 50%,
        rgba(var(--electric-violet-rgb), 0.15) 100%
    );
    z-index: 1;
}

.profile-container {
    position: relative;
    width: 100%;
    max-width: 1400px;
    margin: 0;
    padding-left: 80px;
    z-index: 2;
    display: flex;
    justify-content: flex-start;
}

/* Profile Header */
.profile-header {
    display: flex;
    gap: 2.5rem;
    align-items: flex-start;
    padding: 2.5rem;
    background: rgba(255, 255, 255, 0.05); /* More pronounced background */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 24px; /* Match Artist Page */
    border: 1px solid rgba(255, 255, 255, 0.15); /* Slightly more visible border */
    width: 800px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden; /* For backdrop gradient */
}

.profile-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Add backdrop gradient similar to Artist Page */
.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: -1;
    opacity: 0.5;
}

/* Profile Image Section */
.profile-image-container {
    position: relative;
    width: 140px;
    height: 140px;
    border-radius: 50%;
}

.profile-image-container img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.profile-image-container:hover img {
    transform: scale(1.02);
}

/* Edit Image Button */
.edit-image-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(
        45deg,
        var(--neon-blue),
        var(--cosmic-pink)
    );
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    z-index: 10;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.edit-image-btn i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 1.2rem;
    line-height: 1;
    position: relative;
    left: 0.5px;
    top: 0.5px;
}

.edit-image-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

/* Profile Info */
.profile-info {
    flex: 1;
    padding-top: 0.5rem;
}

.profile-name {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    background: linear-gradient(
        45deg,
        var(--neon-blue),
        var(--cosmic-pink) 50%,
        var(--electric-violet)
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    letter-spacing: -0.5px;
}

.profile-bio {
    font-size: 1.05rem;
    color: var(--text-secondary);
    margin-bottom: 1.75rem;
    line-height: 1.8;
    max-width: 90%;
    position: relative;
    overflow-y: auto; /* Make it scrollable */
    max-height: 120px; /* Fixed height for scrolling */
    padding-right: 10px; /* Add padding for scrollbar */
    background: rgba(255, 255, 255, 0.05); /* More pronounced background */
    border-radius: 8px; /* Rounded corners */
    padding: 12px; /* Add padding */
    border: 1px solid rgba(255, 255, 255, 0.15); /* Slightly more visible border */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); /* Added subtle shadow */
}

/* Custom scrollbar for the profile bio */
.profile-bio::-webkit-scrollbar {
    width: 6px;
}

.profile-bio::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb {
    background: var(--neon-blue);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(var(--neon-blue), var(--cosmic-pink));
}

/* Profile Stats */
.profile-stats {
    display: flex;
    gap: 2.5rem;
    margin-top: 1rem;
    position: relative;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05); /* More pronounced background */
    border: 1px solid rgba(255, 255, 255, 0.15); /* Slightly more visible border */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); /* Added subtle shadow */
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    min-width: 110px;
}

.stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    background: rgba(255, 255, 255, 0.08); /* More pronounced hover background */
    border-color: rgba(var(--neon-blue-rgb), 0.2); /* Subtle colored border on hover */
}

.stat::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
}

.stat:hover::before {
    transform: scaleX(1);
}

.stat-value {
    font-size: 1.6rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.stat-icon {
    font-size: 1.5rem;
    color: white; /* White icons for a cleaner look */
    margin-bottom: 0.5rem;
    transition: transform 0.3s ease;
}

.stat:hover .stat-icon {
    transform: scale(1.2);
    /* Keeping the icon white on hover for a cleaner look */
    color: white;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Edit Profile Button */
.edit-profile-btn {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: linear-gradient(
        45deg,
        var(--neon-blue),
        var(--cosmic-pink)
    );
    border: none;
    color: white;
    padding: 0.4rem 0.9rem;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(var(--neon-blue-rgb), 0.3);
    /* Removed text shadow for cleaner look */
}

.edit-profile-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(var(--neon-blue-rgb), 0.4);
}

/* ===== PROFILE CONTENT SECTION ===== */
.profile-content {
    padding: 2rem 0;
    max-width: 1200px;
    margin: 0 auto;
}

.content-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.content-section:hover {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

.view-all-btn {
    background: var(--button-gradient);
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed);
    box-shadow: var(--button-shadow);
    /* Removed text shadow for cleaner look */
}

.view-all-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

/* ===== PLAYLISTS SECTION ===== */
/* Playlists Grid */
.playlists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.03);
}

/* Playlist Card */
.playlist-card {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.playlist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.playlist-card .img-container {
    position: relative;
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
}

.playlist-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.playlist-card:hover img {
    transform: scale(1.05);
}

.playlist-card .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.playlist-card:hover .play-overlay {
    opacity: 1;
}

.playlist-card .play-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--accent-color);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transform: scale(0.8);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    /* Removed text shadow for cleaner look */
}

.playlist-card .play-button i {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 1;
    margin: 0;
    padding: 0;
    position: relative;
    left: 1px;
}

.playlist-card:hover .play-button {
    transform: scale(1);
}

.playlist-card .play-button:hover {
    background: var(--neon-blue);
    transform: scale(1.1);
}

.playlist-info {
    padding: 1rem;
}

.playlist-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
}

.playlist-tracks {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.6);
}

/* Create Playlist Card */
.create-playlist-card {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.03);
    border: 2px dashed rgba(255, 255, 255, 0.1);
    box-shadow: none;
}

.create-playlist-card:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.2);
}

.create-playlist-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
}

.create-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: white;
    transition: all 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.create-icon i {
    font-size: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 1;
    margin: 0;
    padding: 0;
    position: relative;
}

.create-playlist-card:hover .create-icon {
    transform: scale(1.1);
}

/* ===== RECENTLY PLAYED SECTION ===== */
/* Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.03);
}

/* Card */
.card {
    position: relative;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: var(--card-transition);
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.card .img-container {
    position: relative;
    width: 100%;
    aspect-ratio: 1;
    overflow: hidden;
}

.card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover img {
    transform: scale(1.05);
}

.card .play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover .play-overlay {
    opacity: 1;
}

.card .play-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--accent-color);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transform: scale(0.8);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.card .play-button i {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    line-height: 1;
    margin: 0;
    padding: 0;
    position: relative;
    left: 1px;
}

.card:hover .play-button {
    transform: scale(1);
}

.card .play-button:hover {
    background: var(--neon-blue);
    transform: scale(1.1);
}

.card-content {
    padding: 1rem;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
}

.card-subtitle {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.6);
}

/* ===== FAVORITE ARTISTS SECTION ===== */
/* Artist Card */
.artist-card .img-container {
    border-radius: 50%;
    width: 80%;
    margin: 1rem auto 0;
    aspect-ratio: 1;
    background: linear-gradient(45deg, rgba(var(--neon-blue-rgb), 0.3), rgba(var(--cosmic-pink-rgb), 0.3));
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.artist-card img {
    border-radius: 50%;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center top;
}

.artist-card .img-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2));
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.artist-card:hover .img-container::before {
    opacity: 1;
}

.artist-card .card-content {
    text-align: center;
    padding-top: 1rem;
}

/* ===== ANIMATIONS ===== */
@keyframes gradientMove {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* ===== MEDIA QUERIES ===== */
/* All media queries moved to the bottom for better organization */

/* Large Screens */
@media (max-width: 1200px) {
    .profile-container {
        padding-left: 60px;
    }

    .profile-header {
        width: 700px;
    }
}

/* Medium Screens */
@media (max-width: 992px) {
    .profile-container {
        padding-left: 40px;
    }

    .profile-header {
        width: 600px;
        padding: 2rem;
    }

    .profile-image-container {
        width: 120px;
        height: 120px;
    }

    .profile-name {
        font-size: 2rem;
    }
}

/* Small Screens */
@media (max-width: 768px) {
    .profile-hero {
        margin-top: 1rem;
        padding: 1rem 15px;
    }

    .profile-container {
        padding-left: 20px;
    }

    .profile-header {
        width: 90%;
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .profile-info {
        align-items: center;
        text-align: center;
    }

    .profile-bio {
        max-width: 100%;
    }

    .profile-stats {
        justify-content: center;
    }

    .edit-profile-btn {
        top: 1rem;
        right: 1rem;
        padding: 0.3rem 0.8rem;
        font-size: 0.75rem;
    }

    .content-section {
        padding: 1.25rem;
        margin-bottom: 2rem;
    }

    .playlists-grid,
    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .site-footer {
        padding: 1.5rem 0;
        margin-top: 3rem;
    }

    .footer-nav {
        gap: 1rem;
    }

    .back-to-top {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 40px;
        height: 40px;
    }
}

/* Extra Small Screens */
@media (max-width: 480px) {
    .profile-container {
        padding: 0 10px;
    }

    .profile-header {
        width: 100%;
        padding: 1.5rem 1rem;
    }

    .profile-image-container {
        width: 100px;
        height: 100px;
    }

    .profile-name {
        font-size: 1.8rem;
    }

    .profile-bio {
        font-size: 1rem;
    }

    .stat {
        padding: 0.6rem 0.8rem;
    }

    .stat-value {
        font-size: 1.4rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .profile-stats {
        gap: 1.5rem;
    }

    .content-section {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .playlists-grid,
    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .section-header h2 {
        font-size: 1.5rem;
    }

    .view-all-btn {
        padding: 0.3rem 0.8rem;
        font-size: 0.75rem;
    }

    .footer-nav {
        flex-direction: column;
        gap: 0.75rem;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 35px;
        height: 35px;
    }
}

/* Touch Devices */
@media (hover: none) {
    .card .play-overlay,
    .playlist-card .play-overlay {
        opacity: 0.8;
    }

    .card .play-button,
    .playlist-card .play-button {
        transform: scale(1);
    }

    .card {
        cursor: default;
    }

    .quick-actions {
        opacity: 1;
        transform: none;
    }

    .action-btn {
        padding: 0.75rem 1.5rem;
    }
}

/* ===== MODALS ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-speed), visibility var(--transition-speed);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: linear-gradient(
        135deg,
        rgba(30, 30, 40, 0.95),
        rgba(15, 15, 25, 0.95)
    );
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 2rem;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateY(-20px);
    transition: transform var(--transition-speed);
}

.modal.active .modal-content {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal h2, .modal h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    cursor: pointer;
    transition: color var(--transition-speed);
}

.close-btn:hover {
    color: var(--text-color);
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

input, textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: var(--text-color);
    font-family: inherit;
    font-size: 1rem;
    transition: all var(--transition-speed);
}

input:focus, textarea:focus {
    outline: none;
    border-color: rgba(var(--neon-blue-rgb), 0.5);
    box-shadow: 0 0 0 2px rgba(var(--neon-blue-rgb), 0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

.cancel-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all var(--transition-speed);
}

.cancel-btn:hover {
    background: rgba(255, 255, 255, 0.15);
}

.save-btn {
    background: var(--button-gradient);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all var(--transition-speed);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--neon-blue-rgb), 0.3);
}

/* Tab Buttons */
.tab-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.tab-btn {
    background: none;
    color: var(--text-color);
    padding: 0.5rem 1rem;
    cursor: pointer;
    opacity: 0.7;
    transition: all var(--transition-speed);
}

.tab-btn.active {
    opacity: 1;
    border-bottom: 2px solid var(--neon-blue);
}

/* Connections List */
.connections-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 1rem;
}

.connections-list.hidden {
    display: none;
}

.connection-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 8px;
    transition: background var(--transition-speed);
}

.connection-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.connection-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 1rem;
}

.connection-info {
    flex: 1;
}

.connection-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.connection-meta {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.6);
}

.connection-action {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    transition: all var(--transition-speed);
}

.connection-action:hover {
    background: var(--button-gradient);
    transform: translateY(-2px);
}

/* Activity Modal */
.activity-details {
    margin-bottom: 1.5rem;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Footer Styles */
.site-footer {
    position: relative;
    width: 100%;
    background: linear-gradient(
        to bottom,
        rgba(255, 255, 255, 0.05),
        rgba(255, 255, 255, 0.02)
    );
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2rem 0;
    margin-top: 4rem;
    z-index: 10;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
}

.footer-logo img {
    height: 40px;
    width: auto;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-logo img:hover {
    opacity: 1;
}

.footer-info {
    text-align: center;
}

.copyright {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0 0 1rem;
}

.footer-nav {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.footer-nav a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-nav a:hover {
    color: var(--accent-color);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--accent-color);
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--neon-blue);
    transform: translateY(-3px);
}

.back-to-top i {
    font-size: 1.2rem;
}
