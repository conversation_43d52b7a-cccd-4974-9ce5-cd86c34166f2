/* Artist Page Styles */

/* CSS Variables */
:root {
    /* Colors - Updated to match site theme */
    --background-color: #0D1117;
    --text-color: #ffffff;
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    --cyber-lime-rgb: 167, 255, 74;
    --accent-color: var(--cosmic-pink);
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;

    /* Standardize variable names to match other pages */
    --text-primary: var(--text-color);
    --text-secondary: rgba(255, 255, 255, 0.7);
    --background: var(--background-color);
    --border: rgba(255, 255, 255, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --button-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    --transition-speed: 0.3s ease;
    --transition-fast: 0.2s ease;
    --header-gradient: linear-gradient(to right, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    --hover-color: rgba(255, 255, 255, 0.05);
    --error-color: #ff4d4d;
    --button-hover-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);

    /* Button Gradient - Updated to match style.css */
    --button-gradient: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --card-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
}

/* General Styles */
body {
    margin: 0;
    padding: 0;
    padding-top: 114px; /* Add space for fixed navbar */
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
    font-weight: 400;
    line-height: 1.5;
    overflow-x: hidden;
}

/* Navbar Styles */
header {
    background: linear-gradient(
        to right,
        var(--header-gradient-start) 0%,
        var(--header-gradient-end) 100%
    );
    padding: 12px 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo img {
    width: 90px;
    height: 90px;
    transition: transform var(--transition-speed);
}

.logo img:hover {
    transform: scale(1.05);
}

.menu {
    list-style: none;
    display: flex;
    gap: 3rem;
    margin: 0;
    padding: 0;
}

.menu li a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.125rem;
    position: relative;
    padding: 0.5rem 0;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0);
}

.menu li a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--button-gradient);
    transition: width 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0), opacity 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0), box-shadow 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0);
    opacity: 0;
}

.menu li a:hover::after,
.menu li a[aria-current="page"]::after {
    width: 100%;
    opacity: 1;
    box-shadow: 0 0 20px var(--neon-blue);
}

.menu li a:hover {
    color: rgba(255, 255, 255, 1);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.user-profile {
    position: relative;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(var(--neon-blue-rgb), 0.3);
    transition: transform var(--transition-speed), border-color var(--transition-speed), box-shadow var(--transition-speed), filter var(--transition-speed);
}

.profile-button:hover .profile-icon {
    transform: scale(1.05);
    border-color: rgba(0, 224, 255, 0.3);
    box-shadow:
        0 0 15px rgba(0, 224, 255, 0.2),
        0 0 30px rgba(0, 224, 255, 0.1);
    filter: brightness(1.1);
}

.dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: rgba(20, 23, 31, 0.85);
    border-radius: 12px;
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px) scale(0.98);
    transition: all 0.25s cubic-bezier(0.3, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow:
        0 10px 25px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 224, 255, 0.08);
    -webkit-backdrop-filter: blur(12px);
    backdrop-filter: blur(12px);
    transform-origin: top right;
    z-index: 1001;
    overflow: hidden;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--button-gradient);
    opacity: 0.8;
}

.dropdown.show,
.user-profile:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 8px 0;
}

.dropdown li {
    margin: 0;
    padding: 0;
}

.dropdown a {
    color: var(--text-primary);
    text-decoration: none;
    padding: 10px 16px;
    display: block;
    font-size: 14px;
    transition: all 0.2s ease;
    position: relative;
    margin: 2px 5px;
    border-radius: 8px;
    font-weight: 500;
}

.dropdown a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: var(--button-gradient);
    opacity: 0.1;
    transition: width 0.2s ease;
    border-radius: 8px;
    z-index: -1;
}

.dropdown a:hover {
    color: white;
    transform: translateX(3px);
}

.dropdown a:hover::before {
    width: 100%;
}

.dropdown .logout {
    color: var(--cosmic-pink);
    margin-top: 5px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: 12px;
}

.dropdown .logout:hover {
    color: white;
    background-color: rgba(var(--cosmic-pink-rgb), 0.15);
}

/* Removed duplicate logo hover effect */

@keyframes swing {
    0% { transform: rotate(0deg); }
    20% { transform: rotate(15deg); }
    40% { transform: rotate(-10deg); }
    60% { transform: rotate(5deg); }
    80% { transform: rotate(-5deg); }
    100% { transform: rotate(0deg); }
}

.menu {
    list-style: none;
    display: flex;
    letter-spacing: 1.2px;
    margin: 0;
    padding: 0;
    gap: 3rem;
}

.menu li {
    margin: 0;
}

/* Removed duplicate menu styles */

/* Removed duplicate user profile styles */

/* Container Styles */
.container {
    font-family: "Plus Jakarta Sans", sans-serif;
    letter-spacing: 1.5px;
    width: 90%;
    margin: auto;
    padding: 20px;
    margin-top: 160px;
}

.section {
    margin: 20px 0;
}

.section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    background: linear-gradient(
        300deg,
        var(--neon-blue) 0%,
        var(--cosmic-pink) 25%,
        var(--neon-blue) 50%,
        var(--cosmic-pink) 75%,
        var(--neon-blue) 100%
    );
    background-size: 200% auto;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    letter-spacing: -0.5px;
    text-align: center;
    position: relative;
    padding-bottom: 12px;
    animation: gradientFlow 8s linear infinite;
}

.section h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--button-gradient);
    border-radius: 2px;
    opacity: 0.7;
}

.items {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.item {
    flex: 1 0 45%;
    background: rgba(255, 255, 255, 0.05);
    margin: 10px;
    padding: 20px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.album-item {
    display: flex;
    text-align: center;
    align-items: center;
    gap: 20px;
    padding: 20px;
}

.album-cover {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.album-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.album-item:hover .album-cover {
    transform: translateY(-5px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.3), 0 0 15px rgba(var(--neon-blue-rgb), 0.2);
}

.album-item:hover .album-cover img {
    transform: scale(1.1);
}

.album-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.album-details h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    transition: color 0.3s ease;
}

.album-item:hover .album-details h3 {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.album-year {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
}

.download-btn {
    align-self: center;
    margin-top: 10px;
    padding: 8px 16px;
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: var(--button-shadow);
}

.download-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

.download-btn:active {
    transform: translateY(-1px);
    box-shadow: var(--button-shadow);
}

.item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--button-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
    opacity: 0.7;
}

.item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.item:hover::before {
    transform: scaleX(1);
}

.item h3 {
    margin: 10px 0;
    letter-spacing: 0.5px;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.item:hover h3 {
    color: var(--neon-blue);
}

/* Standard Button Styles */
button {
    font-family: "Plus Jakarta Sans", sans-serif;
    font-size: 16px;
    font-weight: 600;
    padding: 12px 24px;
    background: var(--button-gradient);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all var(--transition-speed);
}

button:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

button:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.artist-container {
    padding-top: var(--section-spacing);
    max-width: 1400px;
    margin: 0 auto;
}

/* Removed duplicate artist header styles */

/* Removed duplicate artist photo container styles */


/* Removed duplicate artist photo styles */

/* Removed duplicate artist details styles */

/* Action buttons - Updated to match global styles */
.artist-actions {
    display: flex;
    gap: 12px; /* Reduced gap */
    margin-top: 15px; /* Reduced margin */
}

.action-btn {
    padding: 12px 24px;
    border-radius: 25px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 15px;
}

.play-btn {
    background: var(--button-gradient);
    color: white;
    box-shadow: var(--button-shadow);
}

.play-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

.play-btn:active {
    transform: translateY(-1px);
    box-shadow: var(--button-shadow);
}

.follow-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.follow-btn:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.share-btn {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.share-btn:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.play-btn:hover {
    box-shadow: 0 8px 25px rgba(var(--neon-blue-rgb), 0.4), 0 8px 25px rgba(var(--cosmic-pink-rgb), 0.4);
}

.action-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

/* Removed duplicate artist content styles */

/* Removed duplicate artist sidebar styles */

/* Removed duplicate artist image styles */

/* Removed duplicate artist bio styles */

/* Removed duplicate artist social styles */

/* Removed duplicate social link styles */

/* Removed duplicate artist main styles */

/* Removed duplicate section title styles */

/* Removed duplicate track grid styles */

/* Removed duplicate track card styles */

/* Removed duplicate track image styles */

/* Removed duplicate track info styles */

/* Removed duplicate track info p styles */

/* Removed duplicate track controls styles */

/* Removed duplicate play button styles */

/* Removed duplicate track actions styles */

/* Removed duplicate track actions button styles */

/* Gallery Section Styles */
.gallery-section {
    margin: 4rem 0;
    padding: 2rem 0;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

.gallery-item {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    aspect-ratio: 3/2;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(
        to top,
        rgba(0, 0, 0, 0.9) 0%,
        rgba(0, 0, 0, 0) 100%
    );
    padding: 1.5rem;
    color: white;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
    transform: translateY(0);
}

.gallery-title {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.gallery-date {
    font-size: 0.9rem;
    opacity: 0.8;
}

.gallery-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.gallery-controls .action-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    background: var(--button-gradient);
    border: none;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 14px;
    box-shadow: var(--button-shadow);
}

.gallery-controls .action-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

.gallery-controls .action-btn:active {
    transform: translateY(-1px);
    box-shadow: var(--button-shadow);
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
    .artist-content {
        grid-template-columns: 1fr;
    }

    .artist-sidebar {
        max-width: 400px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .artist-stats {
        flex-wrap: wrap;
    }

    .stat-item {
        flex: 1 0 calc(50% - var(--element-spacing));
    }

    .track-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .gallery {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 1rem;
    }

    .gallery-buttons {
        flex-direction: row;
        gap: 0.8rem;
    }

    .gallery-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

/* Artist Header Layout */
.artist-header {
    position: relative;
    display: flex;
    align-items: center;
    gap: 3rem; /* Reduced gap between photo and info */
    padding: 3rem 3rem; /* Reduced padding */
    margin: 1.5rem auto; /* Reduced margin */
    max-width: 1200px;
    background: rgba(255, 255, 255, 0.05); /* More pronounced background */
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.15); /* Slightly more visible border */
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2); /* Enhanced shadow */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.artist-header-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: -1;
    opacity: 0.5;
}

.artist-header-backdrop::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at top right,
        rgba(255, 255, 255, 0.05),
        transparent 50%
    ),
    radial-gradient(
        circle at bottom left,
        rgba(255, 255, 255, 0.05),
        transparent 50%
    );
    opacity: 0.4;
    z-index: -1;
}

/* Removed animation and purple coloring */

.artist-photo-container {
    flex: 0 0 320px;
    aspect-ratio: 1;
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.1);
    margin-top: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.artist-photo-container:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(var(--neon-blue-rgb), 0.3);
}

.artist-photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.artist-photo-container:hover .artist-photo-overlay {
    opacity: 1;
}

.artist-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.artist-photo-container:hover .artist-photo {
    transform: scale(1.05);
}

.artist-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 320px; /* Reduced min-height */
    justify-content: center;
    position: relative;
    z-index: 1;
    gap: 15px; /* Added gap between elements */
}

.artist-name h1 {
    font-size: 4rem;
    margin: 0;
    font-weight: 800;
    background: linear-gradient(
        45deg,
        var(--neon-blue),
        var(--cosmic-pink)
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 10px rgba(var(--neon-blue-rgb), 0.2);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    line-height: 1.1;
    letter-spacing: -0.5px;
}

.verified-badge {
    font-size: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
    transition: transform 0.3s ease, filter 0.3s ease;
}

.verified-badge:hover {
    transform: scale(1.2);
    filter: brightness(1.2) drop-shadow(0 0 5px rgba(var(--neon-blue-rgb), 0.5));
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 0.75rem; /* Reduced margin */
    font-weight: 500;
    letter-spacing: 0.5px;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.artist-name:hover .subtitle {
    opacity: 1;
}

.artist-bio-container {
    position: relative;
    margin-bottom: 0.75rem; /* Reduced margin */
}

.artist-bio {
    line-height: 1.8;
    color: var(--text-secondary);
    max-width: 700px;
    font-size: 1.05rem;
    position: relative;
    overflow-y: auto; /* Make it scrollable */
    max-height: 120px; /* Fixed height for scrolling */
    padding-right: 10px; /* Add padding for scrollbar */
    background: rgba(255, 255, 255, 0.05); /* More pronounced background */
    border-radius: 8px; /* Rounded corners */
    padding: 12px; /* Add padding */
    border: 1px solid rgba(255, 255, 255, 0.15); /* Slightly more visible border */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); /* Added subtle shadow */
}

/* Custom scrollbar for the artist bio */
.artist-bio::-webkit-scrollbar {
    width: 6px;
}

.artist-bio::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.artist-bio::-webkit-scrollbar-thumb {
    background: rgba(var(--neon-blue-rgb), 0.5);
    border-radius: 3px;
}

.artist-bio::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--neon-blue-rgb), 0.7);
}

.read-more-btn {
    background: none;
    border: none;
    color: var(--neon-blue);
    font-size: 0.9rem;
    padding: 0.5rem 0;
    cursor: pointer;
    font-weight: 600;
    transition: color 0.3s ease, transform 0.3s ease;
    display: inline-flex;
    align-items: center;
    margin-top: 0.5rem;
}

.read-more-btn:hover {
    color: var(--cosmic-pink);
    transform: translateX(5px);
}

.read-more-btn::after {
    content: '→';
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.read-more-btn:hover::after {
    transform: translateX(3px);
}

.artist-stats {
    display: flex;
    gap: 1rem; /* Reduced gap */
    margin-top: 0.75rem; /* Reduced margin */
    flex-wrap: wrap;
}

.stat {
    background: rgba(255, 255, 255, 0.05); /* More pronounced background */
    border: 1px solid rgba(255, 255, 255, 0.15); /* Slightly more visible border */
    border-radius: 12px;
    padding: 1rem; /* Reduced padding */
    text-align: center;
    flex: 1;
    min-width: 110px; /* Reduced min-width */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.4rem; /* Reduced gap */
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); /* Added subtle shadow */
}

.stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--button-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
}

.stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    background: rgba(255, 255, 255, 0.08); /* More pronounced hover background */
    border-color: rgba(var(--neon-blue-rgb), 0.2); /* Subtle colored border on hover */
}

.stat:hover::before {
    transform: scaleX(1);
}

.stat-icon {
    font-size: 1.5rem;
    color: white; /* Changed to white for a cleaner look */
    margin-bottom: 0.5rem;
    transition: transform 0.3s ease;
}

.stat:hover .stat-icon {
    transform: scale(1.2);
    /* Keeping the icon white on hover for a cleaner look */
    color: white;
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 800;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: block;
    margin-bottom: 0.25rem;
    line-height: 1;
    transition: transform 0.3s ease;
}

.stat:hover .stat-number {
    transform: scale(1.1);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.stat:hover .stat-label {
    color: var(--text-primary);
}

/* Track List */
.track-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin: 2rem 0;
}

.track-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--card-bg);
    border: var(--card-border);
    border-radius: var(--card-radius);
}

.track-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-muted);
    width: 30px;
}

.track-info {
    flex: 1;
}

.track-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.track-meta {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.track-duration {
    color: var(--text-muted);
}

/* Albums Section */
.albums-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--card-spacing);
    margin: var(--section-spacing) 0;
}

.album-card {
    background: var(--card-bg);
    border: var(--card-border);
    border-radius: var(--card-radius);
    overflow: hidden;
}

.album-artwork {
    aspect-ratio: 1;
    width: 100%;
    object-fit: cover;
}

.album-info {
    padding: 15px;
}

.album-year {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Similar Artists Section */
.similar-artists {
    margin: 4rem 0;
    padding: 2rem 0;
}

.similar-artists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.similar-artist-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: slideUp 0.5s ease forwards;
    opacity: 0;
    animation-delay: calc(var(--item-index, 0) * 0.1s);
}

.similar-artist-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--button-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
    opacity: 0.7;
}

.similar-artist-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2), 0 0 20px rgba(var(--neon-blue-rgb), 0.15);
}

.similar-artist-card:hover::before {
    transform: scaleX(1);
}

.similar-artist-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.similar-artist-card:hover .similar-artist-photo {
    border-color: rgba(var(--neon-blue-rgb), 0.3);
    box-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.2);
}

.similar-artist-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.similar-artist-card:hover .similar-artist-photo img {
    transform: scale(1.1);
}

.similar-artist-card h3 {
    font-size: 1.2rem;
    margin: 0.5rem 0;
    font-weight: 600;
    transition: color 0.3s ease;
}

.similar-artist-card:hover h3 {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.similar-artist-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.view-artist-btn {
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: auto;
    box-shadow: var(--button-shadow);
}

.view-artist-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

.view-artist-btn:active {
    transform: translateY(-1px);
    box-shadow: var(--button-shadow);
}

.similar-artist-card.clicked {
    animation: pulse 0.3s ease;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    100% { background-position: 200% 50%; }
}

@keyframes shimmer {
    0% {
        background-position: 0% 0%;
    }
    25% {
        background-position: 50% 100%;
    }
    50% {
        background-position: 100% 50%;
    }
    75% {
        background-position: 50% 0%;
    }
    100% {
        background-position: 0% 0%;
    }
}

.artist-carousel {
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: 200px;
    gap: var(--card-spacing);
    overflow-x: auto;
    padding: 20px 0;
    scroll-snap-type: x mandatory;
    /* scrollbar-width: none; */ /* Commented out due to compatibility issues */
}

.artist-carousel::-webkit-scrollbar {
    display: none;
}

.related-artist-card {
    scroll-snap-align: start;
    background: var(--card-bg);
    border: var(--card-border);
    border-radius: var(--card-radius);
    overflow: hidden;
}

/* Share Button Styles */
.share-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-color);
}

.share-btn:hover {
    background: rgba(255, 255, 255, 0.15);
}

/* Share Modal Styles */
.share-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--glass-bg);
    border-radius: 16px;
    padding: 2rem;
    z-index: 1000;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.share-modal[hidden] {
    display: none;
}

.share-modal h3 {
    margin: 0 0 1.5rem 0;
    font-size: 1.5rem;
    color: var(--text-color);
}

.share-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.share-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    cursor: pointer;
}

.copy-link {
    grid-column: span 2;
}

/* Modal Backdrop */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
    z-index: 999;
}

.modal-backdrop[hidden] {
    display: none;
}

/* Social Links */
.social-link {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-size: 1.5rem;
}

/* Section Divider Styles */
.section-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 4rem auto;
    max-width: 1200px;
    padding: 0 2rem;
}

.section-divider.small {
    margin: 2.5rem auto;
}

.divider-line {
    flex: 1;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.divider-icon {
    margin: 0 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.section-divider:hover .divider-icon {
    transform: rotate(360deg) scale(1.1);
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    box-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
}

/* Loading States and Animations */
.skeleton {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.05) 100%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 8px;
}

.skeleton-text {
    height: 16px;
    margin-bottom: 8px;
    width: 100%;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.skeleton-image {
    aspect-ratio: 1;
    width: 100%;
}

@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.slide-up {
    animation: slideUp 0.5s ease forwards;
}

.item {
    animation: slideUp 0.5s ease forwards;
    opacity: 0;
    animation-delay: calc(var(--item-index, 0) * 0.1s);
}

.stat {
    animation: slideUp 0.5s ease forwards;
    opacity: 0;
    animation-delay: calc(var(--item-index, 0) * 0.1s);
}

.gallery-item {
    animation: slideUp 0.5s ease forwards;
    opacity: 0;
    animation-delay: calc(var(--item-index, 0) * 0.1s);
}

/* Track Carousel Styles */
.track-carousel {
    /* Flickity handles the layout */
    margin: 0;
    padding: 30px 20px 60px; /* Added horizontal padding to match album carousel */
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.03);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    overflow: hidden !important; /* Prevent content from spilling out */
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.05);
    width: 100%; /* Ensure full width */
    box-sizing: border-box; /* Include padding in width calculation */
}

/* Track Item Styles */

.track-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Flickity specific styles */
.flickity-viewport {
    overflow: hidden !important; /* Prevent content from spilling out */
    position: relative;
    height: 600px !important; /* Increased height to match new track item size */
    min-height: 600px !important; /* Increased minimum height */
    border-radius: 16px;
    width: 100% !important; /* Ensure full width */
}

/* Fallback styles if Flickity fails to initialize */
.track-carousel:not(.flickity-enabled),
.album-carousel:not(.flickity-enabled) {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    gap: 20px;
    padding: 20px;
    min-height: 400px;
}

.flickity-slider {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
}

.flickity-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    transition: all 0.3s ease;
}

.flickity-button:hover {
    background: var(--button-gradient);
    box-shadow: var(--button-shadow);
    transform: translateY(-2px);
}

.flickity-button:focus {
    outline: none;
    box-shadow: 0 0 0 5px rgba(var(--neon-blue-rgb), 0.5);
}

.flickity-prev-next-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.flickity-prev-next-button.previous { left: 20px; z-index: 10; }
.flickity-prev-next-button.next { right: 20px; z-index: 10; }

/* Specific positioning for album carousel buttons */
.album-carousel .flickity-prev-next-button.previous { left: 10px; }
.album-carousel .flickity-prev-next-button.next { right: 10px; }

.flickity-page-dots {
    bottom: -30px;
}

.flickity-page-dots .dot {
    width: 10px;
    height: 10px;
    opacity: 0.5;
    background: white;
    transition: all 0.3s ease;
}

.flickity-page-dots .dot.is-selected {
    opacity: 1;
    background: var(--neon-blue);
    box-shadow: 0 0 10px rgba(var(--neon-blue-rgb), 0.5);
    transform: scale(1.2);
}

.track-carousel::-webkit-scrollbar {
    display: none;
}

.track-item {
    width: 1600px !important; /* Extremely wide for better visibility */
    margin-right: 40px !important; /* Maintain spacing */
    scroll-snap-align: start;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    flex-direction: row; /* Changed to row for side-by-side layout */
    position: relative;
    opacity: 1 !important; /* Force visibility */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.track-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--button-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
    z-index: 1;
}

.track-item::after {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at top right, rgba(var(--neon-blue-rgb), 0.05), transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.track-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.15);
    background: rgba(255, 255, 255, 0.08);
}

.track-item:hover::before {
    transform: scaleX(1);
}

.track-item:hover::after {
    opacity: 1;
}

.track-item.playing {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    background: rgba(255, 255, 255, 0.1);
}

.track-item.playing::before {
    transform: scaleX(1);
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    opacity: 1;
    animation: pulseGradient 2s infinite;
}

@keyframes pulseGradient {
    0% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0.7;
    }
}

.track-cover {
    position: relative;
    height: 500px; /* Increased height for better proportions */
    overflow: hidden;
    flex-shrink: 0;
    width: 500px; /* Increased width for better proportions */
    display: block;
}

.track-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.track-item:hover .track-cover img {
    transform: scale(1.05);
}

.track-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.7));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    transform: translateY(10px);
}

.track-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(var(--neon-blue-rgb), 0.2), transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 0;
}

.track-item:hover .track-overlay {
    opacity: 1;
    transform: translateY(0);
}

.track-item:hover .track-overlay::before {
    opacity: 1;
}

.track-item.playing .track-overlay {
    opacity: 1;
    transform: translateY(0);
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.8));
}

.track-item.playing .track-overlay::before {
    opacity: 1;
    animation: pulseOverlay 2s infinite;
}

@keyframes pulseOverlay {
    0% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 0.3;
    }
}

.play-track-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--button-gradient);
    border: none;
    color: white;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    position: relative;
    z-index: 2;
}

.play-track-btn::before {
    content: '';
    position: absolute;
    inset: -5px;
    border-radius: 50%;
    background: rgba(var(--neon-blue-rgb), 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.play-track-btn:hover {
    transform: scale(1);
    box-shadow: 0 0 30px rgba(var(--neon-blue-rgb), 0.4);
}

.play-track-btn:hover::before {
    opacity: 1;
    animation: pulse 1.5s infinite;
}

.track-item.playing .play-track-btn {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1);
}

.track-item.playing .play-track-btn i {
    animation: spin 10s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.track-details {
    padding: 40px;
    display: flex;
    flex-direction: column;
    gap: 25px;
    position: relative;
    z-index: 1;
    flex-grow: 1;
    background: rgba(0, 0, 0, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.05);
    width: calc(100% - 500px); /* Account for new track cover width */
}

/* Track Progress Bar */
.track-progress {
    margin: 10px 0;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0; /* Will be updated via JavaScript */
    background: var(--button-gradient);
    border-radius: 2px;
    transition: width 0.1s linear;
}

.progress-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
    align-self: flex-end;
}

.track-details::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.05), transparent);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.track-item:hover .track-details::before {
    opacity: 1;
}

.track-details h3 {
    margin: 0;
    font-size: 2rem; /* Increased font size for better visibility */
    font-weight: 700;
    transition: color 0.3s ease;
    line-height: 1.2;
    margin-bottom: 5px;
}

.track-item:hover .track-details h3 {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    transform: translateY(-2px);
}

.track-info {
    font-size: 1.3rem; /* Increased font size for better visibility */
    color: var(--text-secondary);
    margin: 0;
    position: relative;
    padding-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.track-info::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.track-item:hover .track-info::after {
    width: 50px;
    background: rgba(var(--neon-blue-rgb), 0.3);
}

.track-item.playing .track-info {
    color: var(--text-primary);
}

.track-item.playing .track-info::after {
    width: 50px;
    background: var(--button-gradient);
    height: 3px;
}

.track-controls {
    display: flex;
    justify-content: flex-start;
    gap: 20px;
    margin-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.track-action-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    font-size: 1.5rem; /* Increased font size for better visibility */
    cursor: pointer;
    transition: all 0.3s ease;
    width: 60px; /* Increased size for better visibility */
    height: 60px; /* Increased size for better visibility */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.track-action-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--button-gradient);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.track-action-btn:hover {
    color: white;
    transform: translateY(-2px);
}

.track-action-btn:hover::before {
    opacity: 0.8;
}

.track-action-btn.clicked {
    transform: scale(1.2);
    color: white;
}

.track-action-btn.clicked::before {
    opacity: 1;
}

.track-item.playing .track-action-btn {
    color: var(--text-primary);
}

/* Carousel options section */

.carousel-options {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 15px;
}

.carousel-option-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.carousel-option-btn i {
    font-size: 1.1rem;
}

.carousel-option-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    transform: translateY(-2px);
}

.carousel-option-btn.active {
    background: var(--button-gradient);
    color: white;
    border-color: transparent;
    box-shadow: var(--button-shadow);
}

/* Expanded Demo Section */
.demos.expanded {
    margin-bottom: 3rem;
}

.demos.expanded .track-carousel {
    margin: 0;
    padding: 20px;
    gap: 20px;
    max-height: none;
}

.demos.expanded .track-item {
    animation: fadeIn 0.5s ease forwards;
}

/* Featured Albums Section */
.featured-albums {
    margin: 4rem 0 6rem;
    position: relative;
    padding-bottom: 30px;
}

.featured-albums::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle at center, rgba(var(--neon-blue-rgb), 0.05), transparent 70%);
    z-index: -1;
    border-radius: 30px;
    pointer-events: none;
}

.featured-albums h2 {
    margin-bottom: 2.5rem;
    position: relative;
    display: inline-block;
    font-size: 2.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
}

.featured-albums h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(var(--neon-blue-rgb), 0.5);
}

.album-carousel {
    /* Flickity handles the layout */
    margin: 0;
    padding: 30px 20px 60px; /* Added horizontal padding */
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.03);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    overflow: hidden !important; /* Prevent content from spilling out */
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.05);
    width: 100%; /* Ensure full width */
    box-sizing: border-box; /* Include padding in width calculation */
}

/* Album card styling */
.album-card {
    width: 300px; /* Adjusted width for better display */
    margin-right: 20px; /* Reduced margin for better spacing */
    perspective: 1000px;
    height: 400px; /* Adjusted height for better proportions */
    scroll-snap-align: start;
    opacity: 1;
    display: block;
    position: relative;
    transition: all 0.3s ease;
    margin: 10px 20px 10px 0; /* Adjusted margin */
    transform-origin: center center;
    cursor: pointer;
}

.album-card::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: radial-gradient(circle at center, rgba(var(--neon-blue-rgb), 0.05), transparent 70%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.5s ease;
    border-radius: 20px;
    pointer-events: none;
}

.album-card:hover::before {
    opacity: 1;
}

.album-card.is-selected {
    transform: scale(1.03);
    z-index: 5;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.album-card.is-selected .album-card-inner {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(var(--neon-blue-rgb), 0.3);
}

/* Album Flickity specific styles */
.album-carousel .flickity-page-dots {
    bottom: 20px; /* Move dots up to be inside the carousel container */
}

.album-carousel .flickity-page-dots .dot {
    background: rgba(var(--cosmic-pink-rgb), 0.5);
}

.album-carousel .flickity-page-dots .dot.is-selected {
    background: var(--cosmic-pink);
    box-shadow: 0 0 10px rgba(var(--cosmic-pink-rgb), 0.5);
}

.album-carousel .flickity-button:hover {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
}

.album-carousel::-webkit-scrollbar {
    display: none;
}

/* This style is now defined above with better visibility */

.album-card-inner {
    position: relative;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    text-align: center;
    transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    will-change: transform;
    overflow: hidden;
    background: rgba(20, 20, 30, 0.6);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.album-card:hover .album-card-inner {
    transform: rotateY(180deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3), 0 0 20px rgba(var(--neon-blue-rgb), 0.4);
    border: 1px solid rgba(var(--neon-blue-rgb), 0.3);
}

.album-card-front, .album-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    left: 0;
    top: 0;
    display: flex;
    flex-direction: column;
}

.album-card-front {
    background-color: rgba(20, 20, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.album-card-front img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s cubic-bezier(0.19, 1, 0.22, 1);
    max-width: 100%; /* Ensure image doesn't overflow */
    filter: brightness(0.9);
}

.album-card:hover .album-card-front img {
    transform: scale(1.08);
    filter: brightness(1.1) contrast(1.1);
}

.album-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 60px 20px 20px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.95) 0%, rgba(0, 0, 0, 0.8) 40%, rgba(0, 0, 0, 0.4) 80%, transparent 100%);
    color: white;
    text-align: left;
    transform: translateY(20px);
    opacity: 0.8;
    transition: all 0.6s cubic-bezier(0.19, 1, 0.22, 1);
    z-index: 2;
    -webkit-backdrop-filter: blur(0px);
    backdrop-filter: blur(0px);
}

.album-card:hover .album-card-overlay {
    transform: translateY(0);
    opacity: 1;
    padding-bottom: 25px;
    -webkit-backdrop-filter: blur(3px);
    backdrop-filter: blur(3px);
}

.album-card-overlay::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(var(--neon-blue-rgb), 0.7), transparent);
    transform: scaleX(0.3);
    opacity: 0;
    transition: all 0.4s ease;
}

.album-card:hover .album-card-overlay::before {
    transform: scaleX(0.8);
    opacity: 1;
}

.album-card-overlay h3 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.album-card:hover .album-card-overlay h3 {
    transform: translateY(-2px);
}

.album-card-overlay h3::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    transition: width 0.3s ease 0.1s;
}

.album-card:hover .album-card-overlay h3::after {
    width: 100%;
}

.album-card-overlay p {
    margin: 0 0 8px 0;
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.album-card-overlay p i {
    color: var(--cosmic-pink);
    font-size: 0.8rem;
    width: 16px;
    text-align: center;
}

.album-card:hover .album-card-overlay p {
    color: rgba(255, 255, 255, 0.9);
    transform: translateX(3px);
}

.album-rating {
    display: flex;
    align-items: center;
    gap: 3px;
    margin-top: 10px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
}

.album-rating i {
    color: #FFD700;
    font-size: 0.9rem;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
}

.album-rating span {
    margin-left: 5px;
    font-weight: 600;
}

.album-meta {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-bottom: 15px;
    background: rgba(0, 0, 0, 0.2);
    padding: 10px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.album-meta p {
    margin: 0;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    gap: 8px;
}

.album-meta p i {
    color: var(--cosmic-pink);
    width: 16px;
    text-align: center;
}

.album-card-back {
    background: linear-gradient(135deg, rgba(var(--cosmic-pink-rgb), 0.15), rgba(var(--neon-blue-rgb), 0.15));
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: white;
    transform: rotateY(180deg);
    padding: 25px;
    display: flex;
    flex-direction: column;
    box-shadow: inset 0 0 30px rgba(var(--neon-blue-rgb), 0.1);
    position: relative;
    overflow: hidden;
}

.album-card-back::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, rgba(var(--neon-blue-rgb), 0.1), transparent 70%);
    opacity: 0.5;
    z-index: 0;
    pointer-events: none;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.album-card-back > * {
    position: relative;
    z-index: 1;
}

.album-card-back h3 {
    margin: 0 0 15px 0;
    font-size: 1.5rem;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 10px rgba(var(--neon-blue-rgb), 0.3);
    position: relative;
    padding-bottom: 10px;
}

.album-card-back h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 2px;
}

.album-year {
    margin: 0 0 15px 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.album-tracks {
    list-style: none;
    padding: 5px;
    margin: 0 0 auto 0;
    text-align: left;
    overflow-y: auto;
    max-height: 200px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Custom scrollbar for WebKit browsers */
.album-tracks::-webkit-scrollbar {
    width: 6px;
}

.album-tracks::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.album-tracks::-webkit-scrollbar-thumb {
    background: rgba(var(--neon-blue-rgb), 0.5);
    border-radius: 3px;
}

.album-tracks::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--neon-blue-rgb), 0.7);
}

.album-tracks li {
    padding: 10px 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    position: relative;
    border-radius: 6px;
    margin-bottom: 2px;
}

.album-tracks li:hover {
    background: rgba(255, 255, 255, 0.05);
    padding-left: 12px;
    border-bottom-color: rgba(var(--neon-blue-rgb), 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.album-tracks li::before {
    content: '♪';
    position: absolute;
    left: -5px;
    opacity: 0;
    color: var(--neon-blue);
    transition: all 0.2s ease;
}

.album-tracks li:hover::before {
    left: 3px;
    opacity: 1;
}

.album-tracks li:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.album-tracks li .track-number {
    color: var(--cosmic-pink);
    font-weight: 600;
    margin-right: 8px;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.album-tracks li:hover .track-number {
    opacity: 1;
}

.album-tracks li .track-duration {
    color: var(--text-secondary);
    font-size: 0.8rem;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.album-tracks li:hover .track-duration {
    opacity: 1;
    color: var(--neon-blue);
}

.album-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 25px;
    position: relative;
    padding-top: 15px;
}

.album-actions::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.album-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--button-gradient);
    border: none;
    color: white;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
    position: relative;
    overflow: hidden;
}

.album-action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.5s ease, height 0.5s ease;
    z-index: 0;
}

.album-action-btn:hover::before {
    width: 120%;
    height: 120%;
}

.album-action-btn i {
    position: relative;
    z-index: 1;
}

.album-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

.album-action-btn:active {
    transform: translateY(-1px);
    box-shadow: var(--button-shadow);
}

.album-controls {
    margin-top: 30px;
}

/* Album pagination is now handled by Flickity */

/* Carousel controls are now handled by Flickity */

.track-item.playing {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
}

.track-action-btn.clicked {
    transform: scale(1.2);
    color: var(--neon-blue);
}

/* Footer Styles */
.site-footer {
    background: rgba(20, 30, 45, 0.8);
    padding: 40px 0;
    margin-top: 60px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.footer-info {
    text-align: center;
}

.copyright {
    color: var(--text-secondary);
    margin-bottom: 20px;
    font-size: 14px;
}

.footer-nav {
    display: flex;
    justify-content: center;
    gap: 30px;
}

.footer-nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 14px;
    transition: color var(--transition-speed);
}

.footer-nav a:hover {
    color: var(--text-primary);
    text-decoration: underline;
}

/* Media Queries - All consolidated at the bottom */
@media (max-width: 1200px) {
    .artist-header {
        padding: 5rem 3rem;
        gap: 3rem;
        margin: 1.5rem;
    }

    .artist-photo-container {
        flex: 0 0 300px;
    }

    .artist-info h1 {
        font-size: 3rem;
    }
}

@media (max-width: 1024px) {
    .track-list {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .albums-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 900px) {
    .artist-header {
        padding: 4rem 2rem;
    }

    .artist-photo-container {
        flex: 0 0 250px;
    }
}

@media (max-width: 768px) {
    .artist-header {
        flex-direction: column;
        padding: 1.5rem; /* Further reduced padding */
        gap: 1.5rem; /* Further reduced gap */
    }

    .artist-photo-container {
        margin-top: 1rem;
        width: 250px;
        height: 250px;
    }

    .artist-name h1 {
        font-size: 2.5rem;
        justify-content: center;
    }

    .subtitle {
        text-align: center;
    }

    .artist-bio {
        text-align: center;
        max-width: 100%;
    }

    .artist-actions {
        justify-content: center;
    }

    .artist-stats {
        justify-content: center;
    }

    .track-list {
        grid-template-columns: 1fr;
    }

    .artist-carousel {
        grid-auto-columns: 150px;
    }

    .track-carousel {
        grid-auto-columns: 220px;
        margin: 0 -10px;
        padding: 15px;
    }

    .track-details {
        padding: 15px;
    }

    .track-details h3 {
        font-size: 1.1rem;
    }

    .play-track-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .album-carousel {
        grid-auto-columns: 250px;
        margin: 0 -10px;
        padding: 15px;
    }

    .album-card {
        height: 350px;
    }

    .album-card-overlay h3 {
        font-size: 1.2rem;
    }

    .album-card-back h3 {
        font-size: 1.2rem;
    }

    .album-tracks {
        max-height: 150px;
    }

    .stat {
        padding: 1rem;
        min-width: 100px;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .section h2 {
        font-size: 1.8rem;
    }

    .items {
        flex-direction: column;
    }

    .item {
        flex: 1 0 100%;
        margin: 10px 0;
    }

    .album-item {
        flex-direction: row;
        align-items: center;
        padding: 15px;
    }

    .album-cover {
        width: 100px;
        height: 100px;
    }

    .section-divider {
        margin: 3rem auto;
    }

    .section-divider.small {
        margin: 2rem auto;
    }
}

@media (max-width: 480px) {
    .artist-header {
        padding: 1rem; /* Further reduced padding */
        margin: 0.5rem;
    }

    .artist-photo-container {
        width: 200px;
        height: 200px;
    }

    .artist-name h1 {
        font-size: 2rem;
    }

    .artist-stats {
        flex-direction: column;
        align-items: center;
    }

    .stat {
        width: 100%;
        max-width: 200px;
    }

    .artist-actions {
        flex-direction: column;
        gap: 10px;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }

    .album-item {
        flex-direction: column;
        text-align: center;
        padding: 15px;
    }

    .album-cover {
        width: 120px;
        height: 120px;
        margin: 0 auto;
    }

    .album-details {
        align-items: center;
    }

    .download-btn {
        align-self: center;
    }

    .track-carousel {
        grid-auto-columns: 180px;
        margin: 0 -5px;
        padding: 10px;
    }

    .track-details {
        padding: 12px;
    }

    .track-details h3 {
        font-size: 1rem;
    }

    .track-info {
        font-size: 0.8rem;
    }

    .play-track-btn {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .track-action-btn {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }

    /* Carousel controls are now handled by Flickity */

    .album-carousel {
        grid-auto-columns: 200px;
        margin: 0 -5px;
        padding: 10px;
    }

    .album-card {
        height: 300px;
    }

    .album-card-overlay h3 {
        font-size: 1.1rem;
    }

    .album-card-overlay p {
        font-size: 0.8rem;
    }

    .album-card-back {
        padding: 15px;
    }

    .album-card-back h3 {
        font-size: 1.1rem;
    }

    .album-year {
        font-size: 0.8rem;
        margin-bottom: 10px;
    }

    .album-tracks {
        max-height: 120px;
    }

    .album-tracks li {
        font-size: 0.8rem;
        padding: 6px 0;
    }

    .album-action-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    /* Album controls are now handled by Flickity */

    /* Pagination dots are now handled by Flickity */

    .carousel-options {
        flex-direction: column;
        gap: 10px;
    }

    .carousel-option-btn {
        width: 100%;
        justify-content: center;
    }

    .section h2 {
        font-size: 1.5rem;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
    }

    .gallery-controls {
        flex-direction: column;
        gap: 10px;
    }

    .gallery-controls .action-btn {
        width: 100%;
    }

    .section-divider {
        margin: 2rem auto;
    }

    .section-divider.small {
        margin: 1.5rem auto;
    }

    .divider-icon {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }

    .footer-nav {
        flex-direction: column;
        gap: 15px;
    }
}


