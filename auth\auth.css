/* Authentication Pages Styles */

:root {
    --auth-card-width: 450px;
    --auth-card-padding: 40px;
    --auth-input-height: 50px;
    --auth-button-height: 50px;
    --auth-border-radius: 12px;
    --auth-card-bg: rgba(25, 32, 44, 0.8);
    --auth-input-bg: rgba(13, 17, 23, 0.6);
    --auth-border-color: rgba(255, 255, 255, 0.1);
    --auth-error-color: #ff3b30;
    --auth-success-color: #34c759;
    --auth-google-color: #ea4335;
    --auth-facebook-color: #1877f2;
    --auth-apple-color: #ffffff;
}

/* Base Styles */
.auth-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #0D1117, #1a1d24);
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(0, 224, 255, 0.1) 0%, rgba(255, 0, 110, 0.1) 50%, transparent 70%);
    opacity: 0.5;
    z-index: 0;
}

/* Animated background particles */
.auth-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 20% 30%, rgba(0, 224, 255, 0.2) 0%, transparent 10%),
        radial-gradient(circle at 80% 70%, rgba(255, 0, 110, 0.2) 0%, transparent 10%);
    opacity: 0.3;
    z-index: 0;
    animation: pulse 15s infinite alternate;
}

@keyframes pulse {
    0% {
        opacity: 0.2;
        transform: scale(1);
    }
    50% {
        opacity: 0.3;
        transform: scale(1.05);
    }
    100% {
        opacity: 0.2;
        transform: scale(1);
    }
}

.auth-container {
    position: relative;
    z-index: 1;
    width: 100%;
    max-width: var(--auth-card-width);
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.auth-logo {
    margin-bottom: 30px;
    text-align: center;
}

.auth-logo img {
    width: 120px;
    height: 120px;
    filter: drop-shadow(0 0 10px rgba(0, 224, 255, 0.5));
}

/* Auth Card */
.auth-card {
    width: 100%;
    background: var(--auth-card-bg);
    border-radius: var(--auth-border-radius);
    padding: var(--auth-card-padding);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--auth-border-color);
    margin-bottom: 20px;
}

.auth-card.hidden {
    display: none;
}

.auth-card h1 {
    font-size: 28px;
    margin: 0 0 10px;
    color: var(--text-color);
    text-align: center;
}

.auth-subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    margin: 0 0 25px;
}

/* Form Styles */
.auth-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.5);
    font-size: 16px;
}

.input-with-icon input {
    width: 100%;
    height: var(--auth-input-height);
    padding: 0 15px 0 45px;
    border-radius: var(--auth-border-radius);
    border: 1px solid var(--auth-border-color);
    background: var(--auth-input-bg);
    color: var(--text-color);
    font-size: 16px;
    transition: all 0.3s ease;
}

.input-with-icon input:focus {
    border-color: var(--neon-blue);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 224, 255, 0.2);
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    font-size: 16px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: var(--text-color);
}

/* Password Strength Meter */
.password-strength {
    margin-top: 10px;
    display: flex;
    align-items: center;
}

.strength-meter {
    display: flex;
    gap: 5px;
    margin-right: 10px;
}

.strength-segment {
    height: 4px;
    width: 25px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.strength-segment.weak {
    background: var(--auth-error-color);
}

.strength-segment.medium {
    background: #ff9500;
}

.strength-segment.strong {
    background: #ffcc00;
}

.strength-segment.very-strong {
    background: var(--auth-success-color);
}

.strength-text {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 10px;
}

.remember-me, .terms-agreement {
    display: flex;
    align-items: center;
}

.remember-me input, .terms-agreement input {
    margin-right: 8px;
}

.remember-me label, .terms-agreement label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.forgot-password {
    font-size: 14px;
    color: var(--neon-blue);
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: var(--cosmic-pink);
    text-decoration: underline;
}

.terms-agreement a {
    color: var(--neon-blue);
    text-decoration: none;
    transition: color 0.3s ease;
}

.terms-agreement a:hover {
    color: var(--cosmic-pink);
    text-decoration: underline;
}

/* Auth Button */
.auth-button {
    display: block;
    width: 100%;
    height: var(--auth-button-height);
    background: var(--button-gradient);
    border: none;
    border-radius: 25px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    line-height: var(--auth-button-height);
    text-decoration: none;
}

.auth-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 224, 255, 0.3), 0 8px 15px rgba(255, 0, 110, 0.3);
}

.auth-button:active {
    transform: translateY(0);
}

/* Auth Divider */
.auth-divider {
    display: flex;
    align-items: center;
    margin: 25px 0;
}

.auth-divider::before, .auth-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: var(--auth-border-color);
}

.auth-divider span {
    padding: 0 15px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
}

/* Social Login */
.social-login {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 25px;
}

.social-button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: var(--auth-button-height);
    border-radius: 25px;
    border: 1px solid var(--auth-border-color);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 10px;
}

.social-button i {
    font-size: 18px;
}

.social-button.google:hover {
    background: rgba(234, 67, 53, 0.1);
    border-color: rgba(234, 67, 53, 0.3);
}

.social-button.facebook:hover {
    background: rgba(24, 119, 242, 0.1);
    border-color: rgba(24, 119, 242, 0.3);
}

.social-button.apple:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.social-button.google i {
    color: var(--auth-google-color);
}

.social-button.facebook i {
    color: var(--auth-facebook-color);
}

.social-button.apple i {
    color: var(--auth-apple-color);
}

/* Auth Redirect */
.auth-redirect {
    text-align: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.auth-redirect a {
    color: var(--neon-blue);
    text-decoration: none;
    transition: color 0.3s ease;
}

.auth-redirect a:hover {
    color: var(--cosmic-pink);
    text-decoration: underline;
}

/* Error and Success Messages */
.auth-error, .auth-success {
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    display: none;
}

.auth-error {
    background: rgba(255, 59, 48, 0.1);
    border: 1px solid rgba(255, 59, 48, 0.3);
    color: var(--auth-error-color);
}

.auth-success {
    background: rgba(52, 199, 89, 0.1);
    border: 1px solid rgba(52, 199, 89, 0.3);
    color: var(--auth-success-color);
}

.auth-error.visible, .auth-success.visible {
    display: block;
}

/* Auth Icon Success */
.auth-icon-success {
    text-align: center;
    margin-bottom: 20px;
}

.auth-icon-success i {
    font-size: 60px;
    color: var(--auth-success-color);
    animation: scaleIn 0.5s ease-out;
}

@keyframes scaleIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.auth-note {
    text-align: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    margin: 20px 0;
}

.auth-note a {
    color: var(--neon-blue);
    text-decoration: none;
    transition: color 0.3s ease;
}

.auth-note a:hover {
    color: var(--cosmic-pink);
    text-decoration: underline;
}

/* Responsive Styles */
@media (max-width: 480px) {
    .auth-card {
        padding: 30px 20px;
    }
    
    .auth-logo img {
        width: 100px;
        height: 100px;
    }
    
    .auth-card h1 {
        font-size: 24px;
    }
    
    .auth-subtitle {
        font-size: 14px;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .forgot-password {
        margin-top: 10px;
    }
}
