<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Library Test - Banshee Music App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #0D1117;
            color: white;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #00E0FF;
        }
        button {
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        .playlist {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .song {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
        }
        #status {
            background: rgba(0, 224, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Library Test</h1>
        <div id="status">Status: Loading...</div>
        
        <button id="create-playlist">Create Test Playlist</button>
        <button id="add-to-favorites">Add Song to Favorites</button>
        <button id="show-playlists">Show Playlists</button>
        <button id="show-favorites">Show Favorites</button>
        
        <div id="output"></div>
    </div>

    <script>
        // Simple library management system
        class Song {
            constructor(id, title, artist) {
                this.id = id;
                this.title = title;
                this.artist = artist;
            }
        }

        class Playlist {
            constructor(id, name) {
                this.id = id;
                this.name = name;
                this.songs = [];
            }
            
            addSong(song) {
                this.songs.push(song);
            }
        }

        class Library {
            constructor() {
                this.playlists = [];
                this.favorites = [];
                this.loadFromStorage();
            }
            
            createPlaylist(name) {
                const id = 'playlist_' + Date.now();
                const playlist = new Playlist(id, name);
                this.playlists.push(playlist);
                this.saveToStorage();
                return playlist;
            }
            
            addToFavorites(song) {
                this.favorites.push(song);
                this.saveToStorage();
            }
            
            saveToStorage() {
                localStorage.setItem('music_library', JSON.stringify({
                    playlists: this.playlists,
                    favorites: this.favorites
                }));
            }
            
            loadFromStorage() {
                const saved = localStorage.getItem('music_library');
                if (saved) {
                    try {
                        const data = JSON.parse(saved);
                        this.playlists = data.playlists || [];
                        this.favorites = data.favorites || [];
                    } catch (e) {
                        console.error('Error loading library:', e);
                    }
                }
            }
        }

        // Sample songs
        const sampleSongs = [
            new Song('song1', 'Blinding Lights', 'The Weeknd'),
            new Song('song2', 'Levitating', 'Dua Lipa'),
            new Song('song3', 'Save Your Tears', 'The Weeknd'),
            new Song('song4', 'Stay', 'The Kid LAROI, Justin Bieber')
        ];

        // Initialize library
        const library = new Library();
        
        // Update status
        document.getElementById('status').textContent = 'Status: Library loaded successfully!';
        
        // Event listeners
        document.getElementById('create-playlist').addEventListener('click', () => {
            const name = prompt('Enter playlist name:');
            if (name) {
                const playlist = library.createPlaylist(name);
                // Add a sample song
                playlist.addSong(sampleSongs[Math.floor(Math.random() * sampleSongs.length)]);
                library.saveToStorage();
                alert(`Playlist "${name}" created!`);
                showPlaylists();
            }
        });
        
        document.getElementById('add-to-favorites').addEventListener('click', () => {
            const song = sampleSongs[Math.floor(Math.random() * sampleSongs.length)];
            library.addToFavorites(song);
            alert(`Added "${song.title}" to favorites!`);
            showFavorites();
        });
        
        document.getElementById('show-playlists').addEventListener('click', showPlaylists);
        document.getElementById('show-favorites').addEventListener('click', showFavorites);
        
        function showPlaylists() {
            const output = document.getElementById('output');
            output.innerHTML = '<h2>Your Playlists</h2>';
            
            if (library.playlists.length === 0) {
                output.innerHTML += '<p>No playlists yet. Create one!</p>';
                return;
            }
            
            library.playlists.forEach(playlist => {
                const playlistEl = document.createElement('div');
                playlistEl.className = 'playlist';
                
                playlistEl.innerHTML = `
                    <h3>${playlist.name}</h3>
                    <p>${playlist.songs.length} songs</p>
                `;
                
                playlist.songs.forEach(song => {
                    const songEl = document.createElement('div');
                    songEl.className = 'song';
                    songEl.innerHTML = `
                        <span>${song.title}</span>
                        <span>${song.artist}</span>
                    `;
                    playlistEl.appendChild(songEl);
                });
                
                output.appendChild(playlistEl);
            });
        }
        
        function showFavorites() {
            const output = document.getElementById('output');
            output.innerHTML = '<h2>Your Favorites</h2>';
            
            if (library.favorites.length === 0) {
                output.innerHTML += '<p>No favorite songs yet.</p>';
                return;
            }
            
            const favoritesEl = document.createElement('div');
            favoritesEl.className = 'playlist';
            
            library.favorites.forEach(song => {
                const songEl = document.createElement('div');
                songEl.className = 'song';
                songEl.innerHTML = `
                    <span>${song.title}</span>
                    <span>${song.artist}</span>
                `;
                favoritesEl.appendChild(songEl);
            });
            
            output.appendChild(favoritesEl);
        }
        
        // Show playlists initially
        showPlaylists();
    </script>
</body>
</html>
