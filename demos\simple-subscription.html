<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Subscription - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Plus Jakarta Sans', sans-serif;
            background-color: #1c1c1e;
            color: #eaeaea;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 2rem;
            color: #fff;
        }
        
        .subscription-plans {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 2rem;
        }
        
        .plan {
            background: linear-gradient(315deg, rgba(13, 17, 23, 0.95), rgba(25, 32, 44, 0.95));
            border-radius: 12px;
            padding: 2rem;
            width: 300px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .plan:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 25px rgba(0, 224, 255, 0.2);
        }
        
        .plan.featured {
            border: 2px solid #00E0FF;
            position: relative;
            transform: scale(1.05);
        }
        
        .plan-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }
        
        .plan-header h2 {
            margin-bottom: 0.5rem;
            color: #fff;
        }
        
        .price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .currency {
            font-size: 1.5rem;
            margin-right: 0.25rem;
            align-self: flex-start;
        }
        
        .period {
            font-size: 1rem;
            color: #aaa;
            align-self: flex-end;
            margin-left: 0.25rem;
        }
        
        .plan ul {
            list-style: none;
            padding: 0;
            margin: 0 0 2rem 0;
        }
        
        .plan li {
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            padding-left: 1.5rem;
        }
        
        .plan li:before {
            content: "✓";
            color: #00E0FF;
            position: absolute;
            left: 0;
        }
        
        .subscribe-btn {
            display: block;
            width: 100%;
            padding: 1rem;
            background: linear-gradient(45deg, #00E0FF, #FF006E);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .subscribe-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
        }
        
        .toast {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 1rem 2rem;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
        }
        
        .toast.success {
            background-color: #4CAF50;
        }
        
        .toast.error {
            background-color: #F44336;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Choose Your Subscription</h1>
        
        <div class="subscription-plans">
            <div class="plan">
                <div class="plan-header">
                    <h2>Free Account</h2>
                    <p class="price">
                        <span class="currency">$</span>
                        0
                        <span class="period">/month</span>
                    </p>
                </div>
                <ul>
                    <li>Enjoy Banshee with occasional ads</li>
                    <li>Basic audio quality (128kbps)</li>
                    <li>Limited skips (6 per hour)</li>
                    <li>Mobile app access</li>
                </ul>
                <button class="subscribe-btn" data-plan="free">Get Started</button>
            </div>
            
            <div class="plan featured">
                <div class="plan-header">
                    <h2>Premium</h2>
                    <p class="price">
                        <span class="currency">$</span>
                        2.99
                        <span class="period">/month</span>
                    </p>
                </div>
                <ul>
                    <li>Ad-free listening experience</li>
                    <li>High-quality audio (320kbps)</li>
                    <li>Unlimited skips</li>
                    <li>Offline mode</li>
                    <li>Cross-platform sync</li>
                    <li>Exclusive content access</li>
                </ul>
                <button class="subscribe-btn" data-plan="premium">Subscribe Now</button>
            </div>
            
            <div class="plan">
                <div class="plan-header">
                    <h2>Artist Account</h2>
                    <p class="price">
                        <span class="currency">$</span>
                        4.99
                        <span class="period">/month</span>
                    </p>
                </div>
                <ul>
                    <li>All Premium features included</li>
                    <li>Upload unlimited tracks</li>
                    <li>Advanced analytics dashboard</li>
                    <li>Promotional tools</li>
                    <li>Direct fan engagement</li>
                    <li>Custom artist profile</li>
                </ul>
                <button class="subscribe-btn" data-plan="artist">Start Creating</button>
            </div>
        </div>
    </div>

    <script>
        // Simple subscription plans data
        const subscriptionPlans = [
            {
                id: 'free',
                name: 'Free Account',
                price: 0,
                interval: 'month',
                features: [
                    'Enjoy Banshee with occasional ads',
                    'Basic audio quality (128kbps)',
                    'Limited skips (6 per hour)',
                    'Mobile app access'
                ]
            },
            {
                id: 'premium',
                name: 'Premium',
                price: 2.99,
                interval: 'month',
                features: [
                    'Ad-free listening experience',
                    'High-quality audio (320kbps)',
                    'Unlimited skips',
                    'Offline mode',
                    'Cross-platform sync',
                    'Exclusive content access'
                ]
            },
            {
                id: 'artist',
                name: 'Artist Account',
                price: 4.99,
                interval: 'month',
                features: [
                    'All Premium features included',
                    'Upload unlimited tracks',
                    'Advanced analytics dashboard',
                    'Promotional tools',
                    'Direct fan engagement',
                    'Custom artist profile'
                ]
            }
        ];

        // Initialize subscription buttons
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Page loaded');
            
            const subscribeBtns = document.querySelectorAll('.subscribe-btn');
            subscribeBtns.forEach(btn => {
                btn.addEventListener('click', handleSubscribe);
            });
        });

        // Handle subscription button click
        function handleSubscribe(event) {
            const planId = event.target.dataset.plan;
            console.log('Selected plan:', planId);
            
            // Find the selected plan
            const selectedPlan = subscriptionPlans.find(plan => plan.id === planId);
            
            if (!selectedPlan) {
                showToast('Plan not found', 'error');
                return;
            }
            
            // Save selected plan to session storage
            sessionStorage.setItem('selectedPlan', JSON.stringify(selectedPlan));
            
            // Generate a mock subscription ID
            const subscriptionId = 'sub_' + Math.random().toString(36).substring(2);
            sessionStorage.setItem('subscriptionId', subscriptionId);
            
            // Show success message
            showToast(`Successfully selected ${selectedPlan.name} plan!`);
            
            // Redirect based on plan type
            setTimeout(() => {
                if (planId === 'free') {
                    // For free plan, go directly to confirmation
                    window.location.href = 'simple-confirmation.html';
                } else {
                    // For paid plans, go to payment page
                    window.location.href = 'simple-payment.html';
                }
            }, 1500);
        }

        // Show toast message
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>
