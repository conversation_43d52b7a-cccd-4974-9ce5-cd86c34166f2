/*
 * Explore Page Styles
 * Cleaned up and consolidated
 */

/* ===== Explore Page Specific Variables ===== */
:root {
    --explore-section-gap: 2rem;
}

/* ===== Main Container ===== */
.explore-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    margin-top: 80px; /* Account for fixed header */
}

/* ===== Hero Section ===== */
.explore-hero {
    position: relative;
    overflow: hidden;
    padding: 3rem 2rem;
    margin-bottom: 2rem;
    border-radius: var(--border-radius-lg);
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
}

.explore-hero .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.explore-hero .hero-content h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 800;
    letter-spacing: -0.5px;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Glowing overlay */
.explore-hero::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.15) 0%,
        transparent 70%
    ), linear-gradient(
        45deg,
        rgba(var(--neon-blue-rgb), 0.2) 0%,
        transparent 25%,
        rgba(255, 255, 255, 0.15) 50%,
        transparent 75%,
        rgba(255, 0, 110, 0.2) 100%
    );
    opacity: 0.8;
    mix-blend-mode: overlay;
    animation: shimmer 12s infinite ease-in-out;
    background-size: 200% 200%;
    border-radius: inherit;
    z-index: 1;
}

/* Add backdrop gradient */
.explore-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: 0;
    opacity: 0.5;
}

/* ===== Search Section ===== */
.search-section {
    margin: 2rem 0;
    padding: 1.5rem 1.5rem 2.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-container {
    position: relative;
    margin-bottom: 1.5rem;
    display: flex;
    gap: 10px;
}

.search-input-wrapper {
    flex: 1;
    position: relative;
}

.search-bar {
    width: 100%;
    padding: 1rem 3rem;
    border-radius: 50px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    font-size: 1rem;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.search-bar:focus {
    outline: none;
    border-color: rgba(var(--neon-blue-rgb), 0.5);
    box-shadow: 0 0 15px rgba(var(--neon-blue-rgb), 0.2);
}

.search-icon {
    position: absolute;
    left: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-clear {
    position: absolute;
    right: 1.2rem;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.search-clear:hover {
    opacity: 1;
}

/* Filter styles */
.filters-container {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.filter-select {
    flex: 1;
    padding: 0.8rem 1rem;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    min-width: 150px;
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: rgba(var(--neon-blue-rgb), 0.5);
    box-shadow: 0 0 15px rgba(var(--neon-blue-rgb), 0.2);
}

.filter-select option {
    background-color: #1a1f2c;
    color: var(--text-color);
}

.results-count {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-align: center;
}

/* ===== Carousel Styles ===== */
.carousel {
    margin: 0;
    padding: 30px 20px 60px;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.03);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    overflow: hidden !important;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.05);
    width: 100%;
    box-sizing: border-box;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.carousel-cell {
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
}

/* ===== Card Grid Styles ===== */
.cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    width: 100%;
    margin: 0 auto;
}

/* ===== Skeleton Loading ===== */
.skeleton-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.skeleton-card {
    height: 280px;
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.05) 25%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 75%
    );
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius-md);
}

@keyframes skeleton-loading {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

/* ===== Pagination ===== */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 40px 0;
}

.nav-button {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.nav-button.active {
    background: var(--button-gradient);
    color: white;
    box-shadow: var(--button-shadow);
}

/* ===== Flickity Customization ===== */
.flickity-button {
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

.flickity-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.flickity-prev-next-button {
    width: 40px;
    height: 40px;
}

.flickity-page-dots {
    bottom: -30px;
}

.flickity-page-dots .dot {
    background: var(--neon-blue);
    opacity: 0.5;
    width: 10px;
    height: 10px;
    margin: 0 6px;
}

.flickity-page-dots .dot.is-selected {
    background: var(--cosmic-pink);
    opacity: 1;
    transform: scale(1.2);
}

/* Fix for carousel display */
.carousel .skeleton-cards {
    display: none;
}

.carousel .card {
    height: 100%;
    margin-bottom: 10px;
}

/* Additional carousel fixes */
.carousel .cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    width: 100%;
    margin: 0 auto;
}

.carousel-cell {
    width: 100% !important;
    padding: 0 15px;
    box-sizing: border-box;
}

.flickity-viewport {
    overflow: visible;
}

.flickity-slider {
    width: 100%;
}

/* Fix card image container */
.card .img-container {
    width: 100%;
    height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card .img-container img {
    width: 85%;
    height: 85%;
    object-fit: contain;
}

/* ===== Responsive Styles ===== */
@media (max-width: 1200px) {
    .skeleton-cards,
    .cards {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .skeleton-cards,
    .cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .explore-hero {
        padding: 3rem 1.5rem;
    }

    .filters-container {
        flex-direction: column;
    }

    .filter-select {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .skeleton-cards,
    .cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .explore-hero {
        padding: 2rem 1rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .search-container {
        flex-direction: column;
    }

    .search-button {
        width: 100%;
    }

    .card-content {
        padding: 0.5rem;
    }

    .card .text-content h3 {
        font-size: 0.9rem;
    }

    .card .text-content p {
        font-size: 0.8rem;
    }

    /* Carousel adjustments for mobile */
    .carousel-cell {
        padding: 0 10px;
    }

    .carousel .cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .skeleton-cards,
    .cards {
        grid-template-columns: 1fr;
    }

    .explore-hero {
        padding: 1.5rem 1rem;
    }

    .hero-content h1 {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 0.9rem;
    }

    /* Carousel adjustments for small screens */
    .carousel .cards {
        grid-template-columns: 1fr;
    }

    .carousel-cell {
        padding: 0 5px;
    }
}

/* Reduce motion if user prefers */
@media (prefers-reduced-motion: reduce) {
    .explore-hero::before,
    .item:hover .img-container img,
    .item:hover {
        animation: none;
        transition: none;
        transform: none;
    }
}
