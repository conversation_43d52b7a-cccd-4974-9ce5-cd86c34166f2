/**
 * Data models for the music library management system
 */

/**
 * Represents a song in the music library
 */
class Song {
    constructor(data = {}) {
        this.id = data.id || generateId();
        this.title = data.title || '';
        this.artist = data.artist || '';
        this.album = data.album || '';
        this.duration = data.duration || 0; // in seconds
        this.coverUrl = data.coverUrl || '';
        this.audioUrl = data.audioUrl || '';
        this.genre = data.genre || [];
        this.releaseDate = data.releaseDate || '';
        this.isExplicit = data.isExplicit || false;
        this.popularity = data.popularity || 0; // 0-100 scale
    }

    /**
     * Format the song duration as mm:ss
     */
    formattedDuration() {
        const minutes = Math.floor(this.duration / 60);
        const seconds = this.duration % 60;
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
}

/**
 * Represents a playlist in the music library
 */
class Playlist {
    constructor(data = {}) {
        this.id = data.id || generateId();
        this.name = data.name || 'New Playlist';
        this.description = data.description || '';
        this.coverUrl = data.coverUrl || '';
        this.createdBy = data.createdBy || '';
        this.createdAt = data.createdAt || new Date().toISOString();
        this.updatedAt = data.updatedAt || new Date().toISOString();
        this.isPublic = data.isPublic !== undefined ? data.isPublic : true;
        this.songs = data.songs || [];
        this.followers = data.followers || 0;
    }

    /**
     * Add a song to the playlist
     * @param {Song} song - The song to add
     */
    addSong(song) {
        if (!this.songs.some(s => s.id === song.id)) {
            this.songs.push(song);
            this.updatedAt = new Date().toISOString();
        }
    }

    /**
     * Remove a song from the playlist
     * @param {string} songId - The ID of the song to remove
     */
    removeSong(songId) {
        this.songs = this.songs.filter(song => song.id !== songId);
        this.updatedAt = new Date().toISOString();
    }

    /**
     * Get the total duration of the playlist in seconds
     */
    getTotalDuration() {
        return this.songs.reduce((total, song) => total + song.duration, 0);
    }

    /**
     * Format the total duration as hh:mm:ss
     */
    formattedTotalDuration() {
        const totalSeconds = this.getTotalDuration();
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
    }
}

/**
 * Represents a user's music library
 */
class Library {
    constructor(userId) {
        this.userId = userId;
        this.playlists = [];
        this.favorites = [];
        this.recentlyPlayed = [];
        this.queue = [];
        this.downloads = [];
    }

    /**
     * Add a playlist to the library
     * @param {Playlist} playlist - The playlist to add
     */
    addPlaylist(playlist) {
        this.playlists.push(playlist);
    }

    /**
     * Remove a playlist from the library
     * @param {string} playlistId - The ID of the playlist to remove
     */
    removePlaylist(playlistId) {
        this.playlists = this.playlists.filter(playlist => playlist.id !== playlistId);
    }

    /**
     * Add a song to favorites
     * @param {Song} song - The song to add to favorites
     */
    addToFavorites(song) {
        if (!this.favorites.some(s => s.id === song.id)) {
            this.favorites.push(song);
        }
    }

    /**
     * Remove a song from favorites
     * @param {string} songId - The ID of the song to remove from favorites
     */
    removeFromFavorites(songId) {
        this.favorites = this.favorites.filter(song => song.id !== songId);
    }

    /**
     * Add a song to recently played
     * @param {Song} song - The song to add to recently played
     */
    addToRecentlyPlayed(song) {
        // Remove the song if it's already in the list to avoid duplicates
        this.recentlyPlayed = this.recentlyPlayed.filter(s => s.id !== song.id);
        
        // Add the song to the beginning of the list
        this.recentlyPlayed.unshift(song);
        
        // Limit the list to 50 songs
        if (this.recentlyPlayed.length > 50) {
            this.recentlyPlayed = this.recentlyPlayed.slice(0, 50);
        }
    }

    /**
     * Add a song to the playback queue
     * @param {Song} song - The song to add to the queue
     */
    addToQueue(song) {
        this.queue.push(song);
    }

    /**
     * Remove a song from the playback queue
     * @param {number} index - The index of the song to remove
     */
    removeFromQueue(index) {
        if (index >= 0 && index < this.queue.length) {
            this.queue.splice(index, 1);
        }
    }

    /**
     * Clear the playback queue
     */
    clearQueue() {
        this.queue = [];
    }

    /**
     * Add a song to downloads
     * @param {Song} song - The song to add to downloads
     */
    addToDownloads(song) {
        if (!this.downloads.some(s => s.id === song.id)) {
            this.downloads.push(song);
        }
    }

    /**
     * Remove a song from downloads
     * @param {string} songId - The ID of the song to remove from downloads
     */
    removeFromDownloads(songId) {
        this.downloads = this.downloads.filter(song => song.id !== songId);
    }
}

/**
 * Generate a unique ID
 */
function generateId() {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
}

// Export the classes
export { Song, Playlist, Library };
