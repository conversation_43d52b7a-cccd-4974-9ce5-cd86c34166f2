/* CSS Variables */
:root {
    /* Colors */
    --background-color: #0D1117;
    --text-color: #ffffff;
    --electric-violet: #6F00FF;
    --neon-blue: #00E0FF;
    --cosmic-pink: #FF006E;
    --cyber-lime: #A7FF4A;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    --accent-color: var(--cosmic-pink);
    
    /* Gradients */
    --button-gradient: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --header-gradient: linear-gradient(to right, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    
    /* Shadows */
    --button-hover-shadow: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --button-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    --card-shadow: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Spacing */
    --section-gap: 2rem;
    --container-padding: 20px;
    
    /* Border Radius */
    --border-radius-lg: 25px;
    --border-radius-md: 16px;
    --border-radius-sm: 12px;
    
    /* Transitions */
    --transition-speed: 0.3s;
}

/* Navbar Styles */
header {
    background: var(--header-gradient);
    padding: 12px 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: var(--button-shadow);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo img {
    width: 90px;
    height: 90px;
    transition: transform var(--transition-speed);
}

.logo img:hover {
    transform: scale(1.05);
}

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-speed);
}

.menu a:hover,
.menu a[aria-current="page"] {
    color: var(--accent-color);
}

/* User Profile */
.user-profile {
    position: relative;
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all var(--transition-speed) ease;
    filter: brightness(0.95);
}

.profile-icon:hover {
    transform: scale(1.05);
    border-color: rgba(0, 224, 255, 0.3);
    box-shadow: 
        0 0 15px rgba(0, 224, 255, 0.2),
        0 0 30px rgba(0, 224, 255, 0.1);
    filter: brightness(1.1);
}

/* Dropdown Menu */
.dropdown {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: linear-gradient(
        135deg,
        var(--header-gradient-start),
        var(--header-gradient-end)
    );
    border-radius: 12px;
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.2),
        0 0 15px rgba(0, 224, 255, 0.1);
    backdrop-filter: blur(10px);
    transform-origin: top right;
}

/* Change from .dropdown.active to .user-profile:hover .dropdown */
.user-profile:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 8px;
}

.dropdown ul li {
    margin: 2px 0;
}

.dropdown ul li a {
    color: var(--text-color);
    text-decoration: none;
    padding: 10px 16px;
    display: block;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
}

.dropdown ul li a:hover {
    color: var(--neon-blue);
    text-shadow: 
        0 0 8px rgba(0, 224, 255, 0.3),
        0 0 16px rgba(0, 224, 255, 0.2);
    transform: translateX(2px);
}

.dropdown ul li a:hover::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(
        90deg,
        transparent,
        var(--neon-blue),
        transparent
    );
    animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Optional: Add a subtle transition effect when opening dropdown */
.dropdown {
    transform-origin: top right;
    transition: all var(--transition-speed) cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    nav {
        padding: 1rem;
    }
    
    .menu {
        gap: 1rem;
    }
    
    .menu a {
        font-size: 0.9rem;
    }

    .dropdown {
        min-width: 180px;
    }
}

@media (max-width: 480px) {
    .menu {
        display: none;
    }
    
    .mobile-menu-button {
        display: block;
    }
}

/* Base Styles */
body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
    margin: 0;
    padding: 0 var(--container-padding);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Header Styles */
header {
    background: var(--header-gradient);
    padding: 12px 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: var(--button-shadow);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

/* Logo Styles */
.logo img {
    width: 90px;
    height: 90px;
    transition: transform var(--transition-speed);
}

.logo img:hover {
    transform: scale(1.05);
}

/* Navigation Menu */
.menu {
    list-style: none;
    display: flex;
    gap: 3rem;
    margin: 0;
    padding: 0;
}

.menu li a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.125rem;
    position: relative;
    padding: 0.5rem 0;
    transition: all var(--transition-speed) ease;
}

.menu li a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--button-gradient);
    transition: all var(--transition-speed) ease;
    opacity: 0;
}

.menu li a:hover::after,
.menu li a[aria-current="page"]::after {
    width: 100%;
    opacity: 1;
    box-shadow: 0 0 20px var(--neon-blue);
}

.menu li a[aria-current="page"] {
    position: relative;
    color: var(--text-color);
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.1);
    transition: all var(--transition-speed) ease;
    font-weight: 400;
}

.menu li a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(
        90deg,
        var(--neon-blue),
        var(--cosmic-pink),
        var(--neon-blue)
    );
    background-size: 200% 100%;
    animation: shimmer 3s linear infinite;
    border-radius: var(--border-radius-sm);
    box-shadow: 
        0 0 8px var(--neon-blue),
        0 0 15px rgba(0, 224, 255, 0.2);
    transform-origin: center;
    transition: transform var(--transition-speed) ease,
                box-shadow var(--transition-speed) ease;
}

@keyframes shimmer {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

@media (prefers-reduced-motion: reduce) {
    .menu li a[aria-current="page"]::after {
        animation: none;
        background-position: 0 0;
        box-shadow: 0 0 8px var(--neon-blue);
    }
}

/* Button Styles */
.btn,
.button,
button[class*="btn-"],
.cta-button {
    background: var(--button-gradient);
    color: var(--text-color);
    border: none;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    box-shadow: var(--button-shadow); 
}

.btn:hover,
.button:hover,
button[class*="btn-"]:hover,
.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

/* Hero Section */
/*.hero {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    padding: 6rem 1rem;
    overflow: hidden;
}*/

/* Later instance */
/*.hero {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    padding: 6rem 1rem;
    overflow: hidden;
}*/

/* Any media queries related to hero */
@media screen and (max-width: 768px) {
    /*.hero {
        padding: 3rem 1rem;
    }*/
}

@media screen and (max-width: 480px) {
    /*.hero {
        padding: 2rem 1rem;
    }*/
}

.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    width: 100%;
    max-width: 800px;
    padding: 3rem;
    margin: 120px auto 0; /* Changed to auto margins for horizontal centering */
    background: rgba(13, 17, 23, 0.8);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex; /* Added for better content alignment */
    flex-direction: column;
    align-items: center;
}

/* Stars Animation */
.stars {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
    z-index: 2; /* Bring stars forward */
    opacity: 0.8; /* Increase opacity from 0.6 */
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #fff;
    border-radius: 50%;
    animation: twinkle var(--twinkle-duration) ease-in-out infinite;
    opacity: 0;
    box-shadow: 
        0 0 8px #fff,
        0 0 12px #fff,
        0 0 20px var(--neon-blue);
}

.star.shooting {
    width: 3px;
    height: 3px;
    background: linear-gradient(90deg, #fff, transparent);
    box-shadow: 
        0 0 15px #fff,
        0 0 30px var(--neon-blue),
        0 0 45px var(--cosmic-pink);
    animation: shooting-star 2s linear infinite;
}

.star::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: inherit;
    transform: translate(-50%, -50%);
    filter: blur(1px);
    animation: starGlow var(--twinkle-duration) ease-in-out infinite;
}

/* Star Positions and Animations */
.star:nth-child(1) { --twinkle-duration: 3s; top: 10%; left: 10%; }
.star:nth-child(2) { --twinkle-duration: 4s; top: 20%; left: 25%; animation-delay: 0.3s; }
.star:nth-child(3) { --twinkle-duration: 3.5s; top: 15%; left: 60%; animation-delay: 0.7s; }
.star:nth-child(4) { --twinkle-duration: 4.5s; top: 35%; left: 85%; animation-delay: 1.1s; }
.star:nth-child(5) { --twinkle-duration: 3s; top: 45%; left: 15%; animation-delay: 1.4s; }
.star:nth-child(6) { --twinkle-duration: 4s; top: 55%; left: 35%; animation-delay: 1.8s; }
.star:nth-child(7) { --twinkle-duration: 3.5s; top: 65%; left: 75%; animation-delay: 2.1s; }
.star:nth-child(8) { --twinkle-duration: 4.5s; top: 75%; left: 25%; animation-delay: 2.5s; }
.star:nth-child(9) { --twinkle-duration: 3s; top: 85%; left: 85%; animation-delay: 2.8s; }
.star:nth-child(10) { --twinkle-duration: 4s; top: 90%; left: 50%; animation-delay: 3.2s; }

@keyframes twinkle {
    0%, 100% {
        opacity: 0;
        transform: scale(0.3) rotate(0deg);
    }
    25%, 75% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg); /* Increased scale */
    }
    50% {
        opacity: 1;
        transform: scale(1.4) rotate(360deg); /* Increased scale */
    }
}

@keyframes shooting-star {
    0% {
        transform: translate(-100px, -100px) rotate(45deg) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    20%, 80% {
        transform: translate(calc(100vw + 100px), calc(100vh + 100px)) rotate(45deg) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(calc(100vw + 150px), calc(100vh + 150px)) rotate(45deg) scale(0.5);
        opacity: 0;
    }
}

@keyframes starGlow {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(255,255,255,0);
    }
    50% {
        box-shadow: 0 0 30px 4px rgba(255,255,255,0.4); /* Increased glow */
    }
}

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(0, 224, 255, 0.3);
}

.hero-content p {
    font-size: clamp(1rem, 2vw, 1.2rem);
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

/* Media Queries for Hero Section */
@media screen and (max-width: 768px) {
    .hero {
        padding: 3rem 1rem;
    }

    .hero-content {
        padding: 2rem;
    }

    .star {
        transform: scale(0.8);
    }
}

@media screen and (max-width: 480px) {
    .hero {
        padding: 2rem 1rem;
    }

    .hero-content {
        padding: 1.5rem;
    }

    .star {
        transform: scale(0.6);
    }
}

/* Quick Access Section */
.quick-access {
    margin: -10px auto 0;
    text-align: center;
    max-width: 1200px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.quick-access h2,
.section h2,
.trending h2,
.recommendations h2 {
    font-size: clamp(2.2rem, 4vw, 2.8rem);  /* Increased from previous size */
    font-weight: 700;
    margin-bottom: 2.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    letter-spacing: 0.5px;
    text-shadow: 0 0 20px rgba(0, 224, 255, 0.3);
    position: relative;
    padding-bottom: 0.5rem;
}

.quick-access h2::after,
.section h2::after,
.trending h2::after,
.recommendations h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 180px; /* Increased from 80px */
    height: 2px; /* Slightly thinner */
    background: linear-gradient(
        90deg,
        transparent,
        rgba(0, 224, 255, 0.4) 20%,
        rgba(255, 0, 110, 0.4) 80%,
        transparent
    );
    border-radius: 2px;
    box-shadow: 
        0 0 10px rgba(0, 224, 255, 0.2),
        0 0 20px rgba(255, 0, 110, 0.1);
}

.quick-access h2 {
    margin-bottom: 2.5rem;
}

.quick-links {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 2rem;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.quick-link {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--text-color);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 110px;
    max-width: 150px;
    aspect-ratio: 1;
    position: relative;
    overflow: hidden;
}

.quick-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        rgba(var(--neon-blue-rgb), 0.1),
        rgba(var(--cosmic-pink-rgb), 0.1)
    );
    opacity: 0;
    transition: opacity 0.3s ease;
}

.quick-link::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 70%
    );
    opacity: 0;
    transform: scale(0.5);
    transition: transform 0.6s ease, opacity 0.6s ease;
}

.quick-link:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 10px 20px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(var(--neon-blue-rgb), 0.2),
        0 0 20px rgba(var(--cosmic-pink-rgb), 0.2);
}

.quick-link:hover::before {
    opacity: 1;
}

.quick-link:hover::after {
    opacity: 1;
    transform: scale(1);
}

.quick-link img {
    width: 45px;
    height: 45px;
    object-fit: contain;
    margin-bottom: 1rem;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.quick-link:hover img {
    transform: scale(1.1) translateY(-5px);
}

.quick-link span {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
    position: relative;
    z-index: 1;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                color 0.3s ease;
}

.quick-link:hover span {
    transform: translateY(-3px);
    color: var(--neon-blue);
    text-shadow: 0 0 8px rgba(var(--neon-blue-rgb), 0.3);
}

/* Add a subtle pulse animation for the hover state */
@keyframes quickLinkPulse {
    0% {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2),
                   0 0 20px rgba(var(--neon-blue-rgb), 0.2);
    }
    50% {
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3),
                   0 0 30px rgba(var(--neon-blue-rgb), 0.3),
                   0 0 20px rgba(var(--cosmic-pink-rgb), 0.2);
    }
    100% {
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2),
                   0 0 20px rgba(var(--neon-blue-rgb), 0.2);
    }
}

.quick-link:hover {
    animation: quickLinkPulse 2s infinite;
}

/* Add a smooth transition back to normal state */
.quick-link:active {
    transform: translateY(-4px);
    transition: transform 0.1s;
}

/* Media Queries for Quick Access */
@media screen and (max-width: 768px) {
    .quick-access {
        margin: -8px auto 0;
        padding: 1.5rem;
    }

    .quick-links {
        gap: 1.5rem;
    }
    
    .quick-link {
        min-width: 100px;
        max-width: 120px;
        padding: 1.25rem;
    }
    
    .quick-link img {
        width: 40px;
        height: 40px;
    }
}

@media screen and (max-width: 480px) {
    .quick-access {
        margin: -5px auto 0;
        padding: 1rem;
    }

    .quick-links {
        gap: 1rem;
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .quick-link {
        min-width: 90px;
        max-width: 100px;
        padding: 1rem;
        flex: 0 1 calc(33.333% - 1rem);
    }
    
    .quick-link img {
        width: 35px;
        height: 35px;
        margin-bottom: 0.75rem;
    }
    
    .quick-link span {
        font-size: 0.8rem;
    }
}

/* Card Container */
.cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 1.5rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Base Card Style */
.card {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.1); /* Light gray with transparency */
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.card:hover {
    transform: translateY(-5px);
    background-color: rgba(255, 255, 255, 0.15); /* Slightly more visible on hover */
    border-color: var(--neon-blue);
    box-shadow: var(--card-shadow);
}

/* Card Image Container */
.card .img-container {
    position: relative;
    overflow: hidden;
}

.card .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover .overlay {
    opacity: 1;
}

.card-content {
    padding: 1rem;
    text-align: center;
}

.card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Card Content */
.card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0.8rem 0;
    color: var(--text-color);
}

.card p {
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 1rem;
}

/* Card Button */
.card button {
    width: 100%;
    padding: 0.8rem 1.5rem;
    margin-top: auto;
    background: var(--button-gradient);
    color: var(--text-color);
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    cursor: pointer;
}

/* Media Queries */
@media screen and (max-width: 768px) {
    .cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 1rem;
    }
}

@media screen and (max-width: 480px) {
    .cards {
        grid-template-columns: 1fr;
    }
}

/* Section Headers */
.section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-align: center;
}

/* Responsive Adjustments */
@media screen and (max-width: 1200px) {
    .cards {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media screen and (max-width: 900px) {
    .cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 600px) {
    .cards {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .card {
        padding: 1rem;
    }
    
    .section h2 {
        font-size: 1.5rem;
    }
}

/* Common button styles */
.button,
.play-button,
.section .view-all,
.playlist-controls button,
.hero-content .cta-button {
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    box-shadow: var(--button-shadow);
}

/* Common hover effects */
.button:hover,
.play-button:hover,
.section .view-all:hover,
.playlist-controls button:hover,
.hero-content .cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 8px 25px rgba(0, 224, 255, 0.3),
        0 8px 25px rgba(255, 0, 110, 0.3);
}

/* Common active effects */
.button:active,
.play-button:active,
.section .view-all:active,
.playlist-controls button:active,
.hero-content .cta-button:active {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Shine effect */
.button::before,
.play-button::before,
.section .view-all::before,
.playlist-controls button::before,
.hero-content .cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--button-shine);
    transition: 0.5s;
}

/* Shine animation on hover */
.button:hover::before,
.play-button:hover::before,
.section .view-all:hover::before,
.playlist-controls button:hover::before,
.hero-content .cta-button:hover::before {
    left: 100%;
}

/* Button size variations */
.play-button {
    padding: 12px 25px;
    font-size: 1rem;
}

.section .view-all {
    padding: 10px 20px;
    font-size: 0.9rem;
}

.playlist-controls button {
    padding: 10px 20px;
    font-size: 0.9rem;
    margin: 0 5px;
}

/* Star Animation */
.stars {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
    opacity: 0.6;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #fff;
    border-radius: 50%;
    animation: shooting-star 1.5s linear infinite;
    opacity: 0;
}

@keyframes shooting-star {
    0% {
        transform: translate(-100%, -100%);
        opacity: 1;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translate(200%, 200%);
        opacity: 0;
    }
}

/* Stagger star animations */
.star:nth-child(1) { animation-delay: 0s; }
.star:nth-child(2) { animation-delay: 0.15s; }
.star:nth-child(3) { animation-delay: 0.3s; }
.star:nth-child(4) { animation-delay: 0.45s; }
.star:nth-child(5) { animation-delay: 0.6s; }
.star:nth-child(6) { animation-delay: 0.75s; }
.star:nth-child(7) { animation-delay: 0.9s; }
.star:nth-child(8) { animation-delay: 1.05s; }
.star:nth-child(9) { animation-delay: 1.2s; }
.star:nth-child(10) { animation-delay: 1.35s; }

@media screen and (max-width: 768px) {
    body {
        padding: 0 15px;
    }
    
    header {
        padding: 10px 15px;
    }
    
    .menu {
        gap: 2rem;
    }
    
    .quick-links {
        gap: 1rem;
    }
    }

@media screen and (max-width: 480px) {
    body {
        padding: 0 10px;
    }
    
    .menu {
        gap: 1rem;
    }
    
    .quick-link {
        width: 80px;
    }
}

/* Global Button Reset */
button {
    font-family: inherit;
    margin: 0;
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;
}

/* Global Button Base Styles */
.btn,
.button,
button[class*="btn-"],
.play-button,
.view-all,
.cta-button,
.action-button,
.control-button,
.playlist-controls button {
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    box-shadow: var(--button-shadow);
}

/* Button Sizes */
.btn-large,
.cta-button,
.hero-content .button {
    padding: var(--button-large-padding);
    font-size: var(--button-large-font);
}

.btn-medium,
.button,
.play-button {
    padding: var(--button-medium-padding);
    font-size: var(--button-medium-font);
}

.btn-small,
.view-all,
.control-button,
.playlist-controls button {
    padding: var(--button-small-padding);
    font-size: var(--button-small-font);
}

/* Button Hover Effects */
.btn:hover,
.button:hover,
button[class*="btn-"]:hover,
.play-button:hover,
.view-all:hover,
.cta-button:hover,
.action-button:hover,
.control-button:hover,
.playlist-controls button:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

/* Button Active Effects */
.btn:active,
.button:active,
button[class*="btn-"]:active,
.play-button:active,
.view-all:active,
.cta-button:active,
.action-button:active,
.control-button:active,
.playlist-controls button:active {
    transform: translateY(-1px);
    box-shadow: var(--button-shadow);
}

/* Icon Spacing */
button i,
.btn i,
.button i {
    margin-right: 8px;
}

/* Navigation Menu Styles */
.menu {
    list-style: none;
    display: flex;
    letter-spacing: 1.2px;
    margin: 0;
    padding: 0;
    gap: 3rem;
}

.menu li {
    margin: 0;
}

.menu li a {
    color: #fff;
    text-decoration: none;
    font-size: 1.125rem;
    position: relative;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.menu li a:hover {
    color: var(--text-color);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu li a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--button-gradient);
    transition: all 0.3s ease;
    opacity: 0;
}

.menu li a:hover::after,
.menu li a[aria-current="page"]::after {
    width: 100%;
    opacity: 1;
    box-shadow: 0 0 20px var(--neon-blue);
}

.menu li a[aria-current="page"] {
    color: var(--text-color); /* Changed from var(--neon-blue) to var(--text-color) */
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

/* Header Styles */
header {
    background: linear-gradient(
        to right,
        var(--header-gradient-start) 0%,
        var(--header-gradient-end) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
        z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

/* Logo Styles */
.logo img {
    width: 90px;
    height: 90px;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* User Profile Styles */
.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.user-profile img {
    width: 36px; /* Reduced from 40px */
    height: 36px; /* Reduced from 40px */
    border-radius: 50%;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.user-profile:hover img {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-profile:hover .dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown ul {
    list-style: none;
    display: flex;
    gap: 3rem;
        margin: 0;
    padding: 8px 0;
}

.dropdown ul li a {
    color: #fff;
    text-decoration: none;
    padding: 10px 20px;
    display: block;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.dropdown ul li a:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--neon-blue);
}

/* Container Styles */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Hero Section */
/*.hero {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    padding: 6rem 1rem;
    overflow: hidden;
}*/

/* Later instance */
/*.hero {
    position: relative;
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    padding: 6rem 1rem;
    overflow: hidden;
}*/

/* Any media queries related to hero */
@media screen and (max-width: 768px) {
    /*.hero {
        padding: 3rem 1rem;
    }*/
}

@media screen and (max-width: 480px) {
    /*.hero {
        padding: 2rem 1rem;
    }*/
}

/* Hero Section */
.hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    width: 100%;
    max-width: 800px;
    padding: 3rem;
    margin: 120px auto 0; /* Changed to auto margins for horizontal centering */
    background: rgba(13, 17, 23, 0.8);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex; /* Added for better content alignment */
    flex-direction: column;
    align-items: center;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .hero-content {
        margin: 100px auto 0;
        padding: 2rem;
        width: 90%; /* Added width control for smaller screens */
    }
}

@media screen and (max-width: 480px) {
    .hero-content {
        margin: 80px auto 0;
        padding: 1.5rem;
        width: 95%; /* Slightly wider on mobile */
    }
}

.hero-content h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.hero-content p {
    font-size: clamp(1.1rem, 2vw, 1.5rem);
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-content .cta-button {
    display: inline-block;
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    color: white;
    background: var(--button-gradient);
    border-radius: 30px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    box-shadow: var(--button-hover-shadow);
}

.hero-content .cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 224, 255, 0.3),
                0 8px 25px rgba(255, 0, 110, 0.3);
}

.hero-content .cta-button:active {
    transform: translateY(-1px);
}

/* Quick Access Section */
.quick-access {
    margin: 0 auto; /* Reduced from 1rem to 0 to bring it closer to hero section */
    text-align: center;
    max-width: 1200px;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.quick-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    padding: 1rem 0;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-color);
    transition: transform 0.3s;
    padding: 1rem;
    border-radius: var(--explore-card-radius);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 100px;
}

.quick-link img {
    width: 40px;
    height: 40px;
    margin-bottom: 0.5rem;
}

.quick-link span {
    font-size: 0.9rem;
    font-weight: 500;
}

/* Items Grid Layout */
.items {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.08) 100%
    );
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.item:hover {
    transform: translateY(-8px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 
        0 10px 20px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(var(--neon-blue-rgb), 0.2);
}

.item img {
    width: 140px;
    height: 140px;
    object-fit: cover;
    border-radius: 12px;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.item:hover img {
    transform: scale(1.05);
}

.item h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0.5rem 0;
    color: var(--text-color);
    letter-spacing: 0.5px;
}

.item p {
    font-size: 0.9rem;
    color: var(--text-color-secondary);
    margin-bottom: 1rem;
    opacity: 0.8;
}

/* Common button styles */
.button,
.play-button,
.section .view-all,
.playlist-controls button,
.hero-content .cta-button {
    background: var(--button-gradient);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    box-shadow: var(--button-shadow);
}

/* Common hover effects */
.button:hover,
.play-button:hover,
.section .view-all:hover,
.playlist-controls button:hover,
.hero-content .cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 8px 25px rgba(0, 224, 255, 0.3),
        0 8px 25px rgba(255, 0, 110, 0.3);
}

/* Common active effects */
.button:active,
.play-button:active,
.section .view-all:active,
.playlist-controls button:active,
.hero-content .cta-button:active {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Shine effect */
.button::before,
.play-button::before,
.section .view-all::before,
.playlist-controls button::before,
.hero-content .cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--button-shine);
    transition: 0.5s;
}

/* Shine animation on hover */
.button:hover::before,
.play-button:hover::before,
.section .view-all:hover::before,
.playlist-controls button:hover::before,
.hero-content .cta-button:hover::before {
    left: 100%;
}

/* Button size variations */
.play-button {
    padding: 12px 25px;
    font-size: 1rem;
}

.section .view-all {
    padding: 10px 20px;
    font-size: 0.9rem;
}

.playlist-controls button {
    padding: 10px 20px;
    font-size: 0.9rem;
    margin: 0 5px;
}

/* Star Animation */
.stars {
    position: absolute;
    inset: 0;
    overflow: hidden;
    pointer-events: none;
    opacity: 0.6;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: #fff;
    border-radius: 50%;
    animation: shooting-star 1.5s linear infinite;
    opacity: 0;
}

@keyframes shooting-star {
    0% {
        transform: translate(-100%, -100%);
        opacity: 1;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translate(200%, 200%);
        opacity: 0;
    }
}

/* Stagger star animations */
.star:nth-child(1) { animation-delay: 0s; }
.star:nth-child(2) { animation-delay: 0.15s; }
.star:nth-child(3) { animation-delay: 0.3s; }
.star:nth-child(4) { animation-delay: 0.45s; }
.star:nth-child(5) { animation-delay: 0.6s; }
.star:nth-child(6) { animation-delay: 0.75s; }
.star:nth-child(7) { animation-delay: 0.9s; }
.star:nth-child(8) { animation-delay: 1.05s; }
.star:nth-child(9) { animation-delay: 1.2s; }
.star:nth-child(10) { animation-delay: 1.35s; }

@media screen and (max-width: 768px) {
    body {
        padding: 0 15px;
    }
    
    header {
        padding: 10px 15px;
    }
    
    .menu {
        gap: 2rem;
    }
    
    .quick-links {
        gap: 1rem;
    }
    }

@media screen and (max-width: 480px) {
    body {
        padding: 0 10px;
    }
    
    .menu {
        gap: 1rem;
    }
    
    .quick-link {
        width: 80px;
    }
}

/* Recommendations Section */
.recommendations .items {
    display: flex;
    overflow-x: auto;
    padding-bottom: 20px;
}

.recommendations .item {
    flex: 0 0 auto;
    width: 150px;
    margin-right: 20px;
}

.recommendations .item img {
    width: 70%;
    height: 100px;
    object-fit: contain;
    border-radius: 5px;
}

.recommendations .item h3 {
    font-size: 14px;
    margin: 10px 0;
}

.recommendations .item button {
    width: 100%;
    padding: 6px;
    font-size: 12px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

/* Footer */
footer {
    background-color: #2c2c2e;
    color: #fff;
    text-align: center;
    padding: 20px;
    margin-top: 60px;
    position: relative;
    width: 100%;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
}

footer p {
    margin: 0;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 120px;  /* Positioned above music player */
    right: 30px;
    background: linear-gradient(90deg, var(--neon-blue) 0%, var(--cosmic-pink) 100%);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 9999;
}

.back-to-top:hover {
    transform: translateY(-3px);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

/* Search Section Styles */
.search-section {
    margin-top: 40px;
}

.search-container {
    max-width: 800px;
    margin: 0 auto 20px;
    padding: 0 20px;
}

.search-input-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    opacity: 0.7;
}

.search-bar {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
    font-size: 16px;
    transition: background-color 0.3s ease;
}

.search-bar:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px var(--accent-color);
}

.search-bar::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.filters-container {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 30px;
}

.filter-select {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.filter-select:hover {
    background: rgba(255, 255, 255, 0.15);
}

.filter-select:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.filter-select option {
    background: var(--background-color);
    color: var(--text-color);
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 40px 0;
}

.nav-button {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.nav-button.active {
    background: var(--accent-color);
}

@media (max-width: 768px) {
    .search-container {
        padding: 0 15px;
    }
    
    .filters-container {
        flex-direction: column;
        padding: 0 15px;
    }
    
    .filter-select {
        width: 100%;
    }
    
    .pagination {
        flex-wrap: wrap;
    }
}

/* Responsive Design */
@media screen and (max-width: 1200px) {
    .section .item {
        min-width: 280px;
    }
}

@media screen and (max-width: 768px) {
    .section .items {
        grid-template-columns: 1fr;
    }
    
    .section .item {
        min-width: 250px;
    }
    
    .container {
        width: 95%;
        padding: 10px;
    }
    
    /* Mobile-specific improvements */
    .menu a, 
    button,
    .item button,
    .back-to-top {
        min-height: 44px;  /* iOS minimum touch target */
        min-width: 44px;
        padding: 12px 20px;
}

    /* Improve mobile navigation */
    /* Mobile Responsive */
    @media (max-width: 768px) {
    .menu {
        display: none;
    }
    
    .menu.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--background-color);
        padding: 1rem;
    }
}

    .menu a {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 12px 16px;
    }

    /* Optimize item layout for touch */
    .item {
        padding: 16px;
        margin: 12px;
        border-radius: 12px;  /* Slightly larger for better touch feel */
    }

    /* Ensure sufficient spacing between interactive elements */
    .user-profile {
        padding: 8px;
    }

    .dropdown {
        padding: 12px 0;
    }

    .dropdown a {
        padding: 12px 16px;
        display: block;
    }
}

@media screen and (max-width: 480px) {
    .section .item {
        min-width: 220px;
    }
    
    .section h2 {
        font-size: 32px;
    }
}

/* Media Query for Mobile Viewport */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
        margin-left: auto;
        margin-right: auto;
    }
}

button {
    transition: background-color var(--transition-speed), transform var(--transition-speed);
}

button:hover {
    background-color: var(--button-gradient-end);
    transform: scale(1.05);
}

/* Section Placeholder Styling */
.placeholder {
    background: #e0e0e0;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 200px;
    border-radius: 8px;
}

.placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.4) 50%, rgba(255,255,255,0) 100%);
    animation: shimmer 1.5s infinite;
}

/* Library Page Specific Styles */
.library-header {
    text-align: center;
    margin-bottom: 40px;
}

.library-header h1 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: var(--text-color);
    font-family: "Plus Jakarta Sans", sans-serif;
}

.library-header .search-container {
    max-width: 600px;
    margin: 0 auto;
}

.playlists-section .img-container,
.favorite-songs .img-container,
.recently-played .img-container {
    width: 160px;
    height: 160px;
    margin: 0 auto;
    border-radius: 10px;
    overflow: hidden;
}

/* Carousel Styles */
.carousel-container {
    margin: 2rem 0;
    padding: 1rem 0;
}

.carousel-cell {
    width: 300px;
    margin-right: 20px;
    border-radius: 12px;
}

.recommendation-card {
    background: var(--card-bg);
    padding: 1rem;
    border-radius: 12px;
    text-align: center;
    transition: transform 0.3s ease;
}

.recommendation-card:hover {
    transform: translateY(-5px);
}

.recommendation-card .img-container {
    width: 100%;
    aspect-ratio: 1;
    margin-bottom: 1rem;
    overflow: hidden;
    border-radius: 8px;
}

.recommendation-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recommendation-card h3 {
    margin: 0.5rem 0;
    font-size: 1.1rem;
    color: var(--text-primary);
}

/* Flickity Customization */
.flickity-button {
    background: var(--button-gradient-start);
    color: white;
}

.flickity-button:hover {
    background: var(--button-gradient-end);
}

.flickity-prev-next-button {
    width: 40px;
    height: 40px;
}

.flickity-page-dots .dot {
    background: var(--button-gradient-start);
    opacity: 0.5;
}

.flickity-page-dots .dot.is-selected {
    background: var(--button-gradient-end);
    opacity: 1;
}

/* Add prefers-reduced-motion media query for accessibility */
@media (prefers-reduced-motion: reduce) {
    .menu li a[aria-current="page"]::after {
        animation: none;
        background-position: 0 0;
    }
}

/* Add the animation keyframes */
@keyframes navShimmer {
    0% {
        background-position: 200% 0;
        box-shadow: 0 0 10px var(--neon-blue),
                   0 0 20px rgba(0, 224, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 15px var(--neon-blue),
                   0 0 25px rgba(0, 224, 255, 0.4);
    }
    100% {
        background-position: -200% 0;
        box-shadow: 0 0 10px var(--neon-blue),
                   0 0 20px rgba(0, 224, 255, 0.3);
    }
}

/* Add prefers-reduced-motion media query for accessibility */
@media (prefers-reduced-motion: reduce) {
    .menu li a[aria-current="page"]::after {
        animation: none;
        background-position: 0 0;
    }
}

/* Add the animation keyframes */
@keyframes navShimmer {
    0% {
        background-position: 200% 0;
        box-shadow: 0 0 10px var(--neon-blue),
                   0 0 20px rgba(0, 224, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 15px var(--neon-blue),
                   0 0 25px rgba(0, 224, 255, 0.4);
    }
    100% {
        background-position: -200% 0;
        box-shadow: 0 0 10px var(--neon-blue),
                   0 0 20px rgba(0, 224, 255, 0.3);
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
