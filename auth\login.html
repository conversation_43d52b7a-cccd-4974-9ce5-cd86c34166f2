<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Banshee Music App</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <div class="auth-logo">
            <img src="../imgs/logo-B.png" alt="Banshee Music Logo">
        </div>
        
        <div class="auth-card">
            <h1>Welcome Back</h1>
            <p class="auth-subtitle">Login to your Banshee Music account</p>
            
            <div id="login-error" class="auth-error"></div>
            
            <form id="login-form" class="auth-form">
                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-with-icon">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-with-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="toggle-password" aria-label="Toggle password visibility">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-options">
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">Remember me</label>
                    </div>
                    <a href="reset-password.html" class="forgot-password">Forgot password?</a>
                </div>
                
                <button type="submit" class="auth-button">Login</button>
            </form>
            
            <div class="auth-divider">
                <span>OR</span>
            </div>
            
            <div class="social-login">
                <button type="button" class="social-button google">
                    <i class="fab fa-google"></i>
                    <span>Continue with Google</span>
                </button>
                <button type="button" class="social-button facebook">
                    <i class="fab fa-facebook-f"></i>
                    <span>Continue with Facebook</span>
                </button>
                <button type="button" class="social-button apple">
                    <i class="fab fa-apple"></i>
                    <span>Continue with Apple</span>
                </button>
            </div>
            
            <p class="auth-redirect">
                Don't have an account? <a href="register.html">Sign up</a>
            </p>
        </div>
    </div>
    
    <script src="auth.js"></script>
</body>
</html>
