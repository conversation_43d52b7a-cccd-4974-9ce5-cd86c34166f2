/**
 * Simple login functionality for Banshee Music App
 */

// Initialize demo users in localStorage
function initializeDemoUsers() {
    // Check if we already have users in localStorage
    const existingUsers = localStorage.getItem('banshee_users');
    if (!existingUsers) {
        // Create demo users
        const demoUsers = [
            {
                id: 1,
                username: 'admin',
                password: 'password',
                name: 'Admin User',
                email: '<EMAIL>',
                role: 'admin'
            },
            {
                id: 2,
                username: 'user',
                password: 'password',
                name: 'Regular User',
                email: '<EMAIL>',
                role: 'user'
            },
            {
                id: 3,
                username: 'demo',
                password: 'demo123',
                name: 'Demo User',
                email: '<EMAIL>',
                role: 'user'
            }
        ];
        
        // Save to localStorage
        localStorage.setItem('banshee_users', JSON.stringify(demoUsers));
        console.log('Demo users initialized');
    }
}

// Show demo credentials on the page
function showDemoCredentials() {
    const loginForm = document.getElementById('loginForm');
    if (!loginForm) return;
    
    const demoBox = document.createElement('div');
    demoBox.className = 'demo-credentials';
    demoBox.style.cssText = `
        background: rgba(0, 224, 255, 0.1);
        border: 1px solid rgba(0, 224, 255, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    `;
    
    demoBox.innerHTML = `
        <h3 style="margin-top: 0; margin-bottom: 10px; color: #00E0FF; font-size: 1.2rem;">Demo Credentials</h3>
        <p style="margin: 5px 0; font-size: 0.9rem;"><strong>Username:</strong> demo</p>
        <p style="margin: 5px 0; font-size: 0.9rem;"><strong>Password:</strong> demo123</p>
        <p style="font-size: 0.8rem; color: rgba(255, 255, 255, 0.6); margin-top: 10px; font-style: italic;">Click to autofill</p>
    `;
    
    // Add click event to autofill credentials
    demoBox.addEventListener('click', () => {
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        
        if (usernameInput) usernameInput.value = 'demo';
        if (passwordInput) passwordInput.value = 'demo123';
    });
    
    // Insert before the form
    loginForm.parentNode.insertBefore(demoBox, loginForm);
}

// Show message to the user
function showMessage(message, isError = false) {
    const messageContainer = document.getElementById('message');
    if (!messageContainer) return;
    
    messageContainer.textContent = message;
    messageContainer.className = isError ? 'error' : 'success';
    messageContainer.style.display = 'block';
}

// Handle login form submission
function handleLogin(event) {
    event.preventDefault();
    
    // Get form values
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('remember')?.checked || false;
    
    // Show loading state
    const submitButton = document.querySelector('button[type="submit"]');
    if (submitButton) {
        const originalText = submitButton.textContent;
        submitButton.textContent = 'Logging in...';
        submitButton.disabled = true;
        
        // Reset after 2 seconds
        setTimeout(() => {
            submitButton.textContent = originalText;
            submitButton.disabled = false;
        }, 2000);
    }
    
    // Simulate API call
    setTimeout(() => {
        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('banshee_users') || '[]');
        
        // Find user with matching credentials
        const user = users.find(u => u.username === username && u.password === password);
        
        if (user) {
            // Generate a token
            const token = 'mock-jwt-token-' + Math.random().toString(36).substring(2);
            
            // Store token
            if (rememberMe) {
                localStorage.setItem('token', token);
            } else {
                sessionStorage.setItem('token', token);
            }
            
            // Store user data
            if (rememberMe) {
                localStorage.setItem('user', JSON.stringify(user));
            } else {
                sessionStorage.setItem('user', JSON.stringify(user));
            }
            
            // Show success message
            showMessage(`Welcome back, ${user.name}! Redirecting...`);
            
            // Redirect to home page
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        } else {
            // Show error message
            showMessage('Invalid username or password. Please try again.', true);
        }
    }, 1000);
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize demo users
    initializeDemoUsers();
    
    // Show demo credentials
    showDemoCredentials();
    
    // Add message container if it doesn't exist
    if (!document.getElementById('message')) {
        const messageContainer = document.createElement('div');
        messageContainer.id = 'message';
        messageContainer.style.cssText = `
            display: none;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 14px;
        `;
        
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.parentNode.insertBefore(messageContainer, loginForm);
        }
    }
    
    // Add event listener to login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Check if user is already logged in
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');
    const user = localStorage.getItem('user') || sessionStorage.getItem('user');
    
    if (token && user) {
        // Redirect to home page
        window.location.href = 'index.html';
    }
});
